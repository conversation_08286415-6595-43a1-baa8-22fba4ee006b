import Common from "../../index.js";
import ExperienceBuilderViews from "../../views/experienceBuilderViews.js";
import { Transition } from "../../async/asyncTransition.js";
import MessageHandler from "../../routing/messageHandler.js";
import { experienceTypes } from "../../store/constants/experiences/constants.js";
// Components
import App from "../app.js";
import MainLoader from "./mainLoader.js";
import NewExperience from "./newExperience.js";
import BarInitial from "./barInitial.js";
import EditBar from "./editExperience/editBar.js";

// Store
import { Store } from "../../store/store.js";
import Actions from "../../store/constants/actions.js"
import { sameUrls } from "../../generic-utils/urlUtils.js";
import { getResponsiveWindow } from "../../templateEditor/element/utils.js";

const MODES = Object.freeze({
    LOADING: "LOADING",
    INITIAL: "INITIAL",
    NEW_EXPERIENCE: "NEW_EXPERIENCE",
    EDIT_EXPERIENCE: "EDIT_EXPERIENCE"
});

const DISPLAY_MODES = Object.freeze({
    SHOWN: "SHOWN",
    HIDDEN: "HIDDEN"
});

const BAR_TOGGLE_MODE = {
    HIDE: "Hide",
    SHOW: "Show",
}

const AppBar = (props = {}) => {
    /* COMPONENT DEFS */
    const componentEl = ExperienceBuilderViews.getViewElement("experience-builder-bar");
    const hideShowBtn = componentEl.querySelector("#hide-btn");
    const barContainer = componentEl.querySelector("#bar-container");

    const initialState = {
        display: DISPLAY_MODES.SHOWN,
        mode: MODES.LOADING,
        ...props
    }

    const appState = Store.getComponentState(App);

    const reducerActions = [
        Actions.SHOW_LOADING_SCREEN,
        Actions.SHOW_BAR_MESSAGE,
        Actions.HIDE_BAR_MESSAGE,
        Actions.HIDE_BAR,
        Actions.SHOW_BAR,
        Actions.SET_BAR_TO_INITIAL,
        Actions.SETUP_NEW_EXPERIENCE,
        Actions.EDIT_EXPERIENCE,
        Actions.SHOW_CURRENT_ACTIVE_EXPERIENCE,
        Actions.PREVIEW_EXPERIENCE,
        Actions.END_PREVIEW,
        Actions.LOCALES_VIEW,
        Actions.LOCK_BAR,
        Actions.UNLOCK_BAR,
        Actions.SET_ACTIVE_LOCALE,
        Actions.SUGGEST_PAGECHANGE_SETTINGS,
        Actions.SUGGEST_PAGE_SETTINGS,
        Actions.RESET_SUGGESTED_PAGE
    ]

    const reducer = (action, props = {}) => {
        switch (action) {
            case Actions.SHOW_LOADING_SCREEN:
                return { mode: MODES.LOADING }

            case Actions.SHOW_BAR:
                return { display: DISPLAY_MODES.SHOWN }

            case Actions.HIDE_BAR:
                return { display: DISPLAY_MODES.HIDDEN }

            case Actions.SET_BAR_TO_INITIAL:
                return { mode: MODES.INITIAL }

            case Actions.SETUP_NEW_EXPERIENCE:
                return { mode: MODES.NEW_EXPERIENCE }

            case Actions.EDIT_EXPERIENCE:
            case Actions.SHOW_CURRENT_ACTIVE_EXPERIENCE:
                return { mode: MODES.EDIT_EXPERIENCE }
        }
        return {};
    }

    const reducerEffect = async (action, payload = {}) => {
        switch (action) {
            case Actions.SUGGEST_PAGE_SETTINGS:
                Common.suggestedPageConfiguration = suggestPageSettings({
                    build_url: payload.pathname,
                    host: payload.host
                }, (resolve) => {
                    setTimeout(() => {
                        resolve(null)
                    }, 3000)
                });
                break;
            case Actions.SUGGEST_PAGECHANGE_SETTINGS:
                Common.suggestedPage = {
                    promise: suggestPageSettings({
                        build_url: payload.pathname,
                        host: payload.host
                    }, (resolve) => {
                        setTimeout(() => {
                            resolve(null)
                        }, 3000)
                    }),
                    active: 1,
                    url: payload.pathname
                }
                break;
            case Actions.RESET_SUGGESTED_PAGE:
                Common.suggestedPage = {
                    active: 0,
                    promise: new Promise((resolve) => {
                        resolve();
                    }),
                }
                break;
            case Actions.SHOW_BAR:
                showBar();
                break;
            case Actions.HIDE_BAR:
                hideBar();
                break;
            case Actions.SHOW_BAR_MESSAGE:
                showBarMessage(payload.severity, payload.message, payload.dismissable, payload.icon);
                break;
            case Actions.HIDE_BAR_MESSAGE:
                cleanBarMessage();
                break;
            case Actions.SET_BAR_TO_INITIAL:
            case Actions.EDIT_EXPERIENCE:
            case Actions.SHOW_CURRENT_ACTIVE_EXPERIENCE:
            case Actions.SHOW_LOADING_SCREEN:
                await Transition(barContainer, "opacity", 0, true);
                render();
                break;

            case Actions.SETUP_NEW_EXPERIENCE:
                await Transition(barContainer, "opacity", 0, true);
                Store.publishAction(Actions.CLEAR_WINDOW_COMPONENTS);
                Store.publishAction(Actions.CLOSE_FEATURE_TAG_HEATMAP);
                Store.publishAction(Actions.SET_ACTIVE_CONTENT_INDEX, {
                    active_content_index: 0,
                })
                await MessageHandler.setBackgroundState({
                    appBarMode: MODES.NEW_EXPERIENCE
                });

                if(Common.backgroundState.isResponsiveModeActive) return Store.publishAction(Actions.RESPONSIVE_MODE_OFF);

                render();
                break;

            case Actions.PREVIEW_EXPERIENCE:
                componentEl.style.opacity = 0;
                componentEl.style.visibility = "hidden";
                break;

            case Actions.END_PREVIEW:
                const type = appState.experience?.settings.type || null;

                if (type !== experienceTypes.FLOW || payload.remove_controller) {
                    componentEl.style.opacity = "";
                    componentEl.style.visibility = "";
                    if (!sameUrls(getResponsiveWindow().location.href, (Common.backgroundState.last_active_url || appState.experience.settings.build_url))) {
                        getResponsiveWindow().location.href = Common.backgroundState.last_active_url || appState.experience.settings.build_url
                    }
                }
                break;

            case Actions.LOCALES_VIEW:
                Store.publishAction(Actions.HIDE_LOCALIZATION_SETTINGS);
                render();
                Store.publishAction(Actions.SHOW_FLOW_OVERFLOW_ARROWS);
                break;

            case Actions.SET_ACTIVE_LOCALE:
                render();
                break;

            case Actions.LOCK_BAR:
                if (payload.hide) {
                    await Transition(componentEl, "opacity", 0, false);
                    componentEl.style.visibility = "hidden";
                    componentEl.style.pointerEvents = "none";

                }
                appState.window.style.zIndex = 1000;
                break;

            case Actions.UNLOCK_BAR:
                if (payload.hide) {
                    componentEl.style.visibility = "";
                    componentEl.style.pointerEvents = "";
                    Transition(componentEl, "opacity", 1, false);
                }
                appState.window.style.zIndex = "";
                break;
        }
    }

    const state = Store.registerComponent(AppBar, componentEl, initialState, reducerActions, reducer, reducerEffect, true);

    /* END OF COMPONENT DEFS */

    /* RENDER/REDUCER EFFECTS */

    const showBar = () => {
        const shadowRootHost = Common.shadowRoot.host;
        const builderHeight = componentEl.classList.contains("show-message") ? (
            componentEl.offsetHeight + 25
        ) : (
            componentEl.offsetHeight
        );

        setTimeout(() => {
            componentEl.style.transform = "";
            componentEl.classList.remove("bar-hidden");
            hideShowBtn.textContent = BAR_TOGGLE_MODE.HIDE;
            shadowRootHost.style.setProperty('--experience-bar-height-transform-offset', `${builderHeight}px`);
        });
    }

    const hideBar = () => {
        const shadowRootHost = Common.shadowRoot.host;
        const builderHeight = componentEl.classList.contains("show-message") ? (
            componentEl.offsetHeight + 25
        ) : (
            componentEl.offsetHeight
        );

        componentEl.style.transform = "translateY(" + builderHeight + "px)";
        componentEl.classList.add("bar-hidden");
        shadowRootHost.style.setProperty('--experience-bar-height-transform-offset', "0px");
        hideShowBtn.textContent = BAR_TOGGLE_MODE.SHOW;
    }

    const showBarMessage = (severity = "warn", message, dismissable = true, icon = '') => {
        componentEl.setAttribute("message-severity", severity);
        componentEl.classList.add("show-message");
        componentEl.querySelector('#bar-message').innerHTML = `${icon}<span>${message}<span>`;

        // clear dismiss-messages
        const dismissMessages = componentEl.querySelectorAll('#dismiss-bar-msg');
        dismissMessages.forEach(element => {
            element.remove();
        });

        if (dismissable) {
            const dismiss = document.createElement("div");
            dismiss.id = "dismiss-bar-msg";
            dismiss.textContent = "Dismiss";
            dismiss.addEventListener("click", () => {
                componentEl.classList.remove("show-message");
                dismiss.remove();
            });
            componentEl.appendChild(dismiss);
        }
    }

    const cleanBarMessage = () => {
        componentEl.classList.remove("show-message");
        componentEl.querySelector('#dismiss-bar-msg')?.remove();
    }

    const initEventListeners = () => {
        hideShowBtn.addEventListener("click", () => {
            (state.display == DISPLAY_MODES.SHOWN) ? Store.publishAction(Actions.HIDE_BAR) : Store.publishAction(Actions.SHOW_BAR);
        });
    }

    const render = () => {
        barContainer.innerHTML = "";
        hideShowBtn.textContent = state.display == DISPLAY_MODES.SHOWN ? BAR_TOGGLE_MODE.HIDE : BAR_TOGGLE_MODE.SHOW;
        if (Common.backgroundState.preview) {
            componentEl.style.opacity = 0;
            componentEl.style.visibility = "hidden";
        }

        const currentMode = (state.mode === MODES.LOADING) ? MODES.LOADING : (Common.backgroundState.appBarMode || state.mode);

        setTimeout(() => {
            (state.mode !== MODES.LOADING) && Store.publishAction(Actions.SHOW_ENGAGEMENTS)
        });

        switch (currentMode) {
            case MODES.LOADING:
                barContainer.appendChild(
                    MainLoader()
                );
                break;
            case MODES.INITIAL:
                cleanBarMessage();
                barContainer.appendChild(BarInitial());
                break;
            case MODES.NEW_EXPERIENCE:
                cleanBarMessage();
                barContainer.appendChild(
                    NewExperience()
                );
                break;
            case MODES.EDIT_EXPERIENCE:
                barContainer.appendChild(
                    EditBar()
                );
                break;
        }
        barContainer.style.opacity = 1;

        return componentEl;
    }

    const suggestPageSettings = (state, cb) => {
        return MessageHandler.postAICompletion({ type: "suggest-url", text: `${state.host}${state.build_url}`, hostname: state.host, pathname: state.build_url }, cb)
    }

    /* END */

    /* INIT */
    initEventListeners();
    return render();
}

export default AppBar