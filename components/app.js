import ExperienceBuilderViews from "../views/experienceBuilderViews.js";
import TemplatePicker from "../templatePicker.js";
import TemplateEditor from "../templateEditor/templateEditor.js";
import { createExperience, createFeatureTagSettings, getDefaultLabeledEvent, matchRuleWildcard, transformLabeledEventToExperience } from "../store/constants/experiences/experience.js";

// Components
import AppBar from "./appBar/appBar.js";
import ExperienceTypePicker from "./menus/experienceTypePicker.js";
import ExperienceSettings from "./menus/experienceSettings.js";
import PageChangeSidebar from "./menus/page-change-settings/pageChangeSidebar.js";
import PreviewController from "./previewController.js";
import LocalizationSettings from  "./localization/localizationSettings.js";

// Store
import Actions from "../store/constants/actions.js";
import { Store } from "../store/store.js";

// Utils
import { Transition } from "../async/asyncTransition.js";
import MessageHandler from "../routing/messageHandler.js";
import { ContentManager } from "../userpilot-utils/flowContentManager.js";
import Common from "../index.js";
import { sameUrls } from "../generic-utils/urlUtils.js";
import ENV from "../../env.js";
import { initShiftListener, removeShiftListener } from "../generic-utils/events.js";
import { FeatureTagSettings, FeatureTagSettingsSidebar, InteractionTypeSidebar } from "./menus/featureTags";
import { EXPERIENCE_STATUSES, experienceTypes } from "../store/constants/experiences/constants.js";
import Element from "../templateEditor/element/element.js";
import { createNewFeature } from "./menus/featureTags/featureTagSettings.js";
import EngagementLayer from "./engagement/engagementLayer.js";
import { composeWithPermissionsLock } from './menus/featureTags/helpers';
import LabeledEventSettingsSidebar from "./menus/labeledEvents/labeledEventsSidebar.js";
import { startCreatingNewLabeledEvent } from "./menus/labeledEvents/helpers.js";
import { EXPERIENCE_PAGE_SETTINGS } from "./menus/featureTags/constants.js";
import LabeledEventsInteractionTypeSidebar from "./menus/labeledEvents/labeledEventsInteractionTypeSidebar.js";
import Find from "../templateEditor/element/find.js";
import { getResponsiveWindow } from "../templateEditor/element/utils.js";

const WINDOW_VIEW_MODES = Object.freeze({
    NONE: "NONE",
    TEMPLATE_EDIT: "TEMPLATE_EDIT",
    TEMPLATE_PICKER: "TEMPLATE_PICKER",
    PAGE_CHANGE_EDIT: "PAGE_CHANGE_EDIT",
    INTERACTION_TYPE: "INTERACTION_TYPE",
    FEATURE_TAG_SETTINGS: "FEATURE_TAG_SETTINGS",
});

const pushNewExperience = (type, settings) => {
    let data = null;
    switch(type) {
        case experienceTypes.FLOW:
            data = Common.userData.flows;

        case experienceTypes.SPOTLIGHT:
            data = Common.userData.spotlights;

        case experienceTypes.BANNER_v2:
            data = Common.userData.banners;
    }
    data?.push({ ...settings });
}

const App = (props = {}) => {
    const componentEl = ExperienceBuilderViews.getViewElement("userpilot-app", {
        themeClass: (localStorage.getItem("userpilot-isDarkThemeEnabled") === "true") ? "theme-dark" : "theme-light"
    });
    const tooltipHelpMessageEl = ExperienceBuilderViews.getViewElement("tooltip-targeting-help-message");
    let transitionPromise = null;

    const initialState = {
        experience: null,
        experienceConfiguration: null,
        window: componentEl.querySelector("#window-container"),
        curr_window_view: WINDOW_VIEW_MODES.NONE,
        prev_window_view: WINDOW_VIEW_MODES.NONE,
        template_editor: null,
        step_preview_timeout: null,
        step_unpreview_timeout: null,
        step_unpreview_promise: null,
        user_permissions: {edit_permissions: true},
        default_locale: 'default',
        current_localization: Common.backgroundState.current_localization || 'default',
        active_content_index: Common.backgroundState.active_content_index || 0,
        featureTagsHeatmapTargets: [],
        ...props
    }

    const reducerActions = [
        Actions.SAVE_EXPERIENCE_SETTINGS,
        Actions.SAVE_EXPERIENCE_CONTENT,
        Actions.SAVE_EXPERIENCE_THEME_ID,
        Actions.SAVE_EXPERIENCE_LOCALIZATION,
        Actions.SET_BAR_TO_INITIAL,
        Actions.EDIT_EXPERIENCE,
        Actions.CLEAR_WINDOW_COMPONENTS,
        Actions.REMOVE_INLINE_PLACEHOLDER,
        Actions.HIDE_WINDOW_COMPONENTS,
        Actions.SHOW_WINDOW_COMPONENTS,
        Actions.SPAWN_TEMPLATE_PICKER,
        Actions.SPAWN_TEMPLATE_EDITOR,
        Actions.SELECT_NEW_EXPERIENCE_TYPE,
        Actions.SPAWN_EXPERIENCE_SETTINGS,
        Actions.INIT_NEW_EXPERIENCE,
        Actions.SPAWN_PAGE_CHANGE_SETTINGS,
        Actions.PREVIEW_STEP,
        Actions.UNPREVIEW_STEP,
        Actions.PREVIEW_EXPERIENCE,
        Actions.END_PREVIEW,
        Actions.END_PREVIEW_SDK,
        Actions.ENDED_PERVIEW_MSG,
        Actions.REMOVE_ENDED_PREVIEW_MSG,
        Actions.HIDE_PAGE_CHANGE_SETTINGS,
        Actions.SPAWN_FEATURE_SETTINGS,
        Actions.CHECK_FOR_UNSAVED_CHANGES,

        Actions.SPAWN_LOCALIZATION_SETTINGS,
        Actions.HIDE_LOCALIZATION_SETTINGS,
        Actions.LOCALES_VIEW,
        Actions.SET_ACTIVE_LOCALE,

        Actions.SHOW_TOOLTIP_HELP_MESSAGE,
        Actions.HIDE_TOOLTIP_HELP_MESSAGE,
        Actions.RESET_BUILD_URL,
        Actions.SET_ACTIVE_CONTENT_INDEX,
        Actions.SHIFT_KEY_PRESSED,
        Actions.RESET_CURRENT_LOCALIZATION,

        Actions.INIT_NEW_FEATURE_TAG,
        Actions.SPAWN_INTERACTION_TYPE_SIDEBAR,
        Actions.HIDE_INTERACTION_TYPE_SIDEBAR,
        Actions.SPAWN_FEATURE_TAG_SETTINGS_SIDEBAR,
        Actions.HIDE_FEATURE_TAG_SETTINGS_SIDEBAR,
        Actions.SAVE_FEATURE_TAG_SETTINGS,
        Actions.SYNC_FEATURE_TAG_CONFIGURATION,
        Actions.CREATE_NEW_FEATURE_TAG,
        Actions.UPDATE_FEATURE_TAG_HEATMAP_TARGETS,

        Actions.CREATE_NEW_LABELED_EVENT,
        Actions.CREATE_NEW_LABELED_EVENT_FROM_RAW_EVENT,
        Actions.INIT_NEW_LABELED_EVENT,
        Actions.SPAWN_LABELED_EVENT_SETTINGS,
        Actions.SPAWN_LABELED_EVENTS_INTERACTION_TYPE_SIDEBAR,
        Actions.SYNC_LABELED_EVENT_DATA,
    ]

    const reducer = (action, payload = {}) => {
        switch (action) {
            case Actions.SAVE_EXPERIENCE_SETTINGS:
                return {
                    experience: {
                        ...state.experience,
                        settings: payload.settings
                    }
                };

            case Actions.SAVE_EXPERIENCE_THEME_ID:
                return {
                    experience: {
                        ...state.experience,
                        settings: {
                            ...state.experience.settings,
                            theme_id: payload.theme_id
                        }
                    }
                }

            case Actions.SAVE_EXPERIENCE_CONTENT:
                return {
                    experience: {
                        ...state.experience,
                        settings: {
                            ...state.experience.settings,
                            steps_status: EXPERIENCE_STATUSES.PENDING_CHANGES,
                            cache: 1,
                        },
                        content: payload.content || state.experience.content
                    }
                };

            case Actions.SAVE_EXPERIENCE_LOCALIZATION:
                return {
                    experience: {
                        ...state.experience,
                        settings: {
                            ...state.experience.settings,
                            steps_status: EXPERIENCE_STATUSES.PENDING_CHANGES,
                            cache: 1,
                        },
                        localizationData: payload.localizationData || state.experience.localizationData
                    }
                }

            case Actions.SAVE_FEATURE_TAG_SETTINGS:
                return {
                    experience: {
                        ...state.experience,
                        settings: {
                            ...state.experience.settings,
                            ...payload.experience.settings,
                        }
                    }
                }

            case Actions.SYNC_FEATURE_TAG_CONFIGURATION:
                return {
                    experience: {
                        ...state.experience,
                        content: {
                            ...state.experience.content,
                            ...payload.experience.content,
                        }
                    }
                }

            case Actions.SYNC_LABELED_EVENT_DATA:
                return { experience: payload.experience || state.experience};

            case Actions.SET_BAR_TO_INITIAL:
                return { experience: null };

            case Actions.EDIT_EXPERIENCE:
                const editableRxperience = payload.experience || state.experience;
                if (!editableRxperience) console.error("DevError: Cannot edit null experience");
                return {
                    experience: editableRxperience,
                    user_permissions: payload.user_permissions || state.user_permissions,
                    experienceConfiguration: payload.experienceConfiguration || state.experienceConfiguration,
                    ...(editableRxperience.settings.type === experienceTypes.FEATURE_TAG) ? {} : {template_editor: new TemplateEditor(componentEl, payload.user_permissions || state.user_permissions)},
                };

            case Actions.INIT_NEW_EXPERIENCE:
                return {
                    experience: createExperience(payload.settings, payload.content) ,
                    experienceConfiguration: payload.experienceConfiguration || state.experienceConfiguration,
                    current_localization: state.default_locale,
                };

            case Actions.INIT_NEW_FEATURE_TAG:
                return {
                    experience: payload.experience,
                };

            case Actions.INIT_NEW_LABELED_EVENT:
                return {
                    experience: payload.experience,
                }

            case Actions.CLEAR_WINDOW_COMPONENTS:
                return {
                    curr_window_view: WINDOW_VIEW_MODES.NONE,
                    prev_window_view: state.curr_window_view
                }

            case Actions.SPAWN_TEMPLATE_EDITOR:
                return {
                    curr_window_view: WINDOW_VIEW_MODES.TEMPLATE_EDIT,
                    prev_window_view: state.curr_window_view
                }

            case Actions.PREVIEW_STEP:
                return {
                    step_preview_timeout: payload.preview_timeout,
                    curr_window_view: WINDOW_VIEW_MODES.TEMPLATE_EDIT,
                    prev_window_view: state.curr_window_view
                }

            case Actions.UNPREVIEW_STEP:
                return {
                    step_unpreview_timeout: payload.unpreview_timeout,
                    curr_window_view: WINDOW_VIEW_MODES.NONE,
                    prev_window_view: state.curr_window_view
                }

            case Actions.SPAWN_TEMPLATE_PICKER:
                return {
                    curr_window_view: WINDOW_VIEW_MODES.TEMPLATE_PICKER,
                    prev_window_view: state.curr_window_view
                }

            case Actions.PREVIEW_EXPERIENCE:
                return {
                    curr_window_view: WINDOW_VIEW_MODES.NONE,
                    prev_window_view: state.curr_window_view
                }
            case Actions.SPAWN_PAGE_CHANGE_SETTINGS:
                return {
                    curr_window_view: WINDOW_VIEW_MODES.PAGE_CHANGE_EDIT,
                    prev_window_view: state.curr_window_view
                }

            case Actions.HIDE_PAGE_CHANGE_SETTINGS:
                return {
                    curr_window_view: WINDOW_VIEW_MODES.NONE,
                    prev_window_view: state.curr_window_view
                }

            case Actions.LOCALES_VIEW:
            case Actions.SET_ACTIVE_LOCALE:
                return {
                    current_localization: payload.code
                }

            case Actions.SET_ACTIVE_CONTENT_INDEX:
                return {
                    active_content_index: payload.active_content_index,
                }

            case Actions.RESET_BUILD_URL:
                return {
                    experience: {...state.experience, settings: {...state.experience.settings, build_url: getResponsiveWindow().location.href}}
                }

            case Actions.SHIFT_KEY_PRESSED:
                return {}

            case Actions.RESET_CURRENT_LOCALIZATION:
                return {
                    current_localization: "default",
                }

            case Actions.SPAWN_INTERACTION_TYPE_SIDEBAR:
            case Actions.SPAWN_LABELED_EVENTS_INTERACTION_TYPE_SIDEBAR:
                return {
                    curr_window_view: WINDOW_VIEW_MODES.INTERACTION_TYPE,
                    prev_window_view: state.curr_window_view
                }

            case Actions.HIDE_INTERACTION_TYPE_SIDEBAR:
                return {
                    curr_window_view: WINDOW_VIEW_MODES.NONE,
                    prev_window_view: state.curr_window_view
                }

            case Actions.SPAWN_FEATURE_TAG_SETTINGS_SIDEBAR:
                return {
                    curr_window_view: WINDOW_VIEW_MODES.FEATURE_TAG_SETTINGS,
                    prev_window_view: state.curr_window_view
                }

            case Actions.HIDE_FEATURE_TAG_SETTINGS_SIDEBAR:
                return {
                    curr_window_view: WINDOW_VIEW_MODES.NONE,
                    prev_window_view: state.curr_window_view
                }

            case Actions.UPDATE_FEATURE_TAG_HEATMAP_TARGETS:
                return {
                    featureTagsHeatmapTargets: payload.targets
                }
        }
    }

    const reducerEffect = async (action, payload = {}) => {
        switch (action) {
            case Actions.SAVE_EXPERIENCE_CONTENT:
                if(state.user_permissions.edit_permissions) {
                    MessageHandler.postExperience({ experienceData: getExperienceData(), token: state.experience.settings.token, id: state.experience.settings.id });
                    Common.startActivityWatcher();
                    Store.publishAction(Actions.SHOW_DISCARD_CHANGES_BUTTON);
                    Store.publishAction(Actions.UPDATE_CURRENT_OPENED_CONTENT);
                }
                break;

            case Actions.SAVE_EXPERIENCE_SETTINGS:
            case Actions.RESET_BUILD_URL:
                if (state.user_permissions.edit_permissions) {
                    Transition(state.window, "opacity", 0, true);
                    MessageHandler.postExperience({
                        experienceData: getExperienceData({ cache: state.experience.settings.steps_status === EXPERIENCE_STATUSES.PENDING_CHANGES ? 1 : 0 }),
                        token: state.experience.settings.token,
                        id: state.experience.settings.id
                    });
                    !payload.skipWebappUpdate && await MessageHandler.putExperienceToWebApp(state.experience.settings);
                    Common.startActivityWatcher();
                }

                //redirect to new experience build url if changed
                if(state.active_content_index === 0 && !sameUrls(state.experience.settings.build_url, getResponsiveWindow().location.href)){
                    MessageHandler.setBackgroundState({
                        url: state.experience.settings.build_url,
                        last_active_url: state.experience.settings.build_url,
                        active_content_index: 0
                    });
                }
                break;

            case Actions.SAVE_EXPERIENCE_THEME_ID:
                if (state.user_permissions.edit_permissions) {
                    MessageHandler.postExperience({ experienceData: getExperienceData(), token: state.experience.settings.token, id: state.experience.settings.id });
                    await MessageHandler.putExperienceToWebApp(state.experience.settings);
                    Common.startActivityWatcher();
                }
                break;

            case Actions.SAVE_EXPERIENCE_LOCALIZATION:
                if(state.user_permissions.edit_permissions) {
                    MessageHandler.postExperience({ experienceData: getExperienceData(), token: state.experience.settings.token, id: state.experience.settings.id });
                    Common.startActivityWatcher();
                    Store.publishAction(Actions.SHOW_DISCARD_CHANGES_BUTTON);
                    Store.publishAction(Actions.UPDATE_CURRENT_OPENED_CONTENT);
                }
                break;

            case Actions.SAVE_FEATURE_TAG_SETTINGS:
                composeWithPermissionsLock(async () => MessageHandler.putFeatureTag(state.experience.settings.id, state.experience.settings), state.experience.settings.id);

                // redirect to new build url if changed
                 if(!sameUrls(state.experience.settings.build_url, window.location.href)){
                    MessageHandler.setBackgroundState({
                        url: state.experience.settings.build_url,
                        last_active_url: state.experience.settings.build_url,
                    });
                }
                break;

            case Actions.SET_BAR_TO_INITIAL:
                if(!state.user_permissions.edit_permissions) componentEl.classList.add('read-only');
                // await Store.publishAction(Actions.CLEAR_WINDOW_COMPONENTS);
                !payload.skipHide && Transition(state.window, "opacity", 0, true);
                break;

            case Actions.EDIT_EXPERIENCE:
                componentEl.classList.remove('read-only');
                if(!state.user_permissions.edit_permissions) componentEl.classList.add('read-only');
                Transition(state.window, "opacity", 0, true);
                if(state.user_permissions.edit_permissions) Common.startActivityWatcher();

                if(state.experience.content.length === 1 && !state.experience.content[0].groups.length ){
                    Store.publishAction(Actions.RESET_BUILD_URL);
                }

                if (Common.backgroundState.preview && state.experience && !componentEl.querySelector('#preview-controller-bar')) {
                    Common.backgroundState.previewState.ended && Store.publishAction(Actions.ENDED_PERVIEW_MSG);
                    componentEl.appendChild(PreviewController());
                }

                //update sentry context with current experience data
                Common.setSentryContext(state.experience.settings.token, state.experience.settings.title);

                break;

            case Actions.CLEAR_WINDOW_COMPONENTS:
                if (state.template_editor && state.template_editor.mode) await state.template_editor.hide();
                if (state.window.innerHTML) await Transition(state.window, "opacity", 0, true);
                Store.publishAction(Actions.REMOVE_INLINE_PLACEHOLDER);
                Store.publishAction(Actions.HIDE_PAGE_CHANGE_SETTINGS);
                Store.publishAction(Actions.HIDE_LOCALIZATION_SETTINGS);
                Store.publishAction(Actions.HIDE_INTERACTION_TYPE_SIDEBAR);
                Store.publishAction(Actions.HIDE_FEATURE_TAG_SETTINGS_SIDEBAR);
                Store.publishAction(Actions.CLOSE_FEATURE_TAG_HEATMAP);
                Store.publishAction(Actions.HIDE_LABELED_EVENT_SETTINGS_SIDEBAR);
                Element.unhighlightElement(Common.shadowRoot);
                initShiftListener();
                window.focus();
                break;

            case Actions.REMOVE_INLINE_PLACEHOLDER:
                if ([experienceTypes.SPOTLIGHT, experienceTypes.BANNER_v2].includes(state.experience?.settings.type)) {
                    state.template_editor?.targetElement?.ownerDocument?.querySelector("userpilotinlineelement")?.remove()
                    document.querySelector("userpilotinlineelement")?.remove();
                }
                break;

            // Use with care
            case Actions.HIDE_WINDOW_COMPONENTS:
                await Transition(state.window, "opacity", 0, false);
                state.window.style.visibility = "hidden";
                state.window.style.pointerEvents = "none";
                break;

            case Actions.SHOW_WINDOW_COMPONENTS:
                state.window.style.visibility = "";
                state.window.style.pointerEvents = "";
                Transition(state.window, "opacity", 1, false);
                break;

            case Actions.SELECT_NEW_EXPERIENCE_TYPE:
                if (state.prev_window_view != WINDOW_VIEW_MODES.NONE)
                    await Store.publishAction(Actions.CLEAR_WINDOW_COMPONENTS);
                state.window.style.opacity = 1;
                state.window.appendChild(ExperienceTypePicker());
                Store.publishAction(Actions.LOCK_BAR);
                break;

            case Actions.SPAWN_EXPERIENCE_SETTINGS:
                Store.publishAction(Actions.LOCK_BAR);
                state.window.style.opacity = 1;
                state.window.appendChild(ExperienceSettings({
                    ...payload.settings,
                    new_experience: payload.new_experience
                }));
                break;

            case Actions.SPAWN_FEATURE_SETTINGS:
                Store.publishAction(Actions.LOCK_BAR);
                state.window.style.opacity = 1;

                state.window.appendChild(FeatureTagSettings({
                    settings: payload.settings,
                    isNew: payload.isNew
                }));
                break;

            case Actions.SPAWN_LOCALIZATION_SETTINGS:
                    Store.publishAction(Actions.LOCK_BAR);
                    state.window.style.opacity = 1;
                    state.window.appendChild(LocalizationSettings({
                        localizationData: state.experience.localizationData,
                        disabled: Common.userData.appSettings && Common.userData.appSettings.localization_enabled === false
                    }));
                    break;

            case Actions.INIT_NEW_EXPERIENCE:
                await handleWindowViewChange(action);
                const user_permissions = await MessageHandler.lockExperience(state.experience.settings.token);
                Common.EXPERIENCE_TYPE = state.experience.settings.type;
                await Store.publishAction(Actions.EDIT_EXPERIENCE, { experience: state.experience, user_permissions });

                pushNewExperience(state.experience.settings.type, state.experience.settings);
                MessageHandler.postExperience({ experienceData: getExperienceData(), token: state.experience.settings.token, id: state.experience.settings.id });
                MessageHandler.setBackgroundState({
                    type: Common.EXPERIENCE_TYPE,
                    experience_token: state.experience.settings.token,
                    active_content_index: 0,
                    current_localization: "default",
                });
                break;

            case Actions.INIT_NEW_FEATURE_TAG:
                // await handleWindowViewChange(action);
                const feature_tag_user_permissions = await MessageHandler.lockExperience(state.experience.settings.id);

                Common.userData.featureTagsList.push(payload.featureTagWithConfig);
                Common.EXPERIENCE_TYPE = state.experience.settings.type;
                await Store.publishAction(Actions.EDIT_EXPERIENCE, { experience: state.experience, user_permissions: feature_tag_user_permissions});

                MessageHandler.setBackgroundState({
                    type: Common.EXPERIENCE_TYPE,
                    experience_token: `${state.experience.settings.id}`,
                    active_content_index: 0,
                    current_localization: "default",
                });
                break;

            case Actions.INIT_NEW_LABELED_EVENT:
                const labeled_event_user_permissions = await MessageHandler.lockExperience(`labeled_event_${state.experience.settings.id}`);

                Common.userData.labeledEvents.push({ ...payload.experience.settings, action: payload.experience.content.action });
                Common.EXPERIENCE_TYPE = state.experience.settings.type;
                await Store.publishAction(Actions.EDIT_EXPERIENCE, { experience: state.experience, user_permissions: labeled_event_user_permissions});

                MessageHandler.setBackgroundState({
                    type: Common.EXPERIENCE_TYPE,
                    experience_token: `${state.experience.settings.id}`,
                    active_content_index: 0,
                    current_localization: "default",
                });
                break;

            case Actions.SPAWN_TEMPLATE_EDITOR:
                removeShiftListener();
                await transitionPromise;
                if (state.window.innerHTML && state.prev_window_view != WINDOW_VIEW_MODES.NONE && state.prev_window_view != WINDOW_VIEW_MODES.TEMPLATE_EDIT) {
                    transitionPromise = new Promise(async resolve => {
                        await Transition(state.window, "opacity", 0, true);
                        resolve();
                    });
                    await transitionPromise;
                }
                if(!payload.preview) Store.publishAction(Actions.SET_UNDER_EDIT_STEP, { step_id: payload.step_id });
                state.window.style.opacity = 1;

                let step;
                let group;
                const localizedContent = state.experience.localizationData.find(item => item.code === state.current_localization).steps[payload.step_id];
                if(state.experience.settings.type === 'flow') {
                    step = ContentManager.getStepByID(state.experience.content, payload.step_id);
                    group = ContentManager.getStepGroup(state.experience.content, step);
                } else {
                    step = state.experience.content.steps[0];
                    group = state.experience.content;
                }

                await state.template_editor.show(step, group, localizedContent, state.current_localization);
                break;

            case Actions.SPAWN_TEMPLATE_PICKER:
                new TemplatePicker(
                    payload.type,
                    payload.new_group,
                    (payload.themeId || state.experience.settings.theme_id),
                    payload.addition_callback,
                    payload.step_settings,
                    payload.preventDefaultAction,
                    payload.close_callback
                );
                break;

            case Actions.SPAWN_PAGE_CHANGE_SETTINGS:
                await handleWindowViewChange(action);
                state.window.appendChild(PageChangeSidebar({
                    page_change: payload.page_change,
                    content_index: payload.content_index
                }));
                break;

            case Actions.PREVIEW_EXPERIENCE:
                MessageHandler.setBackgroundState({ preview: true, preview_type: payload.preview_type });
                await Store.publishAction(Actions.CLEAR_WINDOW_COMPONENTS);
                payload.success && componentEl.appendChild(PreviewController());
                removeShiftListener();
                break;

            case Actions.END_PREVIEW:
                const type = state.experience?.settings.type || null;
                if(type !== experienceTypes.FLOW  || payload.remove_controller) {
                    const previewController = componentEl.querySelector("#preview-controller-bar");
                    if (previewController) ExperienceBuilderViews.removeUsingOpacity(previewController);

                    MessageHandler.setBackgroundState({ preview: false });
                    await Store.publishAction(Actions.SHOW_BAR);
                    Store.publishAction(Actions.SHOW_FLOW_OVERFLOW_ARROWS);

                    type === experienceTypes.FEATURE_TAG
                    && Store.publishAction(Actions.SHOW_FEATURE_TAG_TRIGGERED_MESSAGE, {eventInfo: payload});

                    type === experienceTypes.LABELED_EVENT
                    && Store.publishAction(Actions.SHOW_LABELED_EVENT_TRIGGERED_MESSAGE, {eventInfo: payload});

                    initShiftListener();
                    window.focus();

                    Store.publishAction(Actions.REMOVE_ENDED_PREVIEW_MSG)

                } else if (Common.backgroundState.preview) {
                    Store.publishAction(Actions.ENDED_PERVIEW_MSG)

                    MessageHandler.setBackgroundState({
                        previewState: {
                            ...Common.backgroundState.previewState,
                            ended: true,
                        }
                    });
                }

                if(type === experienceTypes.BANNER_v2) Store.publishAction(Actions.SPAWN_TEMPLATE_EDITOR, { step_id: state.experience.content.steps[0].action_id });
                break;

            case Actions.END_PREVIEW_SDK:
                const data = JSON.stringify({
                    "from-chrome": 1,
                    "end-preview": 1
                });
                const Window = getResponsiveWindow();
                Window.postMessage(data, window.location.origin);
                break

            case Actions.REMOVE_ENDED_PREVIEW_MSG:
                MessageHandler.setBackgroundState({
                    previewState: {
                        ...Common.backgroundState.previewState,
                        ended: false,
                    }
                });

                const previewMessage = componentEl.querySelector("#ended-preview-message");
                previewMessage && (previewMessage.remove())
                break;

            case Actions.ENDED_PERVIEW_MSG:
                const endedPreviewMessage = (msg) => {
                    const div = document.createElement("div");
                    div.id = "ended-preview-message";
                    div.innerHTML = msg;
                    return div;
                }

                if (componentEl && !componentEl.querySelector("#ended-preview-message")) {
                    const previewText = Common.previewState?.previewText || "Preview Ended"
                    const controllerEl = componentEl.querySelector("#preview-controller-bar");
                    controllerEl?.insertBefore(endedPreviewMessage(previewText), controllerEl.firstChild);
                }
                break;

            case Actions.SHOW_TOOLTIP_HELP_MESSAGE:
                componentEl.appendChild(tooltipHelpMessageEl);
                break;

            case Actions.HIDE_TOOLTIP_HELP_MESSAGE:
                tooltipHelpMessageEl.remove();
                break;

            case Actions.SET_ACTIVE_CONTENT_INDEX:
                MessageHandler.setBackgroundState({
                    active_content_index: payload.active_content_index,
                    last_active_url: payload.last_active_url || getResponsiveWindow().location.href,
                    ...payload.url ? { url: payload.url } : {}
                });
                break;

            case Actions.LOCALES_VIEW:
                await Store.publishAction(Actions.CLEAR_WINDOW_COMPONENTS);
                MessageHandler.setBackgroundState({ current_localization: state.current_localization })
                if(state.experience.settings.type === experienceTypes.BANNER_v2) Store.publishAction(Actions.SPAWN_TEMPLATE_EDITOR, { step_id: state.experience.content.steps[0].action_id });
                break;

            case Actions.SHIFT_KEY_PRESSED:
                if (
                  !state.experience ||
                  state.curr_window_view !== WINDOW_VIEW_MODES.NONE ||
                  [experienceTypes.FEATURE_TAG, experienceTypes.LABELED_EVENT, experienceTypes.BANNER_v2].includes(state.experience.settings.type) ||
                  !state.user_permissions.edit_permissions
                )
                  return;

                Store.publishAction(Actions.ACTIVATE_ELEMENT_SELECTION)
                break;

            case Actions.SPAWN_INTERACTION_TYPE_SIDEBAR:
                state.window.style.opacity = 1;
                state.window.appendChild(InteractionTypeSidebar({
                    targetEl: payload.targetEl,
                    featureTagSettings: payload.featureTagSettings,
                }));
                break;

            case Actions.SPAWN_LABELED_EVENTS_INTERACTION_TYPE_SIDEBAR:
                state.window.style.opacity = 1;
                state.window.appendChild(LabeledEventsInteractionTypeSidebar({
                    targetEl: payload.targetEl,
                    isRawEvent: payload.isRawEvent,
                    labeledEvent: payload.labeledEvent
                }));
                break;

            case Actions.SPAWN_FEATURE_TAG_SETTINGS_SIDEBAR:
                state.window.style.opacity = 1;
                state.window.appendChild(FeatureTagSettingsSidebar({
                    targetEl: payload.targetEl
                }));
                break;

            case Actions.CREATE_NEW_LABELED_EVENT:
                startCreatingNewLabeledEvent();
                MessageHandler.setBackgroundState({ experience_token: "" });
                break;

            case Actions.CREATE_NEW_LABELED_EVENT_FROM_RAW_EVENT:
                const rawEventData = Common.backgroundState.rawEventData;
                const labeledEventsElements = Find.manualLookup({
                    element: rawEventData.targeting.css_selector,
                    text: rawEventData.targeting.text,
                })

                Store.publishAction(Actions.HIDE_BAR);
                Store.publishAction(Actions.LOCK_BAR, { hide: true });
                Store.publishAction(Actions.SPAWN_LABELED_EVENT_SETTINGS, {
                    targetEl: labeledEventsElements[0],
                    isRawEvent: true,
                    labeledEvent: rawEventData,
                });

                MessageHandler.setBackgroundState({ experience_token: "", type: experienceTypes.LABELED_EVENT, rawEventData: null, });
                break;

            case Actions.CREATE_NEW_FEATURE_TAG:
                createNewFeature(
                    createFeatureTagSettings({
                        build_url: Common.backgroundState.url,
                        display_name: Common.backgroundState.data.display_name || "",
                        description: Common.backgroundState.data.description || "",
                        category: Common.backgroundState.data.category,
                    }),
                );
                MessageHandler.setBackgroundState({ experience_token: "" });
                break;

            case Actions.SPAWN_LABELED_EVENT_SETTINGS:

                let labeledEvent = null;
                if(payload.isNew) {
                    Store.publishAction(Actions.SHOW_LOADING_SCREEN);
                    let pageRule = {};
                    const pathname = window.location.href.split(window.location.host)[1];

                    await Store.publishAction(Actions.SUGGEST_PAGE_SETTINGS, { pathname , host: window.location.host });
                    const result = await Common.suggestedPageConfiguration;

                    if (!result || (result.match == "regex" && !matchRuleWildcard(pathname, result.pathname))) {
                        pageRule = {
                            match: EXPERIENCE_PAGE_SETTINGS.MATCH,
                            part: window.location.href.split(window.location.host)[1],
                        }
                    } else {
                        pageRule = {
                            match: result.match,
                            part: result.pathname
                        }
                    }

                    labeledEvent = getDefaultLabeledEvent({
                        targetEl: payload.targetEl,
                        action: payload.action || "click",
                        pageRule: pageRule,
                    });

                    (state.experience) ? await Store.publishAction(Actions.SHOW_CURRENT_ACTIVE_EXPERIENCE) : await Store.publishAction(Actions.SET_BAR_TO_INITIAL, { skipHide: true });
                }

                if(payload.isRawEvent) {
                    const {targeting: labeledEventTargeting, ...labeledEventSettings} = payload.labeledEvent;
                    const labeledEventExperience = transformLabeledEventToExperience(labeledEventSettings, {targeting: labeledEventTargeting});
                    labeledEvent = { labeledEvent: labeledEventExperience.settings, labeledEventConfiguration: labeledEventExperience.content, };
                }

                state.window.style.opacity = 1;
                state.window.appendChild(
                    LabeledEventSettingsSidebar({
                        isNew: payload.isNew || payload.isRawEvent,
                        experience: labeledEvent,
                    })
                );
                break;

            case Actions.CHECK_FOR_UNSAVED_CHANGES:
                switch(true) {
                    case state.curr_window_view === WINDOW_VIEW_MODES.FEATURE_TAG_SETTINGS:
                        Store.publishAction(Actions.CLOSE_FEATURE_TAG_SETTINGS_SIDEBAR, { callback: payload.callback });
                        break;

                    case Common.shadowRoot.querySelector("#window-container > #page-change-option-sidemenu") != null:
                        Store.publishAction(Actions.CLOSE_PAGE_CHANGE_SETTINGS, { callback: payload.callback });
                        break;

                    default:
                        payload.callback();
                }
                break;
        }
    }

    const state = Store.registerComponent(App, componentEl, initialState, reducerActions, reducer, reducerEffect, true);

    const handleWindowViewChange = async (action) => {
        switch(action) {
            default:
                if (state.prev_window_view != WINDOW_VIEW_MODES.NONE) await Store.publishAction(Actions.CLEAR_WINDOW_COMPONENTS);
        }
        state.window.style.opacity = 1;
    }

    const getExperienceData = (data = {}) => ({
        content: state.experience.content,
        localizationData: state.experience.localizationData,
        theme_id: state.experience.settings.theme_id,
        cache: 1,
        ...data,
    })

    const render = () => {
        componentEl.appendChild(
            AppBar()
        );
        componentEl.appendChild(
            EngagementLayer()
        );
        return componentEl;
    }

    return render();
}

export default App;