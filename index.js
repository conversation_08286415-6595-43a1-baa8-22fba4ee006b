import InitializeApp from "./app-initializer/initializeAppRoot.js";
import UserData from "./common/user-data/userData.js";
import MessageHandler from "./routing/messageHandler.js";
import { delay } from "./async/async.js";
import App from "./components/app.js";
import { pageUrlMatchesAnyContent } from "./components/appBar/editExperience/editBar.js";

import Actions from "./store/constants/actions.js";
import { Store } from "./store/store.js";

import * as Sentry from "@sentry/browser";

//utils
import { migrateAccount, migrateExperience } from "./migration/migrate.js";
import { checkContentIntegrity, overrideStepSettings } from "./checks/integrity.js";
import { Alert } from "./generic-components/alert.js";
import { SpotlightManager } from "./userpilot-utils/spotlightManager.js";
import { sameUrls, injectScript } from "./generic-utils/urlUtils.js";
import { experienceTypes, EXPERIENCE_STATUSES } from "./store/constants/experiences/constants.js";
import ExperienceBuilderViews from "./views/experienceBuilderViews.js";
import { setLocalStorageItem } from "./generic-utils/localStorageUtils.js";
import { createExperienceSettings, hasFeaturesManagePermission, transformLabeledEventToExperience } from "./store/constants/experiences/experience.js";
import { transformFlow } from "./userpilot-utils/flowManager.js";
import { transformLocalizationData } from "./userpilot-utils/localizationManager.js";
import { getResponsiveWindow } from "./templateEditor/element/utils.js";

const DEFAULT_EXPERIENCE_DETAILS_VALUE = { experience: null, user_permissions: null, experienceConfiguration: null };
const removeTabIndex = () => document.querySelectorAll('[tabIndex="-1"]').forEach(element => element.removeAttribute("tabIndex"));

export default class Common {
   static user;
   static isImpersonating;
   static organization;
   static ORGANIZATION_ID;
   static APP_ID;
   static baseURL = chrome.runtime.getURL('src');
   static shadowRoot;
   static userData;
   static backgroundState;
   static activityWatcherRef = null;
   static LEGACY_APP_TOKEN;
   static EXPERIENCE_TYPE;
   static ELEMENT_PICKER_EVENT_REF = null;
   static suggestedPage = {
      active: 0,
      url: "",
      promise: new Promise((resolve) => { resolve() })
   };
   static suggestedPageConfiguration = new Promise((resolve) => { resolve() });
   static previewState = {};

   constructor(backgroundState, user) {
      Common.user = user;
      Common.isImpersonating = user.metadata.impersonation;
      Common.ORGANIZATION_ID = user.metadata.organization_id.toString();
      Common.backgroundState = backgroundState;

      //make sure there is no content for the sdk to preview when preview is off
      !Common.backgroundState.preview && sessionStorage.removeItem("userpilot_in_preview") && endPreview();

      this.init(Common.backgroundState.experience_token);
   }

   static startActivityWatcher() {
         clearTimeout(Common.activityWatcherRef );
         Common.activityWatcherRef  = setTimeout(async () => {
            await Store.publishAction(Actions.AUTO_SAVE_STEP);

            MessageHandler.releaseEditor(Common.backgroundState.experience_token, Common.user.email);
            Common.initInitialState(null);

            Store.publishAction(Actions.SESSION_ENDED);
            Store.publishAction(Actions.SHOW_WINDOW_COMPONENTS);
            Store.publishAction(Actions.END_PREVIEW_SDK);
            Store.publishAction(Actions.END_PREVIEW, {remove_controller: true});

         }, process.env.LOCKOUT_PERIOD_MINUTES*60*1000);
   };

   static hashCode(str, seed = 3){
      let h1 = 0xdeadbeef ^ seed, h2 = 0x41c6ce57 ^ seed;
      for (let i = 0, ch; i < str.length; i++) {
            ch = str.charCodeAt(i);
            h1 = Math.imul(h1 ^ ch, 2654435761);
            h2 = Math.imul(h2 ^ ch, 1597334677);
      }
      h1 = Math.imul(h1 ^ (h1>>>16), 2246822507) ^ Math.imul(h2 ^ (h2>>>13), 3266489909);
      h2 = Math.imul(h2 ^ (h2>>>16), 2246822507) ^ Math.imul(h1 ^ (h1>>>13), 3266489909);
      return 4294967296 * (2097151 & h2) + (h1>>>0);
   }

   static upload(event, reponse) {
      const input = event.currentTarget;
      const file = !event.dataTransfer ? event.target.files[0] : event.dataTransfer.files[0]
      const reader = new FileReader();
      const filename = this.hashCode((new Date().getTime())+"_" + file.name);
      const extension = file.type.replace(/(.*)\//g, '');
      reader.onload = () => {
         let xhr = new XMLHttpRequest();
         let image = reader.result.split("base64,")[1];
         xhr.open("POST", "https://upload.userpilot.io/api/v1/handler.php?route=uploadImage&app_token="+Common.APP_ID);
         xhr.setRequestHeader("Content-Type", "application/json;charset=UTF-8");
         xhr.send(JSON.stringify({body: image, filename: filename, ext: extension}));
         xhr.onreadystatechange = () => {
            if (xhr.readyState === 4) {
                  reponse(xhr.responseText);
            }
         };
      };
      reader.readAsDataURL(file);
      input.value = "";
   }

   static getApptoken() {
      if (this.backgroundState.app_id && this.isImpersonating) return this.backgroundState.app_id;
      else if(this.backgroundState.app_id) {
         const currentApp = this.profile.applications.find(app => app.application_id === this.backgroundState.app_id);
         if(currentApp) return this.backgroundState.app_id;

         MessageHandler.setBackgroundState({}, true);
      }

      return this.isImpersonating
      ? this.organization.applications[0].application_id
      : this.profile.applications[0].application_id;
   }

   async init(experienceToken) {

      removeTabIndex();
      setInterval(removeTabIndex, 1000);
      //stop sdk
      Common.suppressSDK();

      // Render in loading state
      const shadowRoot = await InitializeApp();
      if (shadowRoot == false) return false;
      Common.shadowRoot = shadowRoot;
      let loaderBufferPromise;
      this.render();
      loaderBufferPromise = new Promise(async resolve => {
         await delay(1500);
         resolve();
      });

      [Common.profile, Common.organization] = await Promise.all([
         await MessageHandler.getProfile(),
         await MessageHandler.getOrganization()
      ]);

      Common.APP_ID = Common.getApptoken();

      // deploy SDK if does not exist
      chrome.runtime.sendMessage({ type: "maybe_inject_SDK", appId: Common.APP_ID });

      if(!Common.isImpersonating) Common.profile = await MessageHandler.getProfile(Common.APP_ID);

      MessageHandler.setBackgroundApplicationId(Common.APP_ID);
      await MessageHandler.getFirebaseAuthToken();
      Common.setAppIdInLocalStorage();

      this.initSentry(experienceToken);

      Common.userData = await UserData();
      const {experience, user_permissions, experienceConfiguration} = await Common.getExperienceDetails(experienceToken);
      await loaderBufferPromise;
      await Common.initInitialState(experience, user_permissions, experienceConfiguration);

      if(Common.backgroundState.step_id) {
         if(sameUrls(Common.backgroundState.last_active_url, getResponsiveWindow().location.href))
         Store.publishAction(Actions.SPAWN_TEMPLATE_EDITOR, {
            step_id: Common.backgroundState.step_id
         });

         MessageHandler.setBackgroundState({
            step_id: null
         });
      }

      //stop sdk in case the first suppress fired while neither the sdk loaded or chrome extension initialized
      Common.suppressSDK();
      MessageHandler.keepSWAlive();
   }

   render() {
      Common.shadowRoot.appendChild(
         App()
      );

      const alertContainer = Common.shadowRoot.querySelector(".userpilot-alert-container") || ExperienceBuilderViews.getViewElement("userpilot-alert-container");
      Common.shadowRoot.appendChild(alertContainer);
   }

   static async initInitialState(experience, user_permissions, experienceConfiguration) {
      if (experience) {
         await Store.publishAction(Actions.EDIT_EXPERIENCE, { experience, user_permissions, experienceConfiguration});
         Store.publishAction(Actions.SHOW_FLOW_OVERFLOW_ARROWS);
         Common.scrollToPageContent(experience);
      } else {
         const isTargetModeEnabled = Common.shadowRoot.querySelector("#tooltip-targeting-help-message");
         if(!isTargetModeEnabled) {
            await Store.publishAction(Actions.CLEAR_WINDOW_COMPONENTS);
            await Store.publishAction(Actions.SET_BAR_TO_INITIAL);
            await Store.publishAction(Actions.UNLOCK_BAR, { hide: true });
            await Store.publishAction(Actions.SHOW_BAR);
         }

         if(Common.backgroundState.type === experienceTypes.RAW_EVENT) setTimeout(() => Store.publishAction(Actions.CREATE_NEW_LABELED_EVENT_FROM_RAW_EVENT));

         if(
            Common.backgroundState.type === experienceTypes.LABELED_EVENT &&
            Common.backgroundState.experience_token === '0'
         ) setTimeout(() => Store.publishAction(Actions.CREATE_NEW_LABELED_EVENT));

         if(
            Common.backgroundState.type === experienceTypes.FEATURE_TAG &&
            Common.backgroundState.experience_token === '0'
         ) setTimeout(() => Store.publishAction(Actions.CREATE_NEW_FEATURE_TAG));

         if(
          [experienceTypes.FLOW, experienceTypes.SPOTLIGHT, experienceTypes.BANNER_v2].includes(Common.backgroundState.type) &&
            Common.backgroundState.experience_token === 0
         ) {
            setTimeout(async () => {
               await Store.publishAction(Actions.SPAWN_EXPERIENCE_SETTINGS, { settings: createExperienceSettings(Common.backgroundState.type), new_experience: true });
               Common.openerTabId = Common.backgroundState.openerTabId;
               MessageHandler.setBackgroundState({ experience_token: null, openerTabId: null, });
            });
         }
      }
   }

   static async reload(reloadProps = {}) {
      const isTargetModeEnabled = Common.shadowRoot.querySelector("#tooltip-targeting-help-message");
      const backdrop = ExperienceBuilderViews.getViewElement("ui-pattern-prompt-backdrop");
      if(isTargetModeEnabled) {
         const shadowRootContainer = Common.shadowRoot.querySelector(".userpilot");
         shadowRootContainer?.appendChild(backdrop);
      }

      const { backgroundState =  Common.backgroundState, tabInfo = {}} = reloadProps;

      if (tabInfo.changeInfoUrl) {
         const isSameUrl = sameUrls(this.currentURL, tabInfo.changeInfoUrl || getResponsiveWindow().location.href);
         if (isSameUrl) {
            return;
         } else {
            this.currentURL = tabInfo.changeInfoUrl;
         }
      }

      const experienceToken = Common.backgroundState.experience_token;
      Common.backgroundState = backgroundState;

      Store.publishAction(Actions.CLEAR_WINDOW_COMPONENTS);
      Store.publishAction(Actions.SHOW_LOADING_SCREEN);
      Common.APP_ID = Common.getApptoken();
      Common.setAppIdInLocalStorage();
      Common.userData = await UserData();

      const {experience, user_permissions, experienceConfiguration} = await Common.getExperienceDetails(experienceToken);

      await this.initInitialState(experience, user_permissions, experienceConfiguration);
      backdrop.remove();
   }

   static async getExperienceDetails(experienceToken) {
      const backgroundState = Common.backgroundState;

      switch(backgroundState.type) {
         case experienceTypes.LABELED_EVENT:
            return (experienceToken && (experienceToken !== '0')) ? await this.getLabeledEventContent(experienceToken) : DEFAULT_EXPERIENCE_DETAILS_VALUE;

         case experienceTypes.FEATURE_TAG:
            return (experienceToken && (experienceToken !== '0')) ? await this.getFeatureTagContent(experienceToken) : DEFAULT_EXPERIENCE_DETAILS_VALUE;

         case experienceTypes.FLOW:
         case experienceTypes.SPOTLIGHT:
         case experienceTypes.BANNER_v2:
            return (experienceToken) ? await this.getExperienceContent(experienceToken) : DEFAULT_EXPERIENCE_DETAILS_VALUE;

         default:
            return DEFAULT_EXPERIENCE_DETAILS_VALUE;
      }
   }

   static async getExperienceContent(token) {
      const experience = Common.userData.flows.find(experience => experience.token == token) ||
         Common.userData.spotlights.find(experience => experience.token == token) ||
         Common.userData.banners.find(experience => experience.token == token);

      const raiseError = () => {
         Alert({ severity: "error", content: "Content was not found" });
         MessageHandler.clearBackgroundState();
         return DEFAULT_EXPERIENCE_DETAILS_VALUE
      }

      if (!experience) return raiseError();
      Common.EXPERIENCE_TYPE = experience.type;

      const experienceConfiguration = MessageHandler.getExperienceConfiguration({ type: experience.type, experience_id: experience.id, });
      const { experience: experienceData, user_permissions } = await MessageHandler.getExperience(experience);
      if (!experienceData) return raiseError();
      const isFlow = (experience.type === experienceTypes.FLOW);
      const isSpotlight = (experience.type === experienceTypes.SPOTLIGHT);

      const experienceContentWithPixelsWidth = isFlow ? transformFlow(experienceData.content, experienceData.cache) : isSpotlight ? SpotlightManager.transformSpotlight(experienceData.content, experienceData.cache) : experienceData.content;
      const experienceWithPixelsWidth = {
         ...experienceData,
         content: experienceContentWithPixelsWidth,
         localizationData: transformLocalizationData(experienceData.localizationData),
      }
      if(user_permissions.edit_permissions) MessageHandler.concurrentPostExperience({ experienceData: experienceWithPixelsWidth, token: token, id: experience.id });

      //update experience content in firestore to match the default localization content
      checkContentIntegrity({ experienceData: experienceWithPixelsWidth, token: token, id: experience.id, edit_permissions: user_permissions.edit_permissions });

      return {
        user_permissions,
        experienceConfiguration: await experienceConfiguration,
        experience: {
          content: experienceContentWithPixelsWidth,
          localizationData: experienceWithPixelsWidth.localizationData,
          settings: { ...experience, theme_id: experienceData.theme_id },
        },
      };
   }

   static async getFeatureTagContent(featureTagId) {
      const featureTag = Common.userData.featureTagsList.find(featureTag => (featureTag.status !== EXPERIENCE_STATUSES.ARCHIVED && featureTag.id == featureTagId));

      const raiseError = () => {
         Alert({ severity: "error", content: "Feature was not found" });
         MessageHandler.clearBackgroundState();
         return DEFAULT_EXPERIENCE_DETAILS_VALUE
      }

      if (!featureTag) return raiseError();
      Common.EXPERIENCE_TYPE = experienceTypes.FEATURE_TAG;

      const featureTagData = MessageHandler.getFeatureTag(featureTagId);
      const featureTagContent = MessageHandler.getFeatureTagConfiguration(featureTagId);

      const { build_url, description, display_name, id, status, type, element, category, } = await featureTagData;
      const { targeting, triggering_type, metadata, } = await featureTagContent;

      const user_permissions = await Common.getFeatureTagPermissions(featureTagId);

      return {
         user_permissions,
         experience: {
            content: {
               triggering_type,
               targeting,
               metadata,
               element: JSON.parse(pako.inflateRaw(atob(element), { to: "string" })),
            },
            settings: { build_url, description, display_name, id, status, type, category },
         }
      };
   }

   static async getLabeledEventContent(labeledEventId) {
      const labeledEvent = Common.userData.labeledEvents.find(labeledEvent => (labeledEvent.status !== EXPERIENCE_STATUSES.ARCHIVED && labeledEvent.id == labeledEventId));

      const raiseError = () => {
         Alert({ severity: "error", content: "Labeled event was not found" });
         MessageHandler.clearBackgroundState();
         return DEFAULT_EXPERIENCE_DETAILS_VALUE
      }

      if (!labeledEvent) return raiseError();
      Common.EXPERIENCE_TYPE = experienceTypes.LABELED_EVENT;

      const labeledEventData = MessageHandler.getLabeledEvent(labeledEventId);
      const labeledEventContent = MessageHandler.getLabeledEventConfiguration(labeledEventId);

      const experience = transformLabeledEventToExperience(await labeledEventData, await labeledEventContent)
      const user_permissions = await Common.getFeatureTagPermissions(`labeled_event_${labeledEventId}`);

      return {
         user_permissions,
         experience: experience,
      };
   }

   static async getFeatureTagPermissions(featureTagId) {
      if(!hasFeaturesManagePermission(Common.profile.perms))
         return { edit_permissions: false };

      return await MessageHandler.lockExperience(featureTagId);
   }

   static isDarkThemeApplied() {
      return Common.shadowRoot.querySelector(".theme-dark") !== null;
   }

   static exitChromeExtension() {
      Common.backgroundState?.preview === true && (Store.publishAction(Actions.END_PREVIEW_SDK), Store.publishAction(Actions.END_PREVIEW))
      const shadowRootContainer = document.querySelector('#userpilot-root');
      shadowRootContainer.remove();
      MessageHandler.deleteFromBackgroundState();
      Common.unSuppressSDK();
   }

   static scrollToPageContent(experience) {
      if (
         experience.settings.type === "flow" &&
         experience.content[0]?.groups.length > 0 &&
         pageUrlMatchesAnyContent(experience.content, experience.settings.build_url)
      ) {
         Store.publishAction(Actions.SCROLL_TO_CURRENT_PAGE_CONTENT);
      }
   }

   static updateAppSettings(payload) {
      Common.userData.appSettings = {
         ...Common.userData.appSettings,
         ...payload
      }
   }

   initSentry(experienceToken = null, experienceTitle = null) {
      Sentry.setUser({ email: Common.user.email })

      Common.setSentryContext(experienceToken, experienceTitle)
   }

   static setSentryContext(experienceToken = null, experienceTitle = null) {
      let sentryContext = {
         app_id: Common.APP_ID,
         org_id: Common.ORGANIZATION_ID
      }
      experienceToken && (sentryContext.experienceToken = experienceToken);
      experienceTitle && (sentryContext.experienceTitle = experienceTitle);
      Sentry.setContext("account", {...sentryContext});
   }

   endPreview() {
      const data = JSON.stringify({
         "from-chrome": 1,
         "end-preview": 1
      });

      const Window = getResponsiveWindow();
      Window.postMessage(data, window.location.origin);
   }

   static reloadLocalesList() {
      Store.publishAction(Actions.REFRESH_LOCALES_LIST);
   }

   static suppressSDK() { chrome.runtime.sendMessage({ type: "suppress_SDK" }); }

   static unSuppressSDK() {
      window.postMessage(JSON.stringify({ "unsuppress": 1, "from-chrome": 1}), window.location.origin);
      sessionStorage.removeItem("userpilotChromeBuilding");
      sessionStorage.removeItem("userpilotExtensionFound");
      // injectScript(`window.userpilot && userpilot.reload(1)`);
      location.reload();
   }

   static setAppIdInLocalStorage = () => setLocalStorageItem("userpilot:active_token", Common.APP_ID);
}
