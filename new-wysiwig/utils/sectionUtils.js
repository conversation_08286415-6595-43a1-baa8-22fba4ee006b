/**
 * 区块工具函数
 * 函数式实现的区块相关工具函数
 */

/**
 * 获取区块类型
 * @param {HTMLElement} section - 区块元素
 * @returns {string|null} 区块类型
 */
export const getSectionType = (section) => {
  if (!section || !section.classList) return null;

  const elementClasses = section.classList;

  if (elementClasses.contains('userpilot-button')) {
    return 'userpilot-node-button';
  } else if (elementClasses.contains('userpilot-header-section')) {
    return 'userpilot-header-section';
  } else if (elementClasses.contains('userpilot-text-section')) {
    return 'userpilot-text-section';
  } else if (elementClasses.contains('userpilot-image-section')) {
    return 'userpilot-image-section';
  } else if (elementClasses.contains('userpilot-button-section')) {
    return 'userpilot-button-section';
  } else if (elementClasses.contains('userpilot-html-section') || 
             elementClasses.contains('userpilot-video-section')) {
    return 'userpilot-html-section';
  } else if (elementClasses.contains('userpilot-emoji-section')) {
    return 'userpilot-emoji-section';
  } else if (elementClasses.contains('userpilot-input-radio')) {
    return 'userpilot-input-radio';
  } else if (elementClasses.contains('userpilot-input-text')) {
    return 'userpilot-input-text';
  } else if (elementClasses.contains('userpilot-input-text-large')) {
    return 'userpilot-input-text-large';
  } else if (elementClasses.contains('userpilot-input-likert-scale')) {
    return 'userpilot-input-likert-scale';
  } else if (elementClasses.contains('userpilot-embed-section')) {
    return 'userpilot-embed-section';
  }

  return null;
};

/**
 * 检查是否为有效的区块元素
 * @param {HTMLElement} element - 要检查的元素
 * @returns {boolean} 是否为有效区块
 */
export const isValidSection = (element) => {
  return element && 
         element.nodeType === Node.ELEMENT_NODE && 
         getSectionType(element) !== null;
};

/**
 * 获取区块的显示名称
 * @param {string} sectionType - 区块类型
 * @returns {string} 显示名称
 */
export const getSectionDisplayName = (sectionType) => {
  const displayNames = {
    'userpilot-header-section': '标题',
    'userpilot-text-section': '文本',
    'userpilot-button-section': '按钮',
    'userpilot-image-section': '图片',
    'userpilot-html-section': 'HTML',
    'userpilot-emoji-section': '表情',
    'userpilot-input-text': '单行输入',
    'userpilot-input-text-large': '多行输入',
    'userpilot-input-radio': '单选框',
    'userpilot-input-likert-scale': '李克特量表',
    'userpilot-embed-section': '嵌入内容',
    'userpilot-node-button': '节点按钮'
  };

  return displayNames[sectionType] || '未知区块';
};

/**
 * 检查区块是否可编辑
 * @param {HTMLElement} section - 区块元素
 * @returns {boolean} 是否可编辑
 */
export const isSectionEditable = (section) => {
  if (!isValidSection(section)) return false;

  // 检查是否有禁用编辑的属性
  if (section.hasAttribute('data-readonly') || 
      section.hasAttribute('data-disabled')) {
    return false;
  }

  // 检查父元素是否禁用编辑
  let parent = section.parentElement;
  while (parent) {
    if (parent.hasAttribute('data-readonly') || 
        parent.hasAttribute('data-disabled')) {
      return false;
    }
    parent = parent.parentElement;
  }

  return true;
};

/**
 * 获取区块的唯一ID
 * @param {HTMLElement} section - 区块元素
 * @returns {string|null} 区块ID
 */
export const getSectionId = (section) => {
  return section?.getAttribute('unit_id') || 
         section?.id || 
         null;
};

/**
 * 设置区块ID
 * @param {HTMLElement} section - 区块元素
 * @param {string} id - 要设置的ID
 */
export const setSectionId = (section, id) => {
  if (section && id) {
    section.setAttribute('unit_id', id);
  }
};

/**
 * 生成新的区块ID
 * @param {string} prefix - ID前缀
 * @returns {string} 新的区块ID
 */
export const generateSectionId = (prefix = 'section') => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return `${prefix}_${timestamp}_${random}`;
};

/**
 * 克隆区块元素
 * @param {HTMLElement} section - 要克隆的区块
 * @param {boolean} generateNewId - 是否生成新ID
 * @returns {HTMLElement} 克隆的区块
 */
export const cloneSection = (section, generateNewId = true) => {
  if (!isValidSection(section)) {
    throw new Error('Invalid section element');
  }

  const cloned = section.cloneNode(true);

  if (generateNewId) {
    const sectionType = getSectionType(section);
    const newId = generateSectionId(sectionType.replace('userpilot-', ''));
    setSectionId(cloned, newId);
  }

  return cloned;
};

/**
 * 获取区块的父容器
 * @param {HTMLElement} section - 区块元素
 * @returns {HTMLElement|null} 父容器
 */
export const getSectionContainer = (section) => {
  if (!section) return null;

  let parent = section.parentElement;
  while (parent) {
    if (parent.classList.contains('userpilot-content') ||
        parent.classList.contains('userpilot-slide') ||
        parent.classList.contains('userpilot-builder')) {
      return parent;
    }
    parent = parent.parentElement;
  }

  return null;
};

/**
 * 获取区块在容器中的索引
 * @param {HTMLElement} section - 区块元素
 * @returns {number} 索引位置，-1表示未找到
 */
export const getSectionIndex = (section) => {
  if (!section || !section.parentElement) return -1;

  const siblings = Array.from(section.parentElement.children);
  return siblings.indexOf(section);
};

/**
 * 移动区块到指定位置
 * @param {HTMLElement} section - 要移动的区块
 * @param {number} newIndex - 新的索引位置
 * @returns {boolean} 是否移动成功
 */
export const moveSectionToIndex = (section, newIndex) => {
  if (!section || !section.parentElement) return false;

  const parent = section.parentElement;
  const siblings = Array.from(parent.children);
  const currentIndex = siblings.indexOf(section);

  if (currentIndex === -1 || newIndex < 0 || newIndex >= siblings.length) {
    return false;
  }

  if (currentIndex === newIndex) return true;

  // 移除元素
  section.remove();

  // 插入到新位置
  if (newIndex >= siblings.length - 1) {
    parent.appendChild(section);
  } else {
    const referenceNode = siblings[newIndex];
    parent.insertBefore(section, referenceNode);
  }

  return true;
};

/**
 * 检查区块是否在列布局中
 * @param {HTMLElement} section - 区块元素
 * @returns {boolean} 是否在列布局中
 */
export const isInColumnLayout = (section) => {
  return section?.parentElement?.classList.contains('userpilot-cols-section') || false;
};

/**
 * 获取列布局中的区块数量
 * @param {HTMLElement} section - 区块元素
 * @returns {number} 列中的区块数量
 */
export const getColumnSectionCount = (section) => {
  if (!isInColumnLayout(section)) return 0;
  return section.parentElement.children.length;
};

/**
 * 检查是否可以在列中添加更多区块
 * @param {HTMLElement} section - 区块元素
 * @param {number} maxColumns - 最大列数，默认为3
 * @returns {boolean} 是否可以添加
 */
export const canAddMoreColumnsections = (section, maxColumns = 3) => {
  if (!isInColumnLayout(section)) return true;
  return getColumnSectionCount(section) < maxColumns;
};

/**
 * 获取区块的样式信息
 * @param {HTMLElement} section - 区块元素
 * @param {Window} iframeWindow - iframe窗口对象
 * @returns {Object} 样式信息
 */
export const getSectionStyles = (section, iframeWindow) => {
  if (!section || !iframeWindow) return {};

  const computedStyle = iframeWindow.getComputedStyle(section);
  
  return {
    width: computedStyle.width,
    height: computedStyle.height,
    padding: computedStyle.padding,
    margin: computedStyle.margin,
    backgroundColor: computedStyle.backgroundColor,
    borderRadius: computedStyle.borderRadius,
    border: computedStyle.border,
    display: computedStyle.display,
    position: computedStyle.position,
    zIndex: computedStyle.zIndex
  };
};

/**
 * 应用样式到区块
 * @param {HTMLElement} section - 区块元素
 * @param {Object} styles - 要应用的样式
 */
export const applySectionStyles = (section, styles) => {
  if (!section || !styles) return;

  Object.entries(styles).forEach(([property, value]) => {
    if (value !== null && value !== undefined) {
      section.style[property] = value;
    }
  });
};

/**
 * 重置区块样式
 * @param {HTMLElement} section - 区块元素
 * @param {Array} properties - 要重置的样式属性，如果为空则重置所有
 */
export const resetSectionStyles = (section, properties = []) => {
  if (!section) return;

  if (properties.length === 0) {
    section.removeAttribute('style');
  } else {
    properties.forEach(property => {
      section.style[property] = '';
    });
  }
};

/**
 * 验证区块内容
 * @param {HTMLElement} section - 区块元素
 * @returns {Object} 验证结果
 */
export const validateSectionContent = (section) => {
  const result = {
    isValid: true,
    errors: [],
    warnings: []
  };

  if (!isValidSection(section)) {
    result.isValid = false;
    result.errors.push('无效的区块元素');
    return result;
  }

  const sectionType = getSectionType(section);
  
  switch (sectionType) {
    case 'userpilot-text-section':
    case 'userpilot-header-section':
      const textContent = section.textContent?.trim();
      if (!textContent || textContent === '\u200B') {
        result.warnings.push('文本内容为空');
      }
      break;
      
    case 'userpilot-image-section':
      const img = section.querySelector('img');
      if (!img || !img.src) {
        result.isValid = false;
        result.errors.push('图片缺少源地址');
      }
      break;
      
    case 'userpilot-button-section':
    case 'userpilot-node-button':
      const button = section.querySelector('.userpilot-btn');
      if (!button || !button.textContent?.trim()) {
        result.warnings.push('按钮文本为空');
      }
      break;
  }

  return result;
};

/**
 * 区块工具函数的调试工具
 */
export const sectionDebugTools = {
  /**
   * 打印区块信息
   * @param {HTMLElement} section - 区块元素
   */
  logSectionInfo: (section) => {
    console.group('Section Debug Info');
    console.log('Element:', section);
    console.log('Type:', getSectionType(section));
    console.log('Display Name:', getSectionDisplayName(getSectionType(section)));
    console.log('ID:', getSectionId(section));
    console.log('Is Valid:', isValidSection(section));
    console.log('Is Editable:', isSectionEditable(section));
    console.log('Is In Column:', isInColumnLayout(section));
    console.log('Index:', getSectionIndex(section));
    console.log('Validation:', validateSectionContent(section));
    console.groupEnd();
  },

  /**
   * 列出容器中的所有区块
   * @param {HTMLElement} container - 容器元素
   */
  listSectionsInContainer: (container) => {
    if (!container) return;

    const sections = Array.from(container.children).filter(isValidSection);
    console.table(sections.map(section => ({
      id: getSectionId(section),
      type: getSectionType(section),
      displayName: getSectionDisplayName(getSectionType(section)),
      index: getSectionIndex(section),
      editable: isSectionEditable(section)
    })));
  }
};
