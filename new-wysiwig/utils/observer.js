/**
 * 观察者工具
 * 函数式实现的DOM变化观察器
 */

/**
 * 创建观察者
 * @param {Object} state - 编辑器状态
 * @returns {Object} 观察者对象
 */
export const createObserver = (state) => {
  let mutationObserver = null;
  let isObserving = false;
  let currentTarget = null;

  // 默认观察配置
  const defaultConfig = {
    attributes: true,
    childList: true,
    characterData: true,
    subtree: true
  };

  /**
   * 开始观察元素
   * @param {HTMLElement} target - 要观察的目标元素
   * @param {Object} config - 观察配置
   */
  const observe = (target, config = defaultConfig) => {
    if (!target) {
      console.error('Observer: Target element is required');
      return;
    }

    // 如果已经在观察，先停止
    if (isObserving) {
      disconnect();
    }

    currentTarget = target;

    // 创建新的观察者
    mutationObserver = new MutationObserver((mutations) => {
      handleMutations(mutations);
    });

    // 延迟开始观察，避免初始化时的变化
    setTimeout(() => {
      if (mutationObserver && currentTarget) {
        mutationObserver.observe(currentTarget, config);
        isObserving = true;
      }
    }, 100);
  };

  /**
   * 停止观察
   */
  const disconnect = () => {
    if (mutationObserver) {
      mutationObserver.disconnect();
      mutationObserver = null;
    }
    isObserving = false;
    currentTarget = null;
  };

  /**
   * 处理DOM变化
   * @param {Array} mutations - 变化列表
   */
  const handleMutations = (mutations) => {
    // 过滤掉不重要的变化
    const significantMutations = mutations.filter(isSignificantMutation);
    
    if (significantMutations.length === 0) return;

    // 延迟处理，避免频繁触发
    debounce(() => {
      onMutationsDetected(significantMutations);
    }, 100)();
  };

  /**
   * 检查是否为重要的变化
   * @param {MutationRecord} mutation - 变化记录
   * @returns {boolean} 是否重要
   */
  const isSignificantMutation = (mutation) => {
    // 忽略某些属性变化
    if (mutation.type === 'attributes') {
      const ignoredAttributes = ['style', 'class', 'data-temp'];
      return !ignoredAttributes.includes(mutation.attributeName);
    }

    // 忽略空白文本节点的变化
    if (mutation.type === 'characterData') {
      const text = mutation.target.textContent?.trim();
      return text && text !== '\u200B'; // 忽略零宽度空格
    }

    // 忽略临时元素的添加/删除
    if (mutation.type === 'childList') {
      const hasSignificantNodes = [...mutation.addedNodes, ...mutation.removedNodes]
        .some(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            return !node.classList.contains('temp-element') &&
                   !node.hasAttribute('data-temp');
          }
          if (node.nodeType === Node.TEXT_NODE) {
            const text = node.textContent?.trim();
            return text && text !== '\u200B';
          }
          return false;
        });
      
      return hasSignificantNodes;
    }

    return true;
  };

  /**
   * 当检测到变化时的处理函数
   * @param {Array} mutations - 重要的变化列表
   */
  const onMutationsDetected = (mutations) => {
    // 触发工具栏重新定位
    if (state.wysiwygToolbar && typeof state.wysiwygToolbar.reposition === 'function') {
      state.wysiwygToolbar.reposition();
    }

    // 触发自定义事件
    triggerMutationEvent(mutations);

    // 如果有活动的编辑器，通知它内容已变化
    if (state.activeBuilder && typeof state.activeBuilder.onContentChanged === 'function') {
      state.activeBuilder.onContentChanged(mutations);
    }
  };

  /**
   * 触发变化事件
   * @param {Array} mutations - 变化列表
   */
  const triggerMutationEvent = (mutations) => {
    const event = new CustomEvent('wysiwyg-content-changed', {
      detail: {
        mutations,
        target: currentTarget,
        timestamp: Date.now()
      }
    });

    if (state.userpilotContainer) {
      state.userpilotContainer.dispatchEvent(event);
    }
  };

  /**
   * 暂停观察
   */
  const pause = () => {
    if (mutationObserver && isObserving) {
      mutationObserver.disconnect();
      isObserving = false;
    }
  };

  /**
   * 恢复观察
   */
  const resume = () => {
    if (mutationObserver && currentTarget && !isObserving) {
      mutationObserver.observe(currentTarget, defaultConfig);
      isObserving = true;
    }
  };

  /**
   * 临时忽略变化
   * @param {Function} callback - 在忽略期间执行的回调
   */
  const ignoreChanges = async (callback) => {
    if (typeof callback !== 'function') return;

    const wasObserving = isObserving;
    
    if (wasObserving) {
      pause();
    }

    try {
      await callback();
    } finally {
      // 延迟恢复观察，确保所有变化都已完成
      setTimeout(() => {
        if (wasObserving) {
          resume();
        }
      }, 50);
    }
  };

  /**
   * 获取观察状态
   * @returns {Object} 观察状态信息
   */
  const getStatus = () => {
    return {
      isObserving,
      hasTarget: !!currentTarget,
      targetElement: currentTarget,
      observerExists: !!mutationObserver
    };
  };

  // 观察者对象
  const observer = {
    observe,
    disconnect,
    pause,
    resume,
    ignoreChanges,
    getStatus,
    
    // 访问器
    get isObserving() { return isObserving; },
    get currentTarget() { return currentTarget; }
  };

  return observer;
};

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export const throttle = (func, limit) => {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * 创建特定类型的观察者
 */
export const createSpecializedObservers = {
  /**
   * 创建文本变化观察者
   * @param {HTMLElement} target - 目标元素
   * @param {Function} callback - 变化回调
   * @returns {Object} 观察者对象
   */
  textObserver: (target, callback) => {
    const observer = new MutationObserver((mutations) => {
      const textMutations = mutations.filter(m => 
        m.type === 'characterData' || 
        (m.type === 'childList' && hasTextChanges(m))
      );
      
      if (textMutations.length > 0) {
        callback(textMutations);
      }
    });

    observer.observe(target, {
      characterData: true,
      childList: true,
      subtree: true
    });

    return {
      disconnect: () => observer.disconnect()
    };
  },

  /**
   * 创建属性变化观察者
   * @param {HTMLElement} target - 目标元素
   * @param {Array} attributes - 要观察的属性列表
   * @param {Function} callback - 变化回调
   * @returns {Object} 观察者对象
   */
  attributeObserver: (target, attributes, callback) => {
    const observer = new MutationObserver((mutations) => {
      const attrMutations = mutations.filter(m => 
        m.type === 'attributes' && 
        attributes.includes(m.attributeName)
      );
      
      if (attrMutations.length > 0) {
        callback(attrMutations);
      }
    });

    observer.observe(target, {
      attributes: true,
      attributeFilter: attributes
    });

    return {
      disconnect: () => observer.disconnect()
    };
  },

  /**
   * 创建子元素变化观察者
   * @param {HTMLElement} target - 目标元素
   * @param {Function} callback - 变化回调
   * @returns {Object} 观察者对象
   */
  childListObserver: (target, callback) => {
    const observer = new MutationObserver((mutations) => {
      const childMutations = mutations.filter(m => m.type === 'childList');
      
      if (childMutations.length > 0) {
        callback(childMutations);
      }
    });

    observer.observe(target, {
      childList: true
    });

    return {
      disconnect: () => observer.disconnect()
    };
  }
};

/**
 * 检查变化是否包含文本更改
 * @param {MutationRecord} mutation - 变化记录
 * @returns {boolean} 是否包含文本更改
 */
const hasTextChanges = (mutation) => {
  const checkNode = (node) => {
    if (node.nodeType === Node.TEXT_NODE) {
      return true;
    }
    if (node.nodeType === Node.ELEMENT_NODE) {
      return Array.from(node.childNodes).some(checkNode);
    }
    return false;
  };

  return [...mutation.addedNodes, ...mutation.removedNodes].some(checkNode);
};

/**
 * 观察者调试工具
 */
export const observerDebugTools = {
  /**
   * 记录所有变化
   * @param {HTMLElement} target - 目标元素
   * @param {number} duration - 记录持续时间（毫秒）
   */
  logAllMutations: (target, duration = 10000) => {
    const mutations = [];
    
    const observer = new MutationObserver((mutationList) => {
      mutations.push(...mutationList.map(m => ({
        type: m.type,
        target: m.target,
        attributeName: m.attributeName,
        addedNodes: Array.from(m.addedNodes),
        removedNodes: Array.from(m.removedNodes),
        timestamp: Date.now()
      })));
    });

    observer.observe(target, {
      attributes: true,
      childList: true,
      characterData: true,
      subtree: true
    });

    setTimeout(() => {
      observer.disconnect();
      console.table(mutations);
    }, duration);
  },

  /**
   * 监控观察者性能
   * @param {Object} observerInstance - 观察者实例
   */
  monitorPerformance: (observerInstance) => {
    const startTime = performance.now();
    let mutationCount = 0;

    const originalObserve = observerInstance.observe;
    observerInstance.observe = function(...args) {
      console.log('Observer started at:', new Date().toISOString());
      return originalObserve.apply(this, args);
    };

    // 这里需要访问内部的mutation handler来计数
    // 实际实现可能需要更复杂的代理模式
    
    setInterval(() => {
      const elapsed = performance.now() - startTime;
      console.log(`Observer performance: ${mutationCount} mutations in ${elapsed.toFixed(2)}ms`);
    }, 5000);
  }
};
