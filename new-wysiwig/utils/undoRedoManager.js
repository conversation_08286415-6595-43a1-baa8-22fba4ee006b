/**
 * 撤销重做管理器
 * 函数式实现的撤销重做功能
 */

/**
 * 创建撤销重做管理器
 * @param {Object} editor - 编辑器实例
 * @param {string} type - 管理器类型 ('text', 'button', 'custom')
 * @returns {Object} 撤销重做管理器对象
 */
export const createUndoRedoManager = (editor, type = 'text') => {
  const { iframeDocument, sectionContainer } = editor;
  
  // 状态栈
  let undoStack = [];
  let redoStack = [];
  let maxStackSize = 50;
  let isReady = true;
  
  // 变化类型
  const MUTATION_TYPES = {
    NATIVE: 'NATIVE',
    CUSTOM: 'CUSTOM',
    BUTTON_STYLE: 'BUTTON_STYLE',
    SECTION_STYLE: 'SECTION_STYLE'
  };

  // 观察器配置
  const observerConfig = {
    attributes: true,
    characterData: true,
    childList: true,
    subtree: true
  };

  let mutationObserver = null;
  let keyboardListener = null;
  let saveTimeout = null;

  /**
   * 初始化管理器
   */
  const init = () => {
    initMutationObserver();
    initKeyboardListener();
    
    // 保存初始状态
    saveInitialState();
  };

  /**
   * 初始化变化观察器
   */
  const initMutationObserver = () => {
    mutationObserver = new MutationObserver((mutations) => {
      handleMutations(mutations);
    });

    // 延迟开始观察，避免初始化时的变化
    setTimeout(() => {
      if (mutationObserver && sectionContainer) {
        mutationObserver.observe(sectionContainer, observerConfig);
      }
    }, 500);
  };

  /**
   * 初始化键盘监听器
   */
  const initKeyboardListener = () => {
    keyboardListener = (event) => {
      handleKeyboardEvent(event);
    };

    iframeDocument.addEventListener('keydown', keyboardListener, true);
    iframeDocument.addEventListener('keyup', keyboardListener, true);
  };

  /**
   * 处理变化
   * @param {Array} mutations - 变化列表
   */
  const handleMutations = (mutations) => {
    // 过滤掉不重要的变化
    const significantMutations = mutations.filter(isSignificantMutation);
    
    if (significantMutations.length === 0) return;

    // 防抖保存
    clearTimeout(saveTimeout);
    saveTimeout = setTimeout(() => {
      if (significantMutations.some(m => m.type === 'childList' || m.type === 'attributes')) {
        return; // 忽略结构性变化
      }
      saveForUndo(MUTATION_TYPES.NATIVE);
    }, 500);
  };

  /**
   * 检查是否为重要变化
   * @param {MutationRecord} mutation - 变化记录
   * @returns {boolean} 是否重要
   */
  const isSignificantMutation = (mutation) => {
    // 忽略临时属性变化
    if (mutation.type === 'attributes') {
      const ignoredAttributes = ['data-temp', 'style', 'class'];
      return !ignoredAttributes.includes(mutation.attributeName);
    }

    // 只关注文本内容变化
    if (mutation.type === 'characterData') {
      return true;
    }

    return false;
  };

  /**
   * 处理键盘事件
   * @param {KeyboardEvent} event - 键盘事件
   */
  const handleKeyboardEvent = (event) => {
    const { ctrlKey, metaKey, shiftKey, key, type } = event;
    
    // 检测撤销重做快捷键
    const isUndo = (ctrlKey || metaKey) && !shiftKey && key === 'z' && type === 'keydown';
    const isRedo = (ctrlKey || metaKey) && (shiftKey && key === 'z' || key === 'y') && type === 'keydown';

    if (isUndo) {
      event.preventDefault();
      performUndo();
      return false;
    }

    if (isRedo) {
      event.preventDefault();
      performRedo();
      return false;
    }

    return true;
  };

  /**
   * 保存初始状态
   */
  const saveInitialState = () => {
    const state = captureCurrentState();
    undoStack.push(state);
  };

  /**
   * 保存状态用于撤销
   * @param {string} mutationType - 变化类型
   */
  const saveForUndo = (mutationType) => {
    if (!isReady) return;

    isReady = false;

    setTimeout(() => {
      const state = captureCurrentState();
      state.type = mutationType;
      
      // 添加到撤销栈
      undoStack.push(state);
      
      // 限制栈大小
      if (undoStack.length > maxStackSize) {
        undoStack.shift();
      }
      
      // 清空重做栈
      redoStack = [];
      
      isReady = true;
    }, 300);
  };

  /**
   * 捕获当前状态
   * @returns {Object} 当前状态
   */
  const captureCurrentState = () => {
    const state = {
      timestamp: Date.now(),
      content: sectionContainer.innerHTML,
      type: MUTATION_TYPES.NATIVE
    };

    // 根据编辑器类型捕获额外状态
    switch (type) {
      case 'button':
        if (editor.button) {
          state.buttonStyle = editor.button.style.cssText;
        }
        state.sectionStyle = sectionContainer.style.cssText;
        break;
        
      case 'text':
        // 捕获光标位置
        if (editor.wysiwygOperations) {
          const caretPos = editor.wysiwygOperations.getCaretPosition();
          if (caretPos) {
            state.caretPosition = {
              x: caretPos.x,
              y: caretPos.y
            };
          }
        }
        break;
    }

    return state;
  };

  /**
   * 恢复状态
   * @param {Object} state - 要恢复的状态
   */
  const restoreState = (state) => {
    if (!state) return;

    // 暂停观察器
    if (mutationObserver) {
      mutationObserver.disconnect();
    }

    try {
      // 恢复内容
      sectionContainer.innerHTML = state.content;

      // 根据类型恢复额外状态
      switch (type) {
        case 'button':
          if (state.buttonStyle && editor.button) {
            editor.button.style.cssText = state.buttonStyle;
          }
          if (state.sectionStyle) {
            sectionContainer.style.cssText = state.sectionStyle;
          }
          break;
          
        case 'text':
          // 恢复光标位置
          if (state.caretPosition && editor.wysiwygOperations) {
            setTimeout(() => {
              editor.wysiwygOperations.setCaretPosition(
                state.caretPosition.x,
                state.caretPosition.y
              );
            }, 50);
          }
          break;
      }

      // 触发恢复事件
      triggerRestoreEvent(state);

    } finally {
      // 重新启动观察器
      setTimeout(() => {
        if (mutationObserver && sectionContainer) {
          mutationObserver.observe(sectionContainer, observerConfig);
        }
      }, 100);
    }
  };

  /**
   * 执行撤销
   */
  const performUndo = () => {
    if (undoStack.length <= 1) return; // 保留初始状态

    const currentState = undoStack.pop();
    redoStack.push(currentState);

    const previousState = undoStack[undoStack.length - 1];
    restoreState(previousState);
  };

  /**
   * 执行重做
   */
  const performRedo = () => {
    if (redoStack.length === 0) return;

    const nextState = redoStack.pop();
    undoStack.push(nextState);

    restoreState(nextState);
  };

  /**
   * 触发恢复事件
   * @param {Object} state - 恢复的状态
   */
  const triggerRestoreEvent = (state) => {
    const event = new CustomEvent('wysiwyg-state-restored', {
      detail: {
        state,
        timestamp: Date.now(),
        editorType: type
      }
    });

    sectionContainer.dispatchEvent(event);
  };

  /**
   * 清空历史
   */
  const clearHistory = () => {
    undoStack = [];
    redoStack = [];
    saveInitialState();
  };

  /**
   * 获取历史状态
   * @returns {Object} 历史状态信息
   */
  const getHistoryState = () => {
    return {
      undoCount: Math.max(0, undoStack.length - 1),
      redoCount: redoStack.length,
      canUndo: undoStack.length > 1,
      canRedo: redoStack.length > 0,
      totalStates: undoStack.length + redoStack.length
    };
  };

  /**
   * 设置最大栈大小
   * @param {number} size - 最大大小
   */
  const setMaxStackSize = (size) => {
    maxStackSize = Math.max(1, size);
    
    // 如果当前栈超过新的限制，裁剪它
    while (undoStack.length > maxStackSize) {
      undoStack.shift();
    }
    while (redoStack.length > maxStackSize) {
      redoStack.shift();
    }
  };

  /**
   * 手动保存状态
   * @param {string} mutationType - 变化类型
   */
  const manualSave = (mutationType = MUTATION_TYPES.CUSTOM) => {
    saveForUndo(mutationType);
  };

  /**
   * 暂停自动保存
   */
  const pauseAutoSave = () => {
    if (mutationObserver) {
      mutationObserver.disconnect();
    }
  };

  /**
   * 恢复自动保存
   */
  const resumeAutoSave = () => {
    if (mutationObserver && sectionContainer) {
      mutationObserver.observe(sectionContainer, observerConfig);
    }
  };

  /**
   * 销毁管理器
   */
  const destroy = () => {
    // 清理观察器
    if (mutationObserver) {
      mutationObserver.disconnect();
      mutationObserver = null;
    }

    // 清理键盘监听器
    if (keyboardListener) {
      iframeDocument.removeEventListener('keydown', keyboardListener, true);
      iframeDocument.removeEventListener('keyup', keyboardListener, true);
      keyboardListener = null;
    }

    // 清理定时器
    if (saveTimeout) {
      clearTimeout(saveTimeout);
      saveTimeout = null;
    }

    // 清空栈
    undoStack = [];
    redoStack = [];
  };

  // 初始化管理器
  init();

  // 撤销重做管理器对象
  const manager = {
    // 核心操作
    performUndo,
    performRedo,
    manualSave,
    clearHistory,
    
    // 状态管理
    getHistoryState,
    setMaxStackSize,
    
    // 控制
    pauseAutoSave,
    resumeAutoSave,
    destroy,
    
    // 访问器
    get canUndo() { return undoStack.length > 1; },
    get canRedo() { return redoStack.length > 0; },
    get undoCount() { return Math.max(0, undoStack.length - 1); },
    get redoCount() { return redoStack.length; },
    
    // 常量
    MUTATION_TYPES
  };

  return manager;
};

/**
 * 撤销重做管理器调试工具
 */
export const undoRedoDebugTools = {
  /**
   * 记录管理器状态
   * @param {Object} manager - 管理器实例
   */
  logManagerState: (manager) => {
    const state = manager.getHistoryState();
    console.group('Undo/Redo Manager Debug');
    console.log('Can Undo:', manager.canUndo);
    console.log('Can Redo:', manager.canRedo);
    console.log('Undo Count:', state.undoCount);
    console.log('Redo Count:', state.redoCount);
    console.log('Total States:', state.totalStates);
    console.groupEnd();
  },

  /**
   * 监控管理器性能
   * @param {Object} manager - 管理器实例
   */
  monitorPerformance: (manager) => {
    const startTime = performance.now();
    let operationCount = 0;

    // 代理核心方法来计数操作
    const originalUndo = manager.performUndo;
    const originalRedo = manager.performRedo;
    const originalSave = manager.manualSave;

    manager.performUndo = function(...args) {
      operationCount++;
      console.log(`Undo operation #${operationCount}`);
      return originalUndo.apply(this, args);
    };

    manager.performRedo = function(...args) {
      operationCount++;
      console.log(`Redo operation #${operationCount}`);
      return originalRedo.apply(this, args);
    };

    manager.manualSave = function(...args) {
      operationCount++;
      console.log(`Save operation #${operationCount}`);
      return originalSave.apply(this, args);
    };

    // 定期报告性能
    setInterval(() => {
      const elapsed = performance.now() - startTime;
      console.log(`Undo/Redo Performance: ${operationCount} operations in ${elapsed.toFixed(2)}ms`);
    }, 10000);
  }
};
