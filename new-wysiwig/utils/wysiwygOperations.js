/**
 * WYSIWYG操作工具
 * 函数式实现的所见即所得编辑操作
 */

/**
 * 创建WYSIWYG操作工具
 * @param {Object} editor - 编辑器实例
 * @returns {Object} WYSIWYG操作对象
 */
export const createWysiwygOperations = (editor) => {
  const { iframeDocument, iframeWindow, sectionContainer } = editor;
  
  let currentRange = null;
  let selection = null;

  /**
   * 初始化选择对象
   */
  const initSelection = () => {
    selection = iframeWindow.getSelection();
    return selection;
  };

  /**
   * 获取当前选择
   * @returns {Selection} 选择对象
   */
  const getSelection = () => {
    if (!selection) {
      initSelection();
    }
    return selection;
  };

  /**
   * 获取当前范围
   * @returns {Range|null} 当前范围
   */
  const getCurrentRange = () => {
    const sel = getSelection();
    if (sel.rangeCount > 0) {
      currentRange = sel.getRangeAt(0);
      return currentRange;
    }
    return null;
  };

  /**
   * 设置当前范围
   * @param {Range} range - 要设置的范围
   */
  const setCurrentRange = (range) => {
    if (!range) return;
    
    currentRange = range;
    const sel = getSelection();
    sel.removeAllRanges();
    sel.addRange(range);
  };

  /**
   * 重置区块范围
   */
  const resetSectionRange = () => {
    currentRange = null;
  };

  /**
   * 在节点上折叠光标
   * @param {Node} node - 目标节点
   * @param {boolean} toStart - 是否折叠到开始位置
   */
  const collapseCaretOnNode = (node, toStart = false) => {
    if (!node) return;

    const range = iframeDocument.createRange();
    const sel = getSelection();

    try {
      if (node.nodeType === Node.TEXT_NODE) {
        const position = toStart ? 0 : node.textContent.length;
        range.setStart(node, position);
        range.setEnd(node, position);
      } else {
        if (toStart) {
          range.setStartBefore(node);
          range.setEndBefore(node);
        } else {
          range.setStartAfter(node);
          range.setEndAfter(node);
        }
      }

      sel.removeAllRanges();
      sel.addRange(range);
      currentRange = range;
    } catch (error) {
      console.warn('Failed to set caret position:', error);
    }
  };

  /**
   * 选择节点内容
   * @param {Node} node - 目标节点
   */
  const selectNodeContents = (node) => {
    if (!node) return;

    const range = iframeDocument.createRange();
    const sel = getSelection();

    try {
      range.selectNodeContents(node);
      sel.removeAllRanges();
      sel.addRange(range);
      currentRange = range;
    } catch (error) {
      console.warn('Failed to select node contents:', error);
    }
  };

  /**
   * 插入HTML内容
   * @param {string} html - 要插入的HTML
   */
  const insertHTML = (html) => {
    const range = getCurrentRange();
    if (!range) return;

    try {
      range.deleteContents();
      
      const fragment = range.createContextualFragment(html);
      range.insertNode(fragment);
      
      // 移动光标到插入内容之后
      range.collapse(false);
      setCurrentRange(range);
    } catch (error) {
      console.warn('Failed to insert HTML:', error);
    }
  };

  /**
   * 插入文本内容
   * @param {string} text - 要插入的文本
   */
  const insertText = (text) => {
    const range = getCurrentRange();
    if (!range) return;

    try {
      range.deleteContents();
      
      const textNode = iframeDocument.createTextNode(text);
      range.insertNode(textNode);
      
      // 移动光标到文本之后
      range.setStartAfter(textNode);
      range.collapse(true);
      setCurrentRange(range);
    } catch (error) {
      console.warn('Failed to insert text:', error);
    }
  };

  /**
   * 获取选中的文本
   * @returns {string} 选中的文本
   */
  const getSelectedText = () => {
    const sel = getSelection();
    return sel.toString();
  };

  /**
   * 获取选中的HTML
   * @returns {string} 选中的HTML
   */
  const getSelectedHTML = () => {
    const range = getCurrentRange();
    if (!range) return '';

    try {
      const container = iframeDocument.createElement('div');
      container.appendChild(range.cloneContents());
      return container.innerHTML;
    } catch (error) {
      console.warn('Failed to get selected HTML:', error);
      return '';
    }
  };

  /**
   * 包装选中内容
   * @param {string} tagName - 包装标签名
   * @param {Object} attributes - 标签属性
   */
  const wrapSelection = (tagName, attributes = {}) => {
    const range = getCurrentRange();
    if (!range || range.collapsed) return;

    try {
      const wrapper = iframeDocument.createElement(tagName);
      
      // 设置属性
      Object.entries(attributes).forEach(([key, value]) => {
        wrapper.setAttribute(key, value);
      });

      // 包装选中内容
      wrapper.appendChild(range.extractContents());
      range.insertNode(wrapper);
      
      // 选择包装后的内容
      selectNodeContents(wrapper);
    } catch (error) {
      console.warn('Failed to wrap selection:', error);
    }
  };

  /**
   * 移除包装
   * @param {string} tagName - 要移除的标签名
   */
  const unwrapSelection = (tagName) => {
    const range = getCurrentRange();
    if (!range) return;

    try {
      let element = range.commonAncestorContainer;
      
      // 如果是文本节点，获取父元素
      if (element.nodeType === Node.TEXT_NODE) {
        element = element.parentElement;
      }

      // 查找要移除的标签
      while (element && element !== sectionContainer) {
        if (element.tagName && element.tagName.toLowerCase() === tagName.toLowerCase()) {
          // 移除包装，保留内容
          const parent = element.parentNode;
          while (element.firstChild) {
            parent.insertBefore(element.firstChild, element);
          }
          parent.removeChild(element);
          break;
        }
        element = element.parentElement;
      }
    } catch (error) {
      console.warn('Failed to unwrap selection:', error);
    }
  };

  /**
   * 执行格式化命令
   * @param {string} command - 命令名称
   * @param {string} value - 命令值
   * @returns {boolean} 是否执行成功
   */
  const execCommand = (command, value = null) => {
    try {
      return iframeDocument.execCommand(command, false, value);
    } catch (error) {
      console.warn('Failed to execute command:', command, error);
      return false;
    }
  };

  /**
   * 查询命令状态
   * @param {string} command - 命令名称
   * @returns {boolean} 命令状态
   */
  const queryCommandState = (command) => {
    try {
      return iframeDocument.queryCommandState(command);
    } catch (error) {
      console.warn('Failed to query command state:', command, error);
      return false;
    }
  };

  /**
   * 查询命令值
   * @param {string} command - 命令名称
   * @returns {string} 命令值
   */
  const queryCommandValue = (command) => {
    try {
      return iframeDocument.queryCommandValue(command);
    } catch (error) {
      console.warn('Failed to query command value:', command, error);
      return '';
    }
  };

  /**
   * 处理退格键
   * @param {KeyboardEvent} event - 键盘事件
   * @returns {boolean} 是否阻止默认行为
   */
  const handleBackspaceKey = (event) => {
    const range = getCurrentRange();
    if (!range) return true;

    // 如果在块的开始位置，阻止删除整个块
    if (range.collapsed && isAtBlockStart(range)) {
      event.preventDefault();
      return false;
    }

    return true;
  };

  /**
   * 检查是否在块的开始位置
   * @param {Range} range - 范围对象
   * @returns {boolean} 是否在开始位置
   */
  const isAtBlockStart = (range) => {
    if (!range.collapsed) return false;

    const container = range.startContainer;
    const offset = range.startOffset;

    // 如果在文本节点的开始
    if (container.nodeType === Node.TEXT_NODE) {
      return offset === 0 && isFirstTextNode(container);
    }

    // 如果在元素的开始
    return offset === 0;
  };

  /**
   * 检查是否为第一个文本节点
   * @param {Node} textNode - 文本节点
   * @returns {boolean} 是否为第一个
   */
  const isFirstTextNode = (textNode) => {
    let node = textNode;
    while (node.previousSibling) {
      node = node.previousSibling;
      if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
        return false;
      }
      if (node.nodeType === Node.ELEMENT_NODE) {
        return false;
      }
    }
    return true;
  };

  /**
   * 移除零宽度空格
   */
  const removeZeroWidthSpace = () => {
    const walker = iframeDocument.createTreeWalker(
      sectionContainer,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );

    const textNodes = [];
    let node;
    while (node = walker.nextNode()) {
      textNodes.push(node);
    }

    textNodes.forEach(textNode => {
      if (textNode.textContent.includes('\u200B')) {
        textNode.textContent = textNode.textContent.replace(/\u200B/g, '');
        
        // 如果文本节点变空，移除它
        if (!textNode.textContent.trim()) {
          textNode.parentNode?.removeChild(textNode);
        }
      }
    });
  };

  /**
   * 清理空元素
   */
  const cleanEmptyElements = () => {
    const emptyElements = sectionContainer.querySelectorAll('*:empty');
    emptyElements.forEach(element => {
      // 保留某些可能为空但有意义的元素
      const keepEmpty = ['br', 'img', 'input', 'hr', 'area', 'base', 'col', 'embed', 'link', 'meta', 'source', 'track', 'wbr'];
      if (!keepEmpty.includes(element.tagName.toLowerCase())) {
        element.remove();
      }
    });
  };

  /**
   * 标准化内容结构
   */
  const normalizeContent = () => {
    // 移除零宽度空格
    removeZeroWidthSpace();
    
    // 清理空元素
    cleanEmptyElements();
    
    // 合并相邻的文本节点
    sectionContainer.normalize();
  };

  /**
   * 获取光标位置信息
   * @returns {Object} 位置信息
   */
  const getCaretPosition = () => {
    const range = getCurrentRange();
    if (!range) return null;

    try {
      const rect = range.getBoundingClientRect();
      return {
        x: rect.left,
        y: rect.top,
        width: rect.width,
        height: rect.height,
        range: range.cloneRange()
      };
    } catch (error) {
      console.warn('Failed to get caret position:', error);
      return null;
    }
  };

  /**
   * 设置光标到指定位置
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   */
  const setCaretPosition = (x, y) => {
    try {
      let range;
      
      if (iframeDocument.caretPositionFromPoint) {
        const position = iframeDocument.caretPositionFromPoint(x, y);
        if (position) {
          range = iframeDocument.createRange();
          range.setStart(position.offsetNode, position.offset);
          range.collapse(true);
        }
      } else if (iframeDocument.caretRangeFromPoint) {
        range = iframeDocument.caretRangeFromPoint(x, y);
      }

      if (range) {
        setCurrentRange(range);
      }
    } catch (error) {
      console.warn('Failed to set caret position:', error);
    }
  };

  // WYSIWYG操作对象
  const operations = {
    // 选择相关
    getSelection,
    getCurrentRange,
    setCurrentRange,
    resetSectionRange,
    collapseCaretOnNode,
    selectNodeContents,
    
    // 内容操作
    insertHTML,
    insertText,
    getSelectedText,
    getSelectedHTML,
    wrapSelection,
    unwrapSelection,
    
    // 命令执行
    execCommand,
    queryCommandState,
    queryCommandValue,
    
    // 事件处理
    handleBackspaceKey,
    
    // 内容清理
    removeZeroWidthSpace,
    cleanEmptyElements,
    normalizeContent,
    
    // 光标位置
    getCaretPosition,
    setCaretPosition,
    
    // 工具方法
    isAtBlockStart,
    isFirstTextNode,
    
    // 访问器
    get selection() { return getSelection(); },
    get currentRange() { return currentRange; }
  };

  return operations;
};

/**
 * 检查是否为空文本容器
 * @param {HTMLElement} container - 容器元素
 * @returns {boolean} 是否为空
 */
export const isEmptyTextContainer = (container) => {
  if (!container) return true;
  
  const textContent = container.textContent || container.innerText || '';
  const cleanText = textContent.replace(/\u200B/g, '').trim();
  
  return cleanText.length === 0;
};

/**
 * 获取文本内容（排除零宽度空格）
 * @param {HTMLElement} element - 元素
 * @returns {string} 清理后的文本内容
 */
export const getCleanTextContent = (element) => {
  if (!element) return '';
  
  const textContent = element.textContent || element.innerText || '';
  return textContent.replace(/\u200B/g, '').trim();
};

/**
 * WYSIWYG操作调试工具
 */
export const wysiwygDebugTools = {
  /**
   * 记录选择信息
   * @param {Object} operations - WYSIWYG操作对象
   */
  logSelectionInfo: (operations) => {
    const selection = operations.getSelection();
    const range = operations.getCurrentRange();
    
    console.group('WYSIWYG Selection Debug');
    console.log('Selection:', selection);
    console.log('Range Count:', selection?.rangeCount || 0);
    console.log('Current Range:', range);
    console.log('Selected Text:', operations.getSelectedText());
    console.log('Selected HTML:', operations.getSelectedHTML());
    console.log('Caret Position:', operations.getCaretPosition());
    console.groupEnd();
  },

  /**
   * 高亮当前选择
   * @param {Object} operations - WYSIWYG操作对象
   */
  highlightSelection: (operations) => {
    const range = operations.getCurrentRange();
    if (!range || range.collapsed) return;

    operations.wrapSelection('mark', { 
      style: 'background-color: yellow; color: black;',
      'data-debug-highlight': 'true'
    });
  },

  /**
   * 移除调试高亮
   * @param {HTMLElement} container - 容器元素
   */
  removeDebugHighlights: (container) => {
    const highlights = container.querySelectorAll('[data-debug-highlight]');
    highlights.forEach(highlight => {
      const parent = highlight.parentNode;
      while (highlight.firstChild) {
        parent.insertBefore(highlight.firstChild, highlight);
      }
      parent.removeChild(highlight);
    });
  }
};
