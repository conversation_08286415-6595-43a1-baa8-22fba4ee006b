/**
 * 工具栏视图管理器
 * 函数式实现的工具栏HTML模板管理
 */

/**
 * 创建工具栏视图管理器
 * @returns {Object} 工具栏视图管理器对象
 */
export const createToolbarViews = () => {
  
  /**
   * 获取主工具栏HTML
   * @returns {string} 主工具栏HTML
   */
  const getMainToolbarHTML = () => {
    return `
      <div class="wysiwyg-toolbar-container">
        <div id="section-toolbar" class="section-toolbar">
          <!-- 区块特定工具栏内容将在这里动态插入 -->
        </div>
        <div id="controller-toolbar" class="controller-toolbar">
          ${getControllerToolbarHTML()}
        </div>
      </div>
    `;
  };

  /**
   * 获取控制器工具栏HTML
   * @returns {string} 控制器工具栏HTML
   */
  const getControllerToolbarHTML = () => {
    return `
      <div class="toolbar-controls">
        <button id="save-section" class="toolbar-btn save-btn" label-on-hover="保存更改">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M17,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3M19,19H5V5H16.17L19,7.83V19M12,12A3,3 0 0,0 9,15A3,3 0 0,0 12,18A3,3 0 0,0 15,15A3,3 0 0,0 12,12Z"/>
          </svg>
        </button>
        <button id="discard-section" class="toolbar-btn discard-btn" label-on-hover="撤销更改">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M14.5,9L12,11.5L9.5,9L8,10.5L10.5,13L8,15.5L9.5,17L12,14.5L14.5,17L16,15.5L13.5,13L16,10.5L14.5,9Z"/>
          </svg>
        </button>
        <button id="clone-section" class="toolbar-btn clone-btn" label-on-hover="克隆区块">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/>
          </svg>
        </button>
        <button id="remove-section" class="toolbar-btn remove-btn" label-on-hover="删除区块">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
          </svg>
        </button>
      </div>
    `;
  };

  /**
   * 获取区块特定工具栏HTML
   * @param {string} sectionType - 区块类型
   * @returns {string} 区块工具栏HTML
   */
  const getSectionToolbarHTML = (sectionType) => {
    switch (sectionType) {
      case 'userpilot-header-section':
        return getHeaderToolbarHTML();
      case 'userpilot-text-section':
        return getTextToolbarHTML();
      case 'userpilot-button-section':
        return getButtonToolbarHTML();
      case 'userpilot-image-section':
        return getImageToolbarHTML();
      case 'userpilot-html-section':
        return getHtmlToolbarHTML();
      case 'userpilot-emoji-section':
        return getEmojiToolbarHTML();
      case 'userpilot-input-text':
      case 'userpilot-input-text-large':
      case 'userpilot-input-radio':
      case 'userpilot-input-likert-scale':
        return getInputToolbarHTML();
      case 'userpilot-embed-section':
        return getEmbedToolbarHTML();
      case 'userpilot-node-button':
        return getNodeButtonToolbarHTML();
      default:
        return getDefaultToolbarHTML();
    }
  };

  /**
   * 获取标题工具栏HTML
   * @returns {string} 标题工具栏HTML
   */
  const getHeaderToolbarHTML = () => {
    return `
      <div class="header-toolbar">
        <div class="toolbar-group">
          <label>标题级别:</label>
          <select id="header-level" class="header-level-select">
            <option value="1">H1</option>
            <option value="2" selected>H2</option>
            <option value="3">H3</option>
            <option value="4">H4</option>
            <option value="5">H5</option>
            <option value="6">H6</option>
          </select>
        </div>
        ${getCommonTextStylesHTML()}
      </div>
    `;
  };

  /**
   * 获取文本工具栏HTML
   * @returns {string} 文本工具栏HTML
   */
  const getTextToolbarHTML = () => {
    return `
      <div class="text-toolbar">
        ${getCommonTextStylesHTML()}
        ${getTextFormattingHTML()}
        ${getTextAlignmentHTML()}
      </div>
    `;
  };

  /**
   * 获取按钮工具栏HTML
   * @returns {string} 按钮工具栏HTML
   */
  const getButtonToolbarHTML = () => {
    return `
      <div class="button-toolbar">
        ${getCommonTextStylesHTML()}
        ${getButtonStylesHTML()}
        ${getButtonActionsHTML()}
      </div>
    `;
  };

  /**
   * 获取图片工具栏HTML
   * @returns {string} 图片工具栏HTML
   */
  const getImageToolbarHTML = () => {
    return `
      <div class="image-toolbar">
        <div class="toolbar-group">
          <button id="upload-image" class="toolbar-btn" label-on-hover="上传图片">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
            </svg>
            上传图片
          </button>
          <button id="unsplash-image" class="toolbar-btn" label-on-hover="从Unsplash选择">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"/>
            </svg>
            Unsplash
          </button>
        </div>
        ${getImageStylesHTML()}
      </div>
    `;
  };

  /**
   * 获取HTML工具栏HTML
   * @returns {string} HTML工具栏HTML
   */
  const getHtmlToolbarHTML = () => {
    return `
      <div class="html-toolbar">
        <div class="toolbar-group">
          <button id="edit-html" class="toolbar-btn" label-on-hover="编辑HTML">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M14.6,16.6L19.2,12L14.6,7.4L16,6L22,12L16,18L14.6,16.6M9.4,16.6L4.8,12L9.4,7.4L8,6L2,12L8,18L9.4,16.6Z"/>
            </svg>
            编辑HTML
          </button>
        </div>
      </div>
    `;
  };

  /**
   * 获取表情工具栏HTML
   * @returns {string} 表情工具栏HTML
   */
  const getEmojiToolbarHTML = () => {
    return `
      <div class="emoji-toolbar">
        <div class="toolbar-group">
          <button id="emoji-picker" class="toolbar-btn" label-on-hover="选择表情">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M7,9.5C7,8.7 7.7,8 8.5,8C9.3,8 10,8.7 10,9.5C10,10.3 9.3,11 8.5,11C7.7,11 7,10.3 7,9.5M14,17.5H10C10,15.6 11.6,14 13.5,14H10.5C12.4,14 14,15.6 14,17.5M17,9.5C17,10.3 16.3,11 15.5,11C14.7,11 14,10.3 14,9.5C14,8.7 14.7,8 15.5,8C16.3,8 17,8.7 17,9.5Z"/>
            </svg>
            选择表情
          </button>
        </div>
        ${getEmojiStylesHTML()}
      </div>
    `;
  };

  /**
   * 获取输入工具栏HTML
   * @returns {string} 输入工具栏HTML
   */
  const getInputToolbarHTML = () => {
    return `
      <div class="input-toolbar">
        <div class="toolbar-group">
          <label>占位符:</label>
          <input type="text" id="input-placeholder" class="toolbar-input" placeholder="输入占位符文本">
        </div>
        <div class="toolbar-group">
          <label>
            <input type="checkbox" id="input-required"> 必填
          </label>
        </div>
      </div>
    `;
  };

  /**
   * 获取嵌入工具栏HTML
   * @returns {string} 嵌入工具栏HTML
   */
  const getEmbedToolbarHTML = () => {
    return `
      <div class="embed-toolbar">
        <div class="toolbar-group">
          <button id="edit-embed" class="toolbar-btn" label-on-hover="编辑嵌入代码">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M14.6,16.6L19.2,12L14.6,7.4L16,6L22,12L16,18L14.6,16.6M9.4,16.6L4.8,12L9.4,7.4L8,6L2,12L8,18L9.4,16.6Z"/>
            </svg>
            编辑代码
          </button>
        </div>
      </div>
    `;
  };

  /**
   * 获取节点按钮工具栏HTML
   * @returns {string} 节点按钮工具栏HTML
   */
  const getNodeButtonToolbarHTML = () => {
    return `
      <div class="node-button-toolbar">
        ${getCommonTextStylesHTML()}
        ${getButtonStylesHTML()}
        ${getNodeButtonActionsHTML()}
      </div>
    `;
  };

  /**
   * 获取默认工具栏HTML
   * @returns {string} 默认工具栏HTML
   */
  const getDefaultToolbarHTML = () => {
    return `
      <div class="default-toolbar">
        <div class="toolbar-group">
          <span>通用编辑工具</span>
        </div>
      </div>
    `;
  };

  /**
   * 获取通用文本样式HTML
   * @returns {string} 通用文本样式HTML
   */
  const getCommonTextStylesHTML = () => {
    return `
      <div class="toolbar-group text-styles">
        <button id="bold" class="toolbar-btn format-btn" label-on-hover="粗体">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M13.5,15.5H10V12.5H13.5A1.5,1.5 0 0,1 15,14A1.5,1.5 0 0,1 13.5,15.5M10,6.5H13A1.5,1.5 0 0,1 14.5,8A1.5,1.5 0 0,1 13,9.5H10M15.6,10.79C16.57,10.11 17.25,9.02 17.25,8C17.25,5.74 15.5,4 13.25,4H7V18H14.04C16.14,18 17.75,16.3 17.75,14.21C17.75,12.69 16.89,11.39 15.6,10.79Z"/>
          </svg>
        </button>
        <button id="italic" class="toolbar-btn format-btn" label-on-hover="斜体">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M10,4V7H12.21L8.79,15H6V18H14V15H11.79L15.21,7H18V4H10Z"/>
          </svg>
        </button>
        <button id="underline" class="toolbar-btn format-btn" label-on-hover="下划线">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M5,21H19V19H5V21M12,17A6,6 0 0,0 18,11V3H15.5V11A3.5,3.5 0 0,1 12,14.5A3.5,3.5 0 0,1 8.5,11V3H6V11A6,6 0 0,0 12,17Z"/>
          </svg>
        </button>
        <div class="toolbar-separator"></div>
        <button id="font-color" class="toolbar-btn color-btn" label-on-hover="文字颜色">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M9.62,12L12,5.67L14.38,12M11,3L5.5,17H7.75L8.87,14H15.13L16.25,17H18.5L13,3H11Z"/>
          </svg>
        </button>
      </div>
    `;
  };

  /**
   * 获取文本格式化HTML
   * @returns {string} 文本格式化HTML
   */
  const getTextFormattingHTML = () => {
    return `
      <div class="toolbar-group text-formatting">
        <button id="create-link" class="toolbar-btn" label-on-hover="创建链接">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3.9,12C3.9,10.29 5.29,8.9 7,8.9H11V7H7A5,5 0 0,0 2,12A5,5 0 0,0 7,17H11V15.1H7C5.29,15.1 3.9,13.71 3.9,12M8,13H16V11H8V13M17,7H13V8.9H17C18.71,8.9 20.1,10.29 20.1,12C20.1,13.71 18.71,15.1 17,15.1H13V17H17A5,5 0 0,0 22,12A5,5 0 0,0 17,7Z"/>
          </svg>
        </button>
        <button id="remove-link" class="toolbar-btn" label-on-hover="移除链接">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M17,7H13V8.9H17C18.71,8.9 20.1,10.29 20.1,12C20.1,13.71 18.71,15.1 17,15.1H13V17H17A5,5 0 0,0 22,12A5,5 0 0,0 17,7M11,15.1H7C5.29,15.1 3.9,13.71 3.9,12C3.9,10.29 5.29,8.9 7,8.9H11V7H7A5,5 0 0,0 2,12A5,5 0 0,0 7,17H11V15.1M8,13H16V11H8V13M2,20L20,2L21.4,3.4L3.4,21.4L2,20Z"/>
          </svg>
        </button>
      </div>
    `;
  };

  /**
   * 获取文本对齐HTML
   * @returns {string} 文本对齐HTML
   */
  const getTextAlignmentHTML = () => {
    return `
      <div class="toolbar-group text-alignment">
        <button id="align-left" class="toolbar-btn align-btn" label-on-hover="左对齐">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3,3H21V5H3V3M3,7H15V9H3V7M3,11H21V13H3V11M3,15H15V17H3V15M3,19H21V21H3V19Z"/>
          </svg>
        </button>
        <button id="align-center" class="toolbar-btn align-btn" label-on-hover="居中对齐">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3,3H21V5H3V3M7,7H17V9H7V7M3,11H21V13H3V11M7,15H17V17H7V15M3,19H21V21H3V19Z"/>
          </svg>
        </button>
        <button id="align-right" class="toolbar-btn align-btn" label-on-hover="右对齐">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3,3H21V5H3V3M9,7H21V9H9V7M3,11H21V13H3V11M9,15H21V17H9V15M3,19H21V21H3V19Z"/>
          </svg>
        </button>
      </div>
    `;
  };

  /**
   * 获取按钮样式HTML
   * @returns {string} 按钮样式HTML
   */
  const getButtonStylesHTML = () => {
    return `
      <div class="toolbar-group button-styles">
        <button id="button-bg-color" class="toolbar-btn color-btn" label-on-hover="背景颜色">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3Z"/>
          </svg>
        </button>
        <button id="button-border" class="toolbar-btn" label-on-hover="边框设置">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,5V19H5V5H19Z"/>
          </svg>
        </button>
        <button id="button-radius" class="toolbar-btn" label-on-hover="圆角设置">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2Z"/>
          </svg>
        </button>
      </div>
    `;
  };

  /**
   * 获取按钮动作HTML
   * @returns {string} 按钮动作HTML
   */
  const getButtonActionsHTML = () => {
    return `
      <div class="toolbar-group button-actions">
        <select id="button-action" class="toolbar-select">
          <option value="">选择动作</option>
          <option value="next">下一步</option>
          <option value="prev">上一步</option>
          <option value="url">跳转链接</option>
          <option value="close">关闭</option>
        </select>
      </div>
    `;
  };

  /**
   * 获取节点按钮动作HTML
   * @returns {string} 节点按钮动作HTML
   */
  const getNodeButtonActionsHTML = () => {
    return `
      <div class="toolbar-group node-button-actions">
        <select id="node-action" class="toolbar-select">
          <option value="">选择节点动作</option>
          <option value="highlight">高亮元素</option>
          <option value="click">点击元素</option>
          <option value="scroll">滚动到元素</option>
        </select>
      </div>
    `;
  };

  /**
   * 获取图片样式HTML
   * @returns {string} 图片样式HTML
   */
  const getImageStylesHTML = () => {
    return `
      <div class="toolbar-group image-styles">
        <button id="image-align-left" class="toolbar-btn" label-on-hover="图片左对齐">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3,3H21V5H3V3M3,7H15V9H3V7M3,11H21V13H3V11M3,15H15V17H3V15M3,19H21V21H3V19Z"/>
          </svg>
        </button>
        <button id="image-align-center" class="toolbar-btn" label-on-hover="图片居中">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M7,7H17V9H7V7M3,11H21V13H3V11M7,15H17V17H7V15"/>
          </svg>
        </button>
        <button id="image-align-right" class="toolbar-btn" label-on-hover="图片右对齐">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M9,7H21V9H9V7M3,11H21V13H3V11M9,15H21V17H9V15"/>
          </svg>
        </button>
      </div>
    `;
  };

  /**
   * 获取表情样式HTML
   * @returns {string} 表情样式HTML
   */
  const getEmojiStylesHTML = () => {
    return `
      <div class="toolbar-group emoji-styles">
        <label>大小:</label>
        <input type="range" id="emoji-size" min="16" max="64" value="24" class="toolbar-range">
      </div>
    `;
  };

  // 返回工具栏视图管理器对象
  return {
    getMainToolbarHTML,
    getControllerToolbarHTML,
    getSectionToolbarHTML,
    getHeaderToolbarHTML,
    getTextToolbarHTML,
    getButtonToolbarHTML,
    getImageToolbarHTML,
    getHtmlToolbarHTML,
    getEmojiToolbarHTML,
    getInputToolbarHTML,
    getEmbedToolbarHTML,
    getNodeButtonToolbarHTML,
    getDefaultToolbarHTML,
    getCommonTextStylesHTML,
    getTextFormattingHTML,
    getTextAlignmentHTML,
    getButtonStylesHTML,
    getButtonActionsHTML,
    getNodeButtonActionsHTML,
    getImageStylesHTML,
    getEmojiStylesHTML
  };
};
