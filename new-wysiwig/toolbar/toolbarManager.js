/**
 * 工具栏管理器
 * 函数式实现的WYSIWYG工具栏管理
 */

import { getSectionType } from '../utils/sectionUtils.js';
import { createToolbarViews } from './toolbarViews.js';
import { createToolbarEvents } from './toolbarEvents.js';

/**
 * 创建工具栏管理器
 * @param {Object} state - 编辑器状态
 * @returns {Object} 工具栏管理器对象
 */
export const createToolbar = (state) => {
  let toolbarElement = null;
  let sectionToolbar = null;
  let controllerToolbar = null;
  let isVisible = false;
  let currentSection = null;
  let currentEditor = null;

  // 工具栏视图和事件管理器
  const views = createToolbarViews();
  const events = createToolbarEvents(state);

  /**
   * 初始化工具栏
   */
  const init = () => {
    if (toolbarElement) return toolbar;

    // 创建主工具栏元素
    toolbarElement = createToolbarElement();
    sectionToolbar = toolbarElement.querySelector('#section-toolbar');
    controllerToolbar = toolbarElement.querySelector('#controller-toolbar');

    // 初始化控制器工具栏事件
    initControllerEvents();

    // 初始化提示工具
    initTooltips();

    return toolbar;
  };

  /**
   * 创建工具栏元素
   * @returns {HTMLElement} 工具栏元素
   */
  const createToolbarElement = () => {
    const toolbar = document.createElement('div');
    toolbar.id = 'wysiwyg-toolbar';
    toolbar.className = 'wysiwyg-toolbar';
    toolbar.innerHTML = views.getMainToolbarHTML();
    
    return toolbar;
  };

  /**
   * 初始化控制器事件
   */
  const initControllerEvents = () => {
    if (!controllerToolbar) return;

    const saveBtn = controllerToolbar.querySelector('#save-section');
    const discardBtn = controllerToolbar.querySelector('#discard-section');
    const removeBtn = controllerToolbar.querySelector('#remove-section');
    const cloneBtn = controllerToolbar.querySelector('#clone-section');

    if (saveBtn) {
      saveBtn.addEventListener('click', events.handleSave);
    }
    if (discardBtn) {
      discardBtn.addEventListener('click', events.handleDiscard);
    }
    if (removeBtn) {
      removeBtn.addEventListener('click', events.handleRemove);
    }
    if (cloneBtn) {
      // 某些模板类型不显示克隆按钮
      if (shouldHideCloneButton()) {
        cloneBtn.remove();
      } else {
        cloneBtn.addEventListener('click', events.handleClone);
      }
    }
  };

  /**
   * 初始化提示工具
   */
  const initTooltips = () => {
    if (!toolbarElement) return;

    const tooltipElements = toolbarElement.querySelectorAll('[label-on-hover]');
    
    // 这里应该初始化tippy.js或其他提示工具
    // tippy(tooltipElements, { 
    //   appendTo: state.userpilotContainer, 
    //   zIndex: 1000 
    // });
  };

  /**
   * 显示工具栏
   * @param {HTMLElement} section - 当前编辑的区块
   * @param {Object} editor - 区块编辑器
   */
  const show = (section, editor) => {
    if (!toolbarElement) init();

    currentSection = section;
    currentEditor = editor;

    // 更新区块工具栏内容
    updateSectionToolbar(section);

    // 添加工具栏到DOM
    state.userpilotContent.appendChild(toolbarElement);

    // 定位工具栏
    position();

    // 设置可见状态
    isVisible = true;
    toolbarElement.style.display = 'block';

    // 检查是否为进度按钮
    const isProgressionButton = ['userpilot-next-button', 'userpilot-back-button']
      .includes(section.id);
    
    if (isProgressionButton) {
      toolbarElement.classList.add('progression-section');
    } else {
      toolbarElement.classList.remove('progression-section');
    }

    return toolbar;
  };

  /**
   * 隐藏工具栏
   */
  const hide = () => {
    if (toolbarElement && toolbarElement.parentNode) {
      toolbarElement.parentNode.removeChild(toolbarElement);
    }

    isVisible = false;
    currentSection = null;
    currentEditor = null;

    // 清理样式类
    if (toolbarElement) {
      toolbarElement.classList.remove('no-image', 'no-embed', 'progression-section');
    }

    return toolbar;
  };

  /**
   * 更新区块工具栏
   * @param {HTMLElement} section - 区块元素
   */
  const updateSectionToolbar = (section) => {
    if (!sectionToolbar) return;

    const sectionType = getSectionType(section);
    const toolbarHTML = views.getSectionToolbarHTML(sectionType);
    
    sectionToolbar.innerHTML = toolbarHTML;

    // 初始化区块特定的事件
    events.initSectionEvents(sectionToolbar, sectionType, currentEditor);

    // 重新初始化提示工具
    initTooltips();
  };

  /**
   * 定位工具栏
   */
  const position = () => {
    if (!toolbarElement || !currentSection) return;

    const sectionRect = currentSection.getBoundingClientRect();
    const containerRect = state.userpilotContainer.getBoundingClientRect();

    // 垂直定位
    let top = currentSection.offsetTop + currentSection.offsetHeight + 20;
    
    // 水平定位
    let left = 0;
    if (state.templateEditor.templateSettings.type === 'BANNER_v2') {
      left = currentSection.offsetLeft;
    }

    toolbarElement.style.top = `${top}px`;
    toolbarElement.style.left = `${left}px`;
    toolbarElement.style.right = 'unset';

    // 重新定位以避免超出边界
    reposition();
  };

  /**
   * 重新定位工具栏
   */
  const reposition = () => {
    if (!isVisible || !toolbarElement || !currentSection) return;

    const toolbarRect = toolbarElement.getBoundingClientRect();
    const sectionRect = currentSection.getBoundingClientRect();
    const windowHeight = state.iframeWindow.innerHeight || 
                        state.iframeDocument.documentElement.clientHeight;
    const windowWidth = state.iframeWindow.innerWidth || 
                       state.iframeDocument.documentElement.clientWidth;

    // 垂直重新定位
    const spaceBelow = windowHeight - sectionRect.bottom;
    const toolbarHeight = toolbarElement.offsetHeight;
    
    if (spaceBelow < toolbarHeight + 20) {
      // 工具栏放在区块上方
      const newTop = currentSection.offsetTop - 
                    state.templateEditor.userpilotSlideContainer.scrollTop - 
                    toolbarHeight - 10;
      toolbarElement.style.top = `${Math.max(0, newTop)}px`;
    } else {
      // 工具栏放在区块下方
      const newTop = currentSection.offsetTop - 
                    state.templateEditor.userpilotSlideContainer.scrollTop + 
                    currentSection.offsetHeight + 20;
      toolbarElement.style.top = `${newTop}px`;
    }

    // 水平重新定位
    if (toolbarRect.right >= windowWidth) {
      toolbarElement.style.left = 'unset';
      toolbarElement.style.right = '0px';
    }

    // 处理特殊情况（如横幅类型）
    if (parseInt(toolbarElement.style.top) < 0) {
      handleSpecialPositioning();
    }
  };

  /**
   * 处理特殊定位情况
   */
  const handleSpecialPositioning = () => {
    const templateType = state.templateEditor.stepType;
    
    switch (templateType) {
      case 'BANNER':
        const height = calculateHeightUntilSectionEnd(currentSection);
        toolbarElement.style.top = `${height + 25}px`;
        break;
      default:
        break;
    }
  };

  /**
   * 计算到区块结束的高度
   * @param {HTMLElement} element - 当前元素
   * @returns {number} 高度值
   */
  const calculateHeightUntilSectionEnd = (element) => {
    let height = element.clientHeight;
    let prevElement = element.classList.contains('userpilot-col-section') 
      ? element.parentElement.previousElementSibling 
      : element.previousElementSibling;

    while (prevElement) {
      height += prevElement.clientHeight;
      prevElement = prevElement.previousElementSibling;
    }

    return height;
  };

  /**
   * 更新工具栏状态
   * @param {Object} styles - 当前样式状态
   */
  const updateState = (styles) => {
    if (!sectionToolbar) return;

    // 更新工具栏按钮状态以反映当前样式
    events.updateToolbarState(sectionToolbar, styles);
  };

  /**
   * 检查是否应该隐藏克隆按钮
   * @returns {boolean} 是否隐藏
   */
  const shouldHideCloneButton = () => {
    return state.templateEditor.stepType === 'BANNER_v2';
  };

  /**
   * 添加工具栏样式类
   * @param {string} className - 样式类名
   */
  const addClass = (className) => {
    if (toolbarElement) {
      toolbarElement.classList.add(className);
    }
  };

  /**
   * 移除工具栏样式类
   * @param {string} className - 样式类名
   */
  const removeClass = (className) => {
    if (toolbarElement) {
      toolbarElement.classList.remove(className);
    }
  };

  /**
   * 销毁工具栏
   */
  const destroy = () => {
    hide();
    events.destroy();
    
    toolbarElement = null;
    sectionToolbar = null;
    controllerToolbar = null;
    currentSection = null;
    currentEditor = null;
  };

  // 工具栏对象
  const toolbar = {
    init,
    show,
    hide,
    position,
    reposition,
    updateState,
    addClass,
    removeClass,
    destroy,
    
    // 访问器
    get element() { return toolbarElement; },
    get sectionToolbar() { return sectionToolbar; },
    get controllerToolbar() { return controllerToolbar; },
    get isVisible() { return isVisible; },
    get currentSection() { return currentSection; },
    get currentEditor() { return currentEditor; }
  };

  return toolbar;
};
