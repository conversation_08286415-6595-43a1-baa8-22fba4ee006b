/**
 * 工具栏事件管理器
 * 函数式实现的工具栏事件处理
 */

/**
 * 创建工具栏事件管理器
 * @param {Object} state - 编辑器状态
 * @returns {Object} 工具栏事件管理器对象
 */
export const createToolbarEvents = (state) => {
  
  // 事件监听器映射
  const eventListeners = new Map();

  /**
   * 处理保存事件
   */
  const handleSave = () => {
    if (state.activeBuilder && typeof state.activeBuilder.validateChanges === 'function') {
      if (state.activeBuilder.validateChanges()) {
        state.activeBuilder.save('apply');
        // 触发保存完成事件
        triggerCustomEvent('section-saved', { section: state.currentSection });
      } else {
        showAlert('error', '请检查输入内容是否有效');
      }
    } else if (state.activeBuilder) {
      state.activeBuilder.save('apply');
      triggerCustomEvent('section-saved', { section: state.currentSection });
    }
  };

  /**
   * 处理撤销事件
   */
  const handleDiscard = () => {
    if (state.activeBuilder) {
      state.activeBuilder.save('discard');
      triggerCustomEvent('section-discarded', { section: state.currentSection });
    }
  };

  /**
   * 处理移除事件
   */
  const handleRemove = () => {
    if (state.currentSection) {
      if (confirm('确定要删除这个区块吗？')) {
        triggerCustomEvent('section-remove', { section: state.currentSection });
      }
    }
  };

  /**
   * 处理克隆事件
   */
  const handleClone = () => {
    if (state.currentSection) {
      triggerCustomEvent('section-clone', { section: state.currentSection });
    }
  };

  /**
   * 初始化区块特定事件
   * @param {HTMLElement} toolbar - 工具栏元素
   * @param {string} sectionType - 区块类型
   * @param {Object} editor - 编辑器实例
   */
  const initSectionEvents = (toolbar, sectionType, editor) => {
    // 清除之前的事件监听器
    clearSectionEvents();

    switch (sectionType) {
      case 'userpilot-header-section':
        initHeaderEvents(toolbar, editor);
        break;
      case 'userpilot-text-section':
        initTextEvents(toolbar, editor);
        break;
      case 'userpilot-button-section':
        initButtonEvents(toolbar, editor);
        break;
      case 'userpilot-image-section':
        initImageEvents(toolbar, editor);
        break;
      case 'userpilot-html-section':
        initHtmlEvents(toolbar, editor);
        break;
      case 'userpilot-emoji-section':
        initEmojiEvents(toolbar, editor);
        break;
      case 'userpilot-input-text':
      case 'userpilot-input-text-large':
      case 'userpilot-input-radio':
      case 'userpilot-input-likert-scale':
        initInputEvents(toolbar, editor);
        break;
      case 'userpilot-embed-section':
        initEmbedEvents(toolbar, editor);
        break;
      case 'userpilot-node-button':
        initNodeButtonEvents(toolbar, editor);
        break;
    }
  };

  /**
   * 初始化标题事件
   * @param {HTMLElement} toolbar - 工具栏元素
   * @param {Object} editor - 编辑器实例
   */
  const initHeaderEvents = (toolbar, editor) => {
    const headerLevelSelect = toolbar.querySelector('#header-level');
    if (headerLevelSelect) {
      const listener = (e) => {
        const level = parseInt(e.target.value);
        editor.setHeaderLevel?.(level);
      };
      headerLevelSelect.addEventListener('change', listener);
      eventListeners.set('header-level', { element: headerLevelSelect, event: 'change', listener });
    }

    initCommonTextEvents(toolbar, editor);
  };

  /**
   * 初始化文本事件
   * @param {HTMLElement} toolbar - 工具栏元素
   * @param {Object} editor - 编辑器实例
   */
  const initTextEvents = (toolbar, editor) => {
    initCommonTextEvents(toolbar, editor);
    initTextFormattingEvents(toolbar, editor);
    initTextAlignmentEvents(toolbar, editor);
  };

  /**
   * 初始化按钮事件
   * @param {HTMLElement} toolbar - 工具栏元素
   * @param {Object} editor - 编辑器实例
   */
  const initButtonEvents = (toolbar, editor) => {
    initCommonTextEvents(toolbar, editor);
    initButtonStyleEvents(toolbar, editor);
    initButtonActionEvents(toolbar, editor);
  };

  /**
   * 初始化图片事件
   * @param {HTMLElement} toolbar - 工具栏元素
   * @param {Object} editor - 编辑器实例
   */
  const initImageEvents = (toolbar, editor) => {
    const uploadBtn = toolbar.querySelector('#upload-image');
    const unsplashBtn = toolbar.querySelector('#unsplash-image');

    if (uploadBtn) {
      const listener = () => {
        triggerImageUpload(editor);
      };
      uploadBtn.addEventListener('click', listener);
      eventListeners.set('upload-image', { element: uploadBtn, event: 'click', listener });
    }

    if (unsplashBtn) {
      const listener = () => {
        triggerUnsplashPicker(editor);
      };
      unsplashBtn.addEventListener('click', listener);
      eventListeners.set('unsplash-image', { element: unsplashBtn, event: 'click', listener });
    }

    initImageStyleEvents(toolbar, editor);
  };

  /**
   * 初始化HTML事件
   * @param {HTMLElement} toolbar - 工具栏元素
   * @param {Object} editor - 编辑器实例
   */
  const initHtmlEvents = (toolbar, editor) => {
    const editBtn = toolbar.querySelector('#edit-html');
    if (editBtn) {
      const listener = () => {
        triggerHtmlEditor(editor);
      };
      editBtn.addEventListener('click', listener);
      eventListeners.set('edit-html', { element: editBtn, event: 'click', listener });
    }
  };

  /**
   * 初始化表情事件
   * @param {HTMLElement} toolbar - 工具栏元素
   * @param {Object} editor - 编辑器实例
   */
  const initEmojiEvents = (toolbar, editor) => {
    const emojiBtn = toolbar.querySelector('#emoji-picker');
    const sizeRange = toolbar.querySelector('#emoji-size');

    if (emojiBtn) {
      const listener = () => {
        triggerEmojiPicker(editor);
      };
      emojiBtn.addEventListener('click', listener);
      eventListeners.set('emoji-picker', { element: emojiBtn, event: 'click', listener });
    }

    if (sizeRange) {
      const listener = (e) => {
        const size = e.target.value + 'px';
        editor.sectionContainer.style.fontSize = size;
      };
      sizeRange.addEventListener('input', listener);
      eventListeners.set('emoji-size', { element: sizeRange, event: 'input', listener });
    }
  };

  /**
   * 初始化输入事件
   * @param {HTMLElement} toolbar - 工具栏元素
   * @param {Object} editor - 编辑器实例
   */
  const initInputEvents = (toolbar, editor) => {
    const placeholderInput = toolbar.querySelector('#input-placeholder');
    const requiredCheckbox = toolbar.querySelector('#input-required');

    if (placeholderInput) {
      const listener = (e) => {
        editor.setPlaceholder?.(e.target.value);
      };
      placeholderInput.addEventListener('input', listener);
      eventListeners.set('input-placeholder', { element: placeholderInput, event: 'input', listener });
    }

    if (requiredCheckbox) {
      const listener = (e) => {
        editor.setRequired?.(e.target.checked);
      };
      requiredCheckbox.addEventListener('change', listener);
      eventListeners.set('input-required', { element: requiredCheckbox, event: 'change', listener });
    }
  };

  /**
   * 初始化嵌入事件
   * @param {HTMLElement} toolbar - 工具栏元素
   * @param {Object} editor - 编辑器实例
   */
  const initEmbedEvents = (toolbar, editor) => {
    const editBtn = toolbar.querySelector('#edit-embed');
    if (editBtn) {
      const listener = () => {
        triggerEmbedEditor(editor);
      };
      editBtn.addEventListener('click', listener);
      eventListeners.set('edit-embed', { element: editBtn, event: 'click', listener });
    }
  };

  /**
   * 初始化节点按钮事件
   * @param {HTMLElement} toolbar - 工具栏元素
   * @param {Object} editor - 编辑器实例
   */
  const initNodeButtonEvents = (toolbar, editor) => {
    initCommonTextEvents(toolbar, editor);
    initButtonStyleEvents(toolbar, editor);

    const nodeActionSelect = toolbar.querySelector('#node-action');
    if (nodeActionSelect) {
      const listener = (e) => {
        editor.setNodeAction?.(e.target.value);
      };
      nodeActionSelect.addEventListener('change', listener);
      eventListeners.set('node-action', { element: nodeActionSelect, event: 'change', listener });
    }
  };

  /**
   * 初始化通用文本事件
   * @param {HTMLElement} toolbar - 工具栏元素
   * @param {Object} editor - 编辑器实例
   */
  const initCommonTextEvents = (toolbar, editor) => {
    const formatButtons = ['bold', 'italic', 'underline'];
    
    formatButtons.forEach(format => {
      const btn = toolbar.querySelector(`#${format}`);
      if (btn) {
        const listener = () => {
          execCommand(format);
        };
        btn.addEventListener('click', listener);
        eventListeners.set(format, { element: btn, event: 'click', listener });
      }
    });

    const colorBtn = toolbar.querySelector('#font-color');
    if (colorBtn) {
      const listener = () => {
        triggerColorPicker(editor, 'color');
      };
      colorBtn.addEventListener('click', listener);
      eventListeners.set('font-color', { element: colorBtn, event: 'click', listener });
    }
  };

  /**
   * 初始化文本格式化事件
   * @param {HTMLElement} toolbar - 工具栏元素
   * @param {Object} editor - 编辑器实例
   */
  const initTextFormattingEvents = (toolbar, editor) => {
    const createLinkBtn = toolbar.querySelector('#create-link');
    const removeLinkBtn = toolbar.querySelector('#remove-link');

    if (createLinkBtn) {
      const listener = () => {
        const url = prompt('请输入链接地址:');
        if (url) {
          execCommand('createLink', url);
        }
      };
      createLinkBtn.addEventListener('click', listener);
      eventListeners.set('create-link', { element: createLinkBtn, event: 'click', listener });
    }

    if (removeLinkBtn) {
      const listener = () => {
        execCommand('unlink');
      };
      removeLinkBtn.addEventListener('click', listener);
      eventListeners.set('remove-link', { element: removeLinkBtn, event: 'click', listener });
    }
  };

  /**
   * 初始化文本对齐事件
   * @param {HTMLElement} toolbar - 工具栏元素
   * @param {Object} editor - 编辑器实例
   */
  const initTextAlignmentEvents = (toolbar, editor) => {
    const alignButtons = ['align-left', 'align-center', 'align-right'];
    const alignValues = ['left', 'center', 'right'];

    alignButtons.forEach((btnId, index) => {
      const btn = toolbar.querySelector(`#${btnId}`);
      if (btn) {
        const listener = () => {
          execCommand('justifyLeft'); // 先重置
          execCommand(`justify${alignValues[index].charAt(0).toUpperCase() + alignValues[index].slice(1)}`);
        };
        btn.addEventListener('click', listener);
        eventListeners.set(btnId, { element: btn, event: 'click', listener });
      }
    });
  };

  /**
   * 初始化按钮样式事件
   * @param {HTMLElement} toolbar - 工具栏元素
   * @param {Object} editor - 编辑器实例
   */
  const initButtonStyleEvents = (toolbar, editor) => {
    const bgColorBtn = toolbar.querySelector('#button-bg-color');
    const borderBtn = toolbar.querySelector('#button-border');
    const radiusBtn = toolbar.querySelector('#button-radius');

    if (bgColorBtn) {
      const listener = () => {
        triggerColorPicker(editor, 'backgroundColor');
      };
      bgColorBtn.addEventListener('click', listener);
      eventListeners.set('button-bg-color', { element: bgColorBtn, event: 'click', listener });
    }

    if (borderBtn) {
      const listener = () => {
        triggerBorderEditor(editor);
      };
      borderBtn.addEventListener('click', listener);
      eventListeners.set('button-border', { element: borderBtn, event: 'click', listener });
    }

    if (radiusBtn) {
      const listener = () => {
        triggerRadiusEditor(editor);
      };
      radiusBtn.addEventListener('click', listener);
      eventListeners.set('button-radius', { element: radiusBtn, event: 'click', listener });
    }
  };

  /**
   * 初始化按钮动作事件
   * @param {HTMLElement} toolbar - 工具栏元素
   * @param {Object} editor - 编辑器实例
   */
  const initButtonActionEvents = (toolbar, editor) => {
    const actionSelect = toolbar.querySelector('#button-action');
    if (actionSelect) {
      const listener = (e) => {
        editor.setButtonAction?.(e.target.value);
      };
      actionSelect.addEventListener('change', listener);
      eventListeners.set('button-action', { element: actionSelect, event: 'change', listener });
    }
  };

  /**
   * 初始化图片样式事件
   * @param {HTMLElement} toolbar - 工具栏元素
   * @param {Object} editor - 编辑器实例
   */
  const initImageStyleEvents = (toolbar, editor) => {
    const alignButtons = ['image-align-left', 'image-align-center', 'image-align-right'];
    const alignValues = ['left', 'center', 'right'];

    alignButtons.forEach((btnId, index) => {
      const btn = toolbar.querySelector(`#${btnId}`);
      if (btn) {
        const listener = () => {
          if (editor.imageElement) {
            editor.imageElement.style.display = 'block';
            editor.imageElement.style.marginLeft = alignValues[index] === 'center' ? 'auto' : 
                                                   alignValues[index] === 'right' ? 'auto' : '0';
            editor.imageElement.style.marginRight = alignValues[index] === 'center' ? 'auto' : 
                                                    alignValues[index] === 'left' ? 'auto' : '0';
          }
        };
        btn.addEventListener('click', listener);
        eventListeners.set(btnId, { element: btn, event: 'click', listener });
      }
    });
  };

  /**
   * 执行编辑命令
   * @param {string} command - 命令名称
   * @param {string} value - 命令值
   */
  const execCommand = (command, value = null) => {
    state.iframeDocument.execCommand(command, false, value);
  };

  /**
   * 更新工具栏状态
   * @param {HTMLElement} toolbar - 工具栏元素
   * @param {Object} styles - 样式对象
   */
  const updateToolbarState = (toolbar, styles) => {
    // 更新格式按钮状态
    const formatButtons = toolbar.querySelectorAll('.format-btn');
    formatButtons.forEach(btn => {
      const command = btn.id;
      const isActive = state.iframeDocument.queryCommandState(command);
      btn.classList.toggle('active', isActive);
    });

    // 更新对齐按钮状态
    const alignButtons = toolbar.querySelectorAll('.align-btn');
    alignButtons.forEach(btn => {
      btn.classList.remove('active');
    });

    // 根据当前对齐方式激活对应按钮
    if (styles.textAlign) {
      const activeAlignBtn = toolbar.querySelector(`#align-${styles.textAlign}`);
      if (activeAlignBtn) {
        activeAlignBtn.classList.add('active');
      }
    }
  };

  /**
   * 清除区块事件监听器
   */
  const clearSectionEvents = () => {
    eventListeners.forEach(({ element, event, listener }) => {
      element.removeEventListener(event, listener);
    });
    eventListeners.clear();
  };

  /**
   * 触发自定义事件
   * @param {string} eventName - 事件名称
   * @param {Object} detail - 事件详情
   */
  const triggerCustomEvent = (eventName, detail) => {
    const event = new CustomEvent(eventName, { detail });
    state.userpilotContainer.dispatchEvent(event);
  };

  /**
   * 显示警告信息
   * @param {string} type - 警告类型
   * @param {string} message - 警告信息
   */
  const showAlert = (type, message) => {
    // 这里应该调用实际的警告组件
    console.log(`${type.toUpperCase()}: ${message}`);
  };

  // 触发器函数（这些应该连接到实际的组件）
  const triggerImageUpload = (editor) => {
    console.log('Trigger image upload for:', editor);
  };

  const triggerUnsplashPicker = (editor) => {
    console.log('Trigger Unsplash picker for:', editor);
  };

  const triggerHtmlEditor = (editor) => {
    console.log('Trigger HTML editor for:', editor);
  };

  const triggerEmojiPicker = (editor) => {
    console.log('Trigger emoji picker for:', editor);
  };

  const triggerEmbedEditor = (editor) => {
    console.log('Trigger embed editor for:', editor);
  };

  const triggerColorPicker = (editor, property) => {
    console.log('Trigger color picker for:', editor, property);
  };

  const triggerBorderEditor = (editor) => {
    console.log('Trigger border editor for:', editor);
  };

  const triggerRadiusEditor = (editor) => {
    console.log('Trigger radius editor for:', editor);
  };

  /**
   * 销毁事件管理器
   */
  const destroy = () => {
    clearSectionEvents();
  };

  // 返回事件管理器对象
  return {
    handleSave,
    handleDiscard,
    handleRemove,
    handleClone,
    initSectionEvents,
    updateToolbarState,
    clearSectionEvents,
    destroy,
    
    // 隐藏和显示窗口组件的方法
    hideWindowComponents: () => {
      // 这里应该调用实际的Store actions
      console.log('Hide window components');
    },
    
    showWindowComponents: () => {
      // 这里应该调用实际的Store actions
      console.log('Show window components');
    },
    
    triggerSectionChange: () => {
      // 触发区块变化事件
      const event = new CustomEvent('stepSectionsChange');
      state.userpilotContainer.dispatchEvent(event);
    },
    
    showAlert,
    readjustColumnSections: (container) => {
      // 重新调整列区块的逻辑
      console.log('Readjust column sections for:', container);
    }
  };
};
