/**
 * 编辑器状态管理
 * 使用函数式方法管理编辑器的全局状态
 */

/**
 * 创建初始编辑器状态
 * @param {Object} config - 配置对象
 * @returns {Object} 编辑器状态对象
 */
export const createEditorState = (config) => {
  const state = {
    // 模板编辑器相关
    templateEditor: config.templateEditor,
    iframeDocument: config.iframeDocument,
    iframeWindow: config.iframeWindow,
    userpilotContainer: config.userpilotContainer,
    userpilotContent: config.userpilotContent,
    userpilotSlide: config.userpilotSlide,
    sections: config.sections,

    // 编辑器状态
    activeBuilder: null,
    currentSection: null,
    oldHtml: null,
    editorActive: false,
    
    // 工具栏相关
    wysiwygToolbar: null,
    sectionToolbar: null,

    // 其他状态
    sectionsMap: new Map(),
    
    // 事件监听器引用
    listeners: new Map(),
    
    // 配置选项
    config: {
      observerConfig: {
        attributes: true,
        childList: true,
        characterData: true,
        subtree: true
      },
      repositionDelay: 100,
      saveDelay: 300
    }
  };

  return state;
};

/**
 * 更新编辑器状态
 * @param {Object} state - 当前状态
 * @param {Object} updates - 要更新的状态
 * @returns {Object} 更新后的状态
 */
export const updateEditorState = (state, updates) => {
  Object.assign(state, updates);
  return state;
};

/**
 * 获取编辑器状态的只读副本
 * @param {Object} state - 当前状态
 * @returns {Object} 状态的只读副本
 */
export const getEditorState = (state) => {
  return Object.freeze({ ...state });
};

/**
 * 重置编辑器状态
 * @param {Object} state - 当前状态
 * @returns {Object} 重置后的状态
 */
export const resetEditorState = (state) => {
  return updateEditorState(state, {
    activeBuilder: null,
    currentSection: null,
    oldHtml: null,
    editorActive: false,
    wysiwygToolbar: null,
    sectionToolbar: null
  });
};

/**
 * 检查编辑器是否处于活动状态
 * @param {Object} state - 当前状态
 * @returns {boolean} 是否活动
 */
export const isEditorActive = (state) => {
  return state.editorActive && state.activeBuilder && state.currentSection;
};

/**
 * 获取当前编辑的区块类型
 * @param {Object} state - 当前状态
 * @returns {string|null} 区块类型
 */
export const getCurrentSectionType = (state) => {
  if (!state.currentSection) return null;
  
  const classList = state.currentSection.classList;
  
  if (classList.contains('userpilot-button')) return 'userpilot-node-button';
  if (classList.contains('userpilot-header-section')) return 'userpilot-header-section';
  if (classList.contains('userpilot-text-section')) return 'userpilot-text-section';
  if (classList.contains('userpilot-image-section')) return 'userpilot-image-section';
  if (classList.contains('userpilot-button-section')) return 'userpilot-button-section';
  if (classList.contains('userpilot-html-section') || classList.contains('userpilot-video-section')) {
    return 'userpilot-html-section';
  }
  if (classList.contains('userpilot-emoji-section')) return 'userpilot-emoji-section';
  if (classList.contains('userpilot-input-radio')) return 'userpilot-input-radio';
  if (classList.contains('userpilot-input-text')) return 'userpilot-input-text';
  if (classList.contains('userpilot-input-text-large')) return 'userpilot-input-text-large';
  if (classList.contains('userpilot-input-likert-scale')) return 'userpilot-input-likert-scale';
  if (classList.contains('userpilot-embed-section')) return 'userpilot-embed-section';
  
  return null;
};

/**
 * 添加事件监听器引用
 * @param {Object} state - 当前状态
 * @param {string} key - 监听器键名
 * @param {Function} listener - 监听器函数
 */
export const addListener = (state, key, listener) => {
  state.listeners.set(key, listener);
};

/**
 * 移除事件监听器引用
 * @param {Object} state - 当前状态
 * @param {string} key - 监听器键名
 */
export const removeListener = (state, key) => {
  const listener = state.listeners.get(key);
  if (listener) {
    state.listeners.delete(key);
  }
  return listener;
};

/**
 * 清除所有事件监听器引用
 * @param {Object} state - 当前状态
 */
export const clearAllListeners = (state) => {
  state.listeners.clear();
};

/**
 * 设置区块编辑器映射
 * @param {Object} state - 当前状态
 * @param {HTMLElement} section - 区块元素
 * @param {Object} editor - 编辑器实例
 */
export const setSectionEditor = (state, section, editor) => {
  state.sectionsMap.set(section, editor);
};

/**
 * 获取区块编辑器
 * @param {Object} state - 当前状态
 * @param {HTMLElement} section - 区块元素
 * @returns {Object|null} 编辑器实例
 */
export const getSectionEditor = (state, section) => {
  return state.sectionsMap.get(section) || null;
};

/**
 * 移除区块编辑器映射
 * @param {Object} state - 当前状态
 * @param {HTMLElement} section - 区块元素
 */
export const removeSectionEditor = (state, section) => {
  state.sectionsMap.delete(section);
};

/**
 * 清除所有区块编辑器映射
 * @param {Object} state - 当前状态
 */
export const clearSectionEditors = (state) => {
  state.sectionsMap.clear();
};

/**
 * 状态验证器
 */
export const validators = {
  /**
   * 验证状态是否有效
   * @param {Object} state - 要验证的状态
   * @returns {boolean} 是否有效
   */
  isValidState: (state) => {
    return state && 
           typeof state === 'object' &&
           state.templateEditor &&
           state.iframeDocument &&
           state.iframeWindow &&
           state.userpilotContainer &&
           state.userpilotContent &&
           Array.isArray(state.sections);
  },

  /**
   * 验证编辑器是否可以启动
   * @param {Object} state - 当前状态
   * @returns {boolean} 是否可以启动
   */
  canStartEditor: (state) => {
    return !state.editorActive && 
           state.currentSection === null && 
           state.activeBuilder === null;
  },

  /**
   * 验证编辑器是否可以保存
   * @param {Object} state - 当前状态
   * @returns {boolean} 是否可以保存
   */
  canSaveEditor: (state) => {
    return state.editorActive && 
           state.currentSection !== null && 
           state.activeBuilder !== null;
  }
};

/**
 * 状态调试工具
 */
export const debugTools = {
  /**
   * 打印当前状态
   * @param {Object} state - 当前状态
   */
  logState: (state) => {
    console.group('Editor State Debug');
    console.log('Editor Active:', state.editorActive);
    console.log('Current Section:', state.currentSection);
    console.log('Active Builder:', state.activeBuilder);
    console.log('Sections Count:', state.sections.length);
    console.log('Section Editors Count:', state.sectionsMap.size);
    console.log('Listeners Count:', state.listeners.size);
    console.groupEnd();
  },

  /**
   * 验证状态完整性
   * @param {Object} state - 当前状态
   * @returns {Array} 验证错误列表
   */
  validateStateIntegrity: (state) => {
    const errors = [];

    if (!validators.isValidState(state)) {
      errors.push('Invalid state structure');
    }

    if (state.editorActive && !state.currentSection) {
      errors.push('Editor is active but no current section');
    }

    if (state.editorActive && !state.activeBuilder) {
      errors.push('Editor is active but no active builder');
    }

    if (state.currentSection && !state.sectionsMap.has(state.currentSection)) {
      errors.push('Current section has no associated editor');
    }

    return errors;
  }
};
