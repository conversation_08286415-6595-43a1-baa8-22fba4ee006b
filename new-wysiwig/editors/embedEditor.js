/**
 * 嵌入编辑器
 * 函数式实现的嵌入内容区块编辑器
 */

import { createBaseEditor } from './sectionEditorFactory.js';

export const createEmbedEditor = (section, toolbar, state) => {
  const baseEditor = createBaseEditor(section, toolbar, state);
  
  let embedCode = '';
  let embedType = 'iframe';

  const init = () => {
    if (baseEditor.isInitialized) return embedEditor;
    
    baseEditor.init();
    embedCode = baseEditor.sectionContainer.innerHTML;
    
    const iframe = baseEditor.sectionContainer.querySelector('iframe');
    const video = baseEditor.sectionContainer.querySelector('video');
    
    if (iframe) embedType = 'iframe';
    else if (video) embedType = 'video';
    else embedType = 'custom';
    
    return embedEditor;
  };

  const save = (action = 'apply') => {
    baseEditor.save(action);
    return embedEditor;
  };

  const setEmbedCode = (code) => {
    embedCode = code;
    baseEditor.sectionContainer.innerHTML = code;
  };

  const getEmbedCode = () => {
    return baseEditor.sectionContainer.innerHTML;
  };

  const validateChanges = () => {
    const code = getEmbedCode().trim();
    return code.length > 0;
  };

  const embedEditor = {
    ...baseEditor,
    init,
    save,
    validateChanges,
    setEmbedCode,
    getEmbedCode,
    get embedCode() { return embedCode; },
    get embedType() { return embedType; }
  };

  return embedEditor;
};
