/**
 * 图片编辑器
 * 函数式实现的图片区块编辑器
 */

import { createBaseEditor } from './sectionEditorFactory.js';

/**
 * 创建图片编辑器
 * @param {HTMLElement} section - 图片区块元素
 * @param {Object} toolbar - 工具栏对象
 * @param {Object} state - 编辑器状态
 * @returns {Object} 图片编辑器实例
 */
export const createImageEditor = (section, toolbar, state) => {
  const baseEditor = createBaseEditor(section, toolbar, state);
  
  let imageElement = null;
  let selectedUnsplashPhoto = null;

  const init = () => {
    if (baseEditor.isInitialized) return imageEditor;
    
    baseEditor.init();
    imageElement = baseEditor.sectionContainer.querySelector('img');
    
    return imageEditor;
  };

  const save = (action = 'apply') => {
    baseEditor.save(action);
    return imageEditor;
  };

  const setImageSrc = (src) => {
    if (imageElement) {
      imageElement.src = src;
    }
  };

  const getImageSrc = () => {
    return imageElement?.src || '';
  };

  const setImageAlt = (alt) => {
    if (imageElement) {
      imageElement.alt = alt;
    }
  };

  const getImageAlt = () => {
    return imageElement?.alt || '';
  };

  const saveSelectedUnsplashPhoto = () => {
    if (selectedUnsplashPhoto) {
      // 保存Unsplash照片逻辑
      console.log('Saving Unsplash photo:', selectedUnsplashPhoto);
    }
  };

  const imageEditor = {
    ...baseEditor,
    init,
    save,
    setImageSrc,
    getImageSrc,
    setImageAlt,
    getImageAlt,
    saveSelectedUnsplashPhoto,
    get imageElement() { return imageElement; },
    get selectedUnsplashPhoto() { return selectedUnsplashPhoto; },
    set selectedUnsplashPhoto(photo) { selectedUnsplashPhoto = photo; }
  };

  return imageEditor;
};
