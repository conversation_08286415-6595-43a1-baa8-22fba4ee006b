/**
 * 节点按钮编辑器
 * 函数式实现的节点按钮区块编辑器
 */

import { createButtonEditor } from './buttonEditor.js';

export const createNodeButtonEditor = (section, toolbar, state) => {
  const buttonEditor = createButtonEditor(section, toolbar, state);
  
  // 节点按钮特有的功能
  let nodeAction = '';
  let nodeTarget = '';

  const originalInit = buttonEditor.init;
  const originalSave = buttonEditor.save;

  const init = () => {
    const result = originalInit();
    
    // 节点按钮特有的初始化
    nodeAction = section.getAttribute('data-node-action') || '';
    nodeTarget = section.getAttribute('data-node-target') || '';
    
    return nodeButtonEditor;
  };

  const save = (action = 'apply') => {
    // 保存节点特有属性
    if (nodeAction) {
      section.setAttribute('data-node-action', nodeAction);
    }
    if (nodeTarget) {
      section.setAttribute('data-node-target', nodeTarget);
    }
    
    return originalSave(action);
  };

  const setNodeAction = (action) => {
    nodeAction = action;
  };

  const getNodeAction = () => {
    return nodeAction;
  };

  const setNodeTarget = (target) => {
    nodeTarget = target;
  };

  const getNodeTarget = () => {
    return nodeTarget;
  };

  const nodeButtonEditor = {
    ...buttonEditor,
    init,
    save,
    setNodeAction,
    getNodeAction,
    setNodeTarget,
    getNodeTarget,
    get nodeAction() { return nodeAction; },
    get nodeTarget() { return nodeTarget; }
  };

  return nodeButtonEditor;
};
