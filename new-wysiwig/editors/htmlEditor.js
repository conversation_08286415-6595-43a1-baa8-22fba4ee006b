/**
 * HTML编辑器
 * 函数式实现的HTML区块编辑器
 */

import { createBaseEditor } from './sectionEditorFactory.js';

export const createHtmlEditor = (section, toolbar, state) => {
  const baseEditor = createBaseEditor(section, toolbar, state);
  
  let htmlContent = '';

  const init = () => {
    if (baseEditor.isInitialized) return htmlEditor;
    
    baseEditor.init();
    htmlContent = baseEditor.sectionContainer.innerHTML;
    
    return htmlEditor;
  };

  const save = (action = 'apply') => {
    baseEditor.save(action);
    return htmlEditor;
  };

  const setHtmlContent = (html) => {
    htmlContent = html;
    baseEditor.sectionContainer.innerHTML = html;
  };

  const getHtmlContent = () => {
    return baseEditor.sectionContainer.innerHTML;
  };

  const validateChanges = () => {
    const content = getHtmlContent().trim();
    return content.length > 0;
  };

  const htmlEditor = {
    ...baseEditor,
    init,
    save,
    validateChanges,
    setHtmlContent,
    getHtmlContent,
    get htmlContent() { return htmlContent; }
  };

  return htmlEditor;
};
