/**
 * 表情编辑器
 * 函数式实现的表情区块编辑器
 */

import { createBaseEditor } from './sectionEditorFactory.js';

export const createEmojiEditor = (section, toolbar, state) => {
  const baseEditor = createBaseEditor(section, toolbar, state);
  
  let selectedEmoji = '';

  const init = () => {
    if (baseEditor.isInitialized) return emojiEditor;
    
    baseEditor.init();
    selectedEmoji = baseEditor.sectionContainer.textContent || '';
    
    return emojiEditor;
  };

  const save = (action = 'apply') => {
    baseEditor.save(action);
    return emojiEditor;
  };

  const setEmoji = (emoji) => {
    selectedEmoji = emoji;
    baseEditor.sectionContainer.textContent = emoji;
  };

  const getEmoji = () => {
    return baseEditor.sectionContainer.textContent || '';
  };

  const validateChanges = () => {
    return getEmoji().length > 0;
  };

  const emojiEditor = {
    ...baseEditor,
    init,
    save,
    validateChanges,
    setEmoji,
    getEmoji,
    get selectedEmoji() { return selectedEmoji; }
  };

  return emojiEditor;
};
