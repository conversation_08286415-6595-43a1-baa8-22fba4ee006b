/**
 * 文本编辑器
 * 函数式实现的文本区块编辑器
 */

import { createBaseEditor } from './sectionEditorFactory.js';
import { createWysiwygOperations } from '../utils/wysiwygOperations.js';
import { createUndoRedoManager } from '../utils/undoRedoManager.js';

/**
 * 创建文本编辑器
 * @param {HTMLElement} section - 文本区块元素
 * @param {Object} toolbar - 工具栏对象
 * @param {Object} state - 编辑器状态
 * @returns {Object} 文本编辑器实例
 */
export const createTextEditor = (section, toolbar, state) => {
  const baseEditor = createBaseEditor(section, toolbar, state);
  
  // 文本编辑器特有的状态
  let undoRedoManager = null;
  let wysiwygOperations = null;
  let keyboardHandlerRef = null;
  let caretMovementRef = null;

  // 当前字体值
  const currentFontValues = {
    fontSize: '',
    fontFamily: '',
    textTransform: '',
    lineHeight: '',
    letterSpacing: '',
    fontWeight: '',
    fontStyle: '',
    textDecoration: ''
  };

  /**
   * 初始化文本编辑器
   */
  const init = () => {
    if (baseEditor.isInitialized) return textEditor;

    // 调用基础初始化
    baseEditor.init();

    // 运行文本区块完整性检查
    runTextSectionIntegrity(baseEditor.sectionContainer, baseEditor.iframeDocument);

    // 创建撤销重做管理器
    undoRedoManager = createUndoRedoManager(textEditor, 'text');

    // 创建WYSIWYG操作工具
    wysiwygOperations = createWysiwygOperations(textEditor);

    // 设置内容可编辑
    baseEditor.sectionContainer.contentEditable = true;

    // 标准化文本
    normalizeText();

    // 设置光标位置
    wysiwygOperations.collapseCaretOnNode(baseEditor.sectionContainer);

    // 查找活动样式
    findActiveStyles();

    // 初始化事件监听器
    initEventListeners();

    return textEditor;
  };

  /**
   * 保存文本编辑器
   * @param {string} action - 保存动作类型
   */
  const save = (action = 'apply') => {
    baseEditor.save(action);

    // 销毁撤销重做管理器
    undoRedoManager?.destroy();

    // 重置光标颜色
    baseEditor.sectionContainer.style.caretColor = '';

    // 检查是否为空文本容器
    if (isEmptyTextContainer(baseEditor.sectionContainer)) {
      baseEditor.sectionContainer.innerHTML = '<div class="userpilot-builder-block"><span class="userpilot-text-node"></span></div>';
    } else {
      runTextSectionIntegrity(baseEditor.sectionContainer, baseEditor.iframeDocument);
      wysiwygOperations?.removeZeroWidthSpace();
      normalizeText();
    }

    // 移除事件监听器
    removeEventListeners();

    return textEditor;
  };

  /**
   * 销毁文本编辑器
   */
  const destroy = () => {
    undoRedoManager?.destroy();
    removeEventListeners();
    baseEditor.destroy();
    return textEditor;
  };

  /**
   * 初始化事件监听器
   */
  const initEventListeners = () => {
    keyboardHandlerRef = handleKeydown.bind(textEditor);
    caretMovementRef = handleCaretMovement.bind(textEditor);

    const container = baseEditor.sectionContainer;
    
    container.addEventListener('keydown', keyboardHandlerRef, true);
    container.addEventListener('keyup', caretMovementRef, true);
    container.addEventListener('mouseup', caretMovementRef, true);
    container.addEventListener('paste', handlePaste);

    // AI相关事件（如果需要）
    container.addEventListener('ai-is-calling', handleAICall, true);
  };

  /**
   * 移除事件监听器
   */
  const removeEventListeners = () => {
    const container = baseEditor.sectionContainer;
    
    if (keyboardHandlerRef) {
      container.removeEventListener('keydown', keyboardHandlerRef, true);
    }
    if (caretMovementRef) {
      container.removeEventListener('keyup', caretMovementRef, true);
      container.removeEventListener('mouseup', caretMovementRef, true);
    }
    container.removeEventListener('paste', handlePaste);
    container.removeEventListener('ai-is-calling', handleAICall, true);
  };

  /**
   * 处理键盘事件
   * @param {KeyboardEvent} event - 键盘事件
   */
  const handleKeydown = (event) => {
    switch (event.code) {
      case 'Backspace':
        return wysiwygOperations?.handleBackspaceKey(event) ?? true;
      case 'Enter':
        return handleEnterKey(event);
      case 'Tab':
        return handleTabKey(event);
      default:
        return true;
    }
  };

  /**
   * 处理回车键
   * @param {KeyboardEvent} event - 键盘事件
   */
  const handleEnterKey = (event) => {
    // 防止默认行为，创建新的文本块
    event.preventDefault();
    
    const selection = baseEditor.iframeWindow.getSelection();
    if (selection.rangeCount === 0) return false;

    const range = selection.getRangeAt(0);
    const newBlock = createTextBuilderBlock();
    
    // 在当前位置插入新块
    range.deleteContents();
    range.insertNode(newBlock);
    
    // 设置光标到新块
    const newRange = baseEditor.iframeDocument.createRange();
    newRange.setStart(newBlock.firstChild, 0);
    newRange.collapse(true);
    selection.removeAllRanges();
    selection.addRange(newRange);

    return false;
  };

  /**
   * 处理Tab键
   * @param {KeyboardEvent} event - 键盘事件
   */
  const handleTabKey = (event) => {
    event.preventDefault();
    
    // 插入制表符或空格
    const selection = baseEditor.iframeWindow.getSelection();
    if (selection.rangeCount === 0) return false;

    const range = selection.getRangeAt(0);
    const tabText = baseEditor.iframeDocument.createTextNode('\t');
    range.deleteContents();
    range.insertNode(tabText);
    range.setStartAfter(tabText);
    range.collapse(true);

    return false;
  };

  /**
   * 处理光标移动
   * @param {Event} event - 事件对象
   */
  const handleCaretMovement = (event) => {
    findActiveStyles();
    updateToolbarState();
  };

  /**
   * 处理粘贴事件
   * @param {ClipboardEvent} event - 粘贴事件
   */
  const handlePaste = (event) => {
    event.preventDefault();
    
    const clipboardData = event.clipboardData || baseEditor.iframeWindow.clipboardData;
    const pastedText = clipboardData.getData('text/plain');
    
    if (pastedText) {
      const selection = baseEditor.iframeWindow.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        range.deleteContents();
        
        const textNode = baseEditor.iframeDocument.createTextNode(pastedText);
        range.insertNode(textNode);
        range.setStartAfter(textNode);
        range.collapse(true);
      }
    }
  };

  /**
   * 处理AI调用事件
   * @param {CustomEvent} event - AI事件
   */
  const handleAICall = (event) => {
    // AI功能处理逻辑
    console.log('AI call event:', event.detail);
  };

  /**
   * 标准化文本内容
   */
  const normalizeText = () => {
    const blocks = baseEditor.sectionContainer.querySelectorAll('.userpilot-builder-block');
    
    blocks.forEach(block => {
      if (!block.textContent.trim()) {
        block.innerHTML = '<span class="userpilot-text-node">\u200B</span>';
      }
    });
  };

  /**
   * 查找活动样式
   */
  const findActiveStyles = () => {
    const selection = baseEditor.iframeWindow.getSelection();
    if (selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const element = range.commonAncestorContainer.nodeType === Node.TEXT_NODE
      ? range.commonAncestorContainer.parentElement
      : range.commonAncestorContainer;

    const computedStyle = baseEditor.iframeWindow.getComputedStyle(element);
    
    currentFontValues.fontSize = computedStyle.fontSize;
    currentFontValues.fontFamily = computedStyle.fontFamily;
    currentFontValues.fontWeight = computedStyle.fontWeight;
    currentFontValues.fontStyle = computedStyle.fontStyle;
    currentFontValues.textDecoration = computedStyle.textDecoration;
    currentFontValues.textTransform = computedStyle.textTransform;
    currentFontValues.lineHeight = computedStyle.lineHeight;
    currentFontValues.letterSpacing = computedStyle.letterSpacing;
  };

  /**
   * 更新工具栏状态
   */
  const updateToolbarState = () => {
    // 更新工具栏按钮状态以反映当前样式
    toolbar.updateState?.(currentFontValues);
  };

  /**
   * 创建文本构建块
   * @param {boolean} withSpan - 是否包含span元素
   * @returns {HTMLElement} 文本构建块
   */
  const createTextBuilderBlock = (withSpan = true) => {
    const builderBlock = baseEditor.iframeDocument.createElement('div');
    builderBlock.classList.add('userpilot-builder-block');
    
    if (withSpan) {
      const span = baseEditor.iframeDocument.createElement('span');
      span.classList.add('userpilot-text-node');
      span.textContent = '\u200B';
      builderBlock.appendChild(span);
    }
    
    return builderBlock;
  };

  /**
   * 获取区块选择目标
   * @returns {HTMLElement} 选择目标元素
   */
  const getSectionSelectionTarget = () => {
    const blocks = baseEditor.sectionContainer.querySelectorAll('.userpilot-builder-block');
    const lastBlock = blocks[blocks.length - 1];
    
    if (lastBlock && lastBlock.lastChild) {
      return lastBlock.lastChild;
    }
    
    return baseEditor.sectionContainer;
  };

  /**
   * 验证更改
   * @returns {boolean} 验证是否通过
   */
  const validateChanges = () => {
    const textContent = baseEditor.sectionContainer.textContent.trim();
    return textContent.length > 0 || textContent === '\u200B';
  };

  // 文本编辑器对象
  const textEditor = {
    ...baseEditor,
    init,
    save,
    destroy,
    validateChanges,
    getSectionSelectionTarget,
    
    // 文本编辑器特有方法
    normalizeText,
    findActiveStyles,
    updateToolbarState,
    createTextBuilderBlock,
    
    // 事件处理器
    handleKeydown,
    handleCaretMovement,
    handlePaste,
    handleAICall,
    
    // 访问器
    get currentFontValues() { return { ...currentFontValues }; },
    get undoRedoManager() { return undoRedoManager; },
    get wysiwygOperations() { return wysiwygOperations; }
  };

  return textEditor;
};

/**
 * 运行文本区块完整性检查
 * @param {HTMLElement} container - 文本容器
 * @param {Document} doc - 文档对象
 */
const runTextSectionIntegrity = (container, doc) => {
  // 确保文本区块结构正确
  const blocks = container.querySelectorAll('.userpilot-builder-block');
  
  if (blocks.length === 0) {
    const newBlock = doc.createElement('div');
    newBlock.classList.add('userpilot-builder-block');
    newBlock.innerHTML = '<span class="userpilot-text-node">\u200B</span>';
    container.appendChild(newBlock);
  }
};

/**
 * 检查是否为空文本容器
 * @param {HTMLElement} container - 文本容器
 * @returns {boolean} 是否为空
 */
const isEmptyTextContainer = (container) => {
  const textContent = container.textContent.replace(/\u200B/g, '').trim();
  return textContent.length === 0;
};
