/**
 * 输入编辑器
 * 函数式实现的表单输入区块编辑器
 */

import { createBaseEditor } from './sectionEditorFactory.js';

export const createInputEditor = (section, toolbar, state) => {
  const baseEditor = createBaseEditor(section, toolbar, state);
  
  let inputElement = null;
  let inputType = 'text';

  const init = () => {
    if (baseEditor.isInitialized) return inputEditor;
    
    baseEditor.init();
    inputElement = baseEditor.sectionContainer.querySelector('input, textarea, select');
    
    if (inputElement) {
      inputType = inputElement.type || inputElement.tagName.toLowerCase();
    }
    
    return inputEditor;
  };

  const save = (action = 'apply') => {
    baseEditor.save(action);
    return inputEditor;
  };

  const setPlaceholder = (placeholder) => {
    if (inputElement) {
      inputElement.placeholder = placeholder;
    }
  };

  const getPlaceholder = () => {
    return inputElement?.placeholder || '';
  };

  const setRequired = (required) => {
    if (inputElement) {
      inputElement.required = required;
    }
  };

  const getRequired = () => {
    return inputElement?.required || false;
  };

  const validateChanges = () => {
    return true; // 输入字段总是有效的
  };

  const inputEditor = {
    ...baseEditor,
    init,
    save,
    validateChanges,
    setPlaceholder,
    getPlaceholder,
    setRequired,
    getRequired,
    get inputElement() { return inputElement; },
    get inputType() { return inputType; }
  };

  return inputEditor;
};
