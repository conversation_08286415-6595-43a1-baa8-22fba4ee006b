/**
 * 按钮编辑器
 * 函数式实现的按钮区块编辑器
 */

import { createBaseEditor } from './sectionEditorFactory.js';
import { createWysiwygOperations } from '../utils/wysiwygOperations.js';
import { createUndoRedoManager } from '../utils/undoRedoManager.js';

/**
 * 创建按钮编辑器
 * @param {HTMLElement} section - 按钮区块元素
 * @param {Object} toolbar - 工具栏对象
 * @param {Object} state - 编辑器状态
 * @returns {Object} 按钮编辑器实例
 */
export const createButtonEditor = (section, toolbar, state) => {
  const baseEditor = createBaseEditor(section, toolbar, state);
  
  // 按钮编辑器特有的状态
  let button = null;
  let buttonStyles = null;
  let undoRedoManager = null;
  let wysiwygOperations = null;
  let actionSelected = '';
  let pasteListenerRef = null;

  /**
   * 初始化按钮编辑器
   */
  const init = () => {
    if (baseEditor.isInitialized) return buttonEditor;

    // 调用基础初始化
    baseEditor.init();

    // 获取按钮元素
    button = baseEditor.sectionContainer.querySelector('.userpilot-btn');
    if (!button) {
      console.error('Button element not found in section');
      return buttonEditor;
    }

    // 获取按钮样式
    buttonStyles = baseEditor.iframeWindow.getComputedStyle(button);
    
    // 设置按钮可编辑
    button.contentEditable = true;

    // 创建撤销重做管理器
    undoRedoManager = createUndoRedoManager(buttonEditor, 'button');

    // 创建WYSIWYG操作工具
    wysiwygOperations = createWysiwygOperations(buttonEditor);

    // 设置选择目标
    const targetSelectionElement = getSectionSelectionTarget();
    wysiwygOperations.selection.selectNodeContents(targetSelectionElement);
    wysiwygOperations.selection.collapse(false);
    wysiwygOperations.resetSectionRange();

    // 初始化事件监听器
    initEventListeners();

    return buttonEditor;
  };

  /**
   * 保存按钮编辑器
   * @param {string} action - 保存动作类型
   */
  const save = (action = 'apply') => {
    baseEditor.save(action);

    // 设置按钮不可编辑
    if (button) {
      button.contentEditable = false;
    }

    // 移除事件监听器
    removeEventListeners();

    return buttonEditor;
  };

  /**
   * 销毁按钮编辑器
   */
  const destroy = () => {
    undoRedoManager?.destroy();
    removeEventListeners();
    baseEditor.destroy();
    return buttonEditor;
  };

  /**
   * 初始化事件监听器
   */
  const initEventListeners = () => {
    pasteListenerRef = handlePaste.bind(buttonEditor);
    
    if (button) {
      button.addEventListener('paste', pasteListenerRef);
      button.addEventListener('keydown', handleKeydown);
      button.addEventListener('input', handleInput);
    }
  };

  /**
   * 移除事件监听器
   */
  const removeEventListeners = () => {
    if (button && pasteListenerRef) {
      button.removeEventListener('paste', pasteListenerRef);
      button.removeEventListener('keydown', handleKeydown);
      button.removeEventListener('input', handleInput);
    }
  };

  /**
   * 处理键盘事件
   * @param {KeyboardEvent} event - 键盘事件
   */
  const handleKeydown = (event) => {
    switch (event.code) {
      case 'Enter':
        // 按钮文本中不允许换行
        event.preventDefault();
        return false;
      case 'Tab':
        event.preventDefault();
        return false;
      default:
        return true;
    }
  };

  /**
   * 处理输入事件
   * @param {InputEvent} event - 输入事件
   */
  const handleInput = (event) => {
    // 清理按钮文本，移除不必要的HTML标签
    cleanButtonText();
    
    // 更新工具栏状态
    updateToolbarState();
  };

  /**
   * 处理粘贴事件
   * @param {ClipboardEvent} event - 粘贴事件
   */
  const handlePaste = (event) => {
    event.preventDefault();
    
    const clipboardData = event.clipboardData || baseEditor.iframeWindow.clipboardData;
    const pastedText = clipboardData.getData('text/plain');
    
    if (pastedText) {
      // 只插入纯文本，不允许HTML
      const selection = baseEditor.iframeWindow.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        range.deleteContents();
        
        const textNode = baseEditor.iframeDocument.createTextNode(pastedText);
        range.insertNode(textNode);
        range.setStartAfter(textNode);
        range.collapse(true);
      }
    }
  };

  /**
   * 清理按钮文本
   */
  const cleanButtonText = () => {
    if (!button) return;

    // 移除不必要的HTML标签，只保留纯文本
    const text = button.textContent || button.innerText || '';
    if (button.innerHTML !== text) {
      button.textContent = text;
    }
  };

  /**
   * 更新工具栏状态
   */
  const updateToolbarState = () => {
    if (!button) return;

    const currentStyles = {
      fontSize: buttonStyles.fontSize,
      fontFamily: buttonStyles.fontFamily,
      fontWeight: buttonStyles.fontWeight,
      color: buttonStyles.color,
      backgroundColor: buttonStyles.backgroundColor,
      borderRadius: buttonStyles.borderRadius,
      padding: buttonStyles.padding,
      textAlign: buttonStyles.textAlign
    };

    toolbar.updateState?.(currentStyles);
  };

  /**
   * 获取区块选择目标
   * @returns {HTMLElement} 选择目标元素
   */
  const getSectionSelectionTarget = () => {
    if (!button) return baseEditor.sectionContainer;
    
    if (button.firstChild) {
      return button.firstChild;
    } else {
      return button;
    }
  };

  /**
   * 验证更改
   * @returns {boolean} 验证是否通过
   */
  const validateChanges = () => {
    if (!button) return false;
    
    const text = button.textContent?.trim() || '';
    return text.length > 0;
  };

  /**
   * 设置按钮文本
   * @param {string} text - 按钮文本
   */
  const setButtonText = (text) => {
    if (button) {
      button.textContent = text;
    }
  };

  /**
   * 获取按钮文本
   * @returns {string} 按钮文本
   */
  const getButtonText = () => {
    return button?.textContent || '';
  };

  /**
   * 设置按钮样式
   * @param {Object} styles - 样式对象
   */
  const setButtonStyles = (styles) => {
    if (!button) return;

    Object.entries(styles).forEach(([property, value]) => {
      button.style[property] = value;
    });

    // 更新样式引用
    buttonStyles = baseEditor.iframeWindow.getComputedStyle(button);
  };

  /**
   * 获取按钮样式
   * @returns {Object} 按钮样式对象
   */
  const getButtonStyles = () => {
    if (!buttonStyles) return {};

    return {
      fontSize: buttonStyles.fontSize,
      fontFamily: buttonStyles.fontFamily,
      fontWeight: buttonStyles.fontWeight,
      fontStyle: buttonStyles.fontStyle,
      color: buttonStyles.color,
      backgroundColor: buttonStyles.backgroundColor,
      borderColor: buttonStyles.borderColor,
      borderWidth: buttonStyles.borderWidth,
      borderStyle: buttonStyles.borderStyle,
      borderRadius: buttonStyles.borderRadius,
      padding: buttonStyles.padding,
      margin: buttonStyles.margin,
      textAlign: buttonStyles.textAlign,
      textDecoration: buttonStyles.textDecoration,
      textTransform: buttonStyles.textTransform,
      lineHeight: buttonStyles.lineHeight,
      letterSpacing: buttonStyles.letterSpacing
    };
  };

  /**
   * 设置按钮动作
   * @param {string} action - 动作类型
   * @param {Object} config - 动作配置
   */
  const setButtonAction = (action, config = {}) => {
    actionSelected = action;
    
    // 在按钮元素上设置动作属性
    if (button) {
      button.setAttribute('data-action', action);
      
      // 根据动作类型设置其他属性
      Object.entries(config).forEach(([key, value]) => {
        button.setAttribute(`data-${key}`, value);
      });
    }
  };

  /**
   * 获取按钮动作
   * @returns {Object} 动作配置对象
   */
  const getButtonAction = () => {
    if (!button) return { action: '', config: {} };

    const action = button.getAttribute('data-action') || '';
    const config = {};

    // 获取所有data-*属性作为配置
    Array.from(button.attributes).forEach(attr => {
      if (attr.name.startsWith('data-') && attr.name !== 'data-action') {
        const key = attr.name.replace('data-', '');
        config[key] = attr.value;
      }
    });

    return { action, config };
  };

  /**
   * 重置按钮到默认状态
   */
  const resetButton = () => {
    if (!button) return;

    button.textContent = 'Button';
    button.removeAttribute('style');
    
    // 移除所有data-*属性
    Array.from(button.attributes).forEach(attr => {
      if (attr.name.startsWith('data-')) {
        button.removeAttribute(attr.name);
      }
    });

    actionSelected = '';
  };

  // 按钮编辑器对象
  const buttonEditor = {
    ...baseEditor,
    init,
    save,
    destroy,
    validateChanges,
    getSectionSelectionTarget,
    
    // 按钮编辑器特有方法
    setButtonText,
    getButtonText,
    setButtonStyles,
    getButtonStyles,
    setButtonAction,
    getButtonAction,
    resetButton,
    cleanButtonText,
    updateToolbarState,
    
    // 事件处理器
    handleKeydown,
    handleInput,
    handlePaste,
    
    // 访问器
    get button() { return button; },
    get buttonStyles() { return buttonStyles; },
    get actionSelected() { return actionSelected; },
    get undoRedoManager() { return undoRedoManager; },
    get wysiwygOperations() { return wysiwygOperations; }
  };

  return buttonEditor;
};
