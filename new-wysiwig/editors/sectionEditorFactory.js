/**
 * 区块编辑器工厂
 * 使用函数式方法创建不同类型的区块编辑器
 */

import { getSectionType } from '../utils/sectionUtils.js';
import { createHeaderEditor } from './headerEditor.js';
import { createTextEditor } from './textEditor.js';
import { createButtonEditor } from './buttonEditor.js';
import { createImageEditor } from './imageEditor.js';
import { createHtmlEditor } from './htmlEditor.js';
import { createEmojiEditor } from './emojiEditor.js';
import { createInputEditor } from './inputEditor.js';
import { createEmbedEditor } from './embedEditor.js';
import { createNodeButtonEditor } from './nodeButtonEditor.js';

// 编辑器类型映射
const EDITOR_TYPE_MAP = {
  'userpilot-header-section': createHeaderEditor,
  'userpilot-text-section': createTextEditor,
  'userpilot-button-section': createButtonEditor,
  'userpilot-image-section': createImageEditor,
  'userpilot-html-section': createHtmlEditor,
  'userpilot-emoji-section': createEmojiEditor,
  'userpilot-input-text': createInputEditor,
  'userpilot-input-text-large': createInputEditor,
  'userpilot-input-radio': createInputEditor,
  'userpilot-input-likert-scale': createInputEditor,
  'userpilot-embed-section': createEmbedEditor,
  'userpilot-node-button': createNodeButtonEditor
};

/**
 * 创建区块编辑器
 * @param {HTMLElement} section - 区块元素
 * @param {Object} toolbar - 工具栏对象
 * @param {Object} state - 编辑器状态
 * @returns {Object} 区块编辑器实例
 */
export const createSectionEditor = (section, toolbar, state) => {
  const sectionType = getSectionType(section);
  const editorFactory = EDITOR_TYPE_MAP[sectionType];

  if (!editorFactory) {
    console.error(`DevError: Unsupported section type: ${sectionType}`);
    return null;
  }

  return editorFactory(section, toolbar, state);
};

/**
 * 获取支持的编辑器类型列表
 * @returns {Array} 支持的编辑器类型
 */
export const getSupportedEditorTypes = () => {
  return Object.keys(EDITOR_TYPE_MAP);
};

/**
 * 检查是否支持指定的编辑器类型
 * @param {string} sectionType - 区块类型
 * @returns {boolean} 是否支持
 */
export const isSupportedEditorType = (sectionType) => {
  return sectionType in EDITOR_TYPE_MAP;
};

/**
 * 注册新的编辑器类型
 * @param {string} sectionType - 区块类型
 * @param {Function} editorFactory - 编辑器工厂函数
 */
export const registerEditorType = (sectionType, editorFactory) => {
  if (typeof editorFactory !== 'function') {
    throw new Error('Editor factory must be a function');
  }
  
  EDITOR_TYPE_MAP[sectionType] = editorFactory;
};

/**
 * 注销编辑器类型
 * @param {string} sectionType - 区块类型
 */
export const unregisterEditorType = (sectionType) => {
  delete EDITOR_TYPE_MAP[sectionType];
};

/**
 * 创建基础编辑器接口
 * 所有编辑器都应该实现这个接口
 * @param {HTMLElement} section - 区块元素
 * @param {Object} toolbar - 工具栏对象
 * @param {Object} state - 编辑器状态
 * @returns {Object} 基础编辑器对象
 */
export const createBaseEditor = (section, toolbar, state) => {
  const baseEditor = {
    // 基础属性
    sectionContainer: section,
    toolbar,
    state,
    iframeDocument: state.iframeDocument,
    iframeWindow: state.iframeWindow,
    
    // 编辑器状态
    isInitialized: false,
    isActive: false,
    
    // 基础方法
    init: () => {
      if (baseEditor.isInitialized) return;
      
      baseEditor.sectionContainer.setAttribute('draggable', 'false');
      baseEditor.isInitialized = true;
      baseEditor.isActive = true;
      
      return baseEditor;
    },

    save: (action = 'apply') => {
      baseEditor.sectionContainer.contentEditable = false;
      
      const isProgressionButton = ['userpilot-next-button', 'userpilot-back-button']
        .includes(baseEditor.sectionContainer.id);
      
      baseEditor.sectionContainer.setAttribute('draggable', isProgressionButton ? 'false' : 'true');
      baseEditor.isActive = false;
      
      return baseEditor;
    },

    destroy: () => {
      baseEditor.isInitialized = false;
      baseEditor.isActive = false;
      return baseEditor;
    },

    validateChanges: () => {
      // 默认验证通过，子编辑器可以重写
      return true;
    },

    // 工具方法
    createTextSpan: (empty = false) => {
      const textSpan = document.createElement('span');
      if (!empty) textSpan.textContent = '\u200B';
      return textSpan;
    },

    // 获取区块选择目标
    getSectionSelectionTarget: () => {
      return baseEditor.sectionContainer;
    },

    // 事件处理器
    handleKeydown: (event) => {
      // 默认键盘事件处理，子编辑器可以重写
      return true;
    },

    handleMouseup: (event) => {
      // 默认鼠标事件处理，子编辑器可以重写
      return true;
    },

    handlePaste: (event) => {
      // 默认粘贴事件处理，子编辑器可以重写
      return true;
    }
  };

  return baseEditor;
};

/**
 * 编辑器工厂配置
 */
export const editorFactoryConfig = {
  // 默认配置
  defaults: {
    contentEditable: true,
    draggable: false,
    undoRedoEnabled: true,
    autoSave: false,
    autoSaveDelay: 1000
  },

  // 验证配置
  validation: {
    required: ['sectionContainer', 'toolbar', 'state'],
    optional: ['undoRedoManager', 'wysiwygOperations']
  }
};

/**
 * 验证编辑器配置
 * @param {Object} config - 编辑器配置
 * @returns {boolean} 配置是否有效
 */
export const validateEditorConfig = (config) => {
  const { required } = editorFactoryConfig.validation;
  
  return required.every(prop => {
    if (!(prop in config)) {
      console.error(`Missing required property: ${prop}`);
      return false;
    }
    return true;
  });
};

/**
 * 合并编辑器配置
 * @param {Object} config - 用户配置
 * @returns {Object} 合并后的配置
 */
export const mergeEditorConfig = (config) => {
  return {
    ...editorFactoryConfig.defaults,
    ...config
  };
};

/**
 * 编辑器工厂调试工具
 */
export const factoryDebugTools = {
  /**
   * 列出所有注册的编辑器类型
   */
  listRegisteredTypes: () => {
    console.table(Object.keys(EDITOR_TYPE_MAP));
  },

  /**
   * 测试编辑器工厂
   * @param {string} sectionType - 区块类型
   * @param {HTMLElement} mockSection - 模拟区块元素
   * @param {Object} mockToolbar - 模拟工具栏
   * @param {Object} mockState - 模拟状态
   */
  testEditorFactory: (sectionType, mockSection, mockToolbar, mockState) => {
    try {
      const editor = createSectionEditor(mockSection, mockToolbar, mockState);
      console.log(`✅ Editor factory test passed for type: ${sectionType}`, editor);
      return true;
    } catch (error) {
      console.error(`❌ Editor factory test failed for type: ${sectionType}`, error);
      return false;
    }
  },

  /**
   * 批量测试所有编辑器工厂
   * @param {HTMLElement} mockSection - 模拟区块元素
   * @param {Object} mockToolbar - 模拟工具栏
   * @param {Object} mockState - 模拟状态
   */
  testAllFactories: (mockSection, mockToolbar, mockState) => {
    const results = {};
    
    Object.keys(EDITOR_TYPE_MAP).forEach(type => {
      results[type] = factoryDebugTools.testEditorFactory(type, mockSection, mockToolbar, mockState);
    });

    console.table(results);
    return results;
  }
};
