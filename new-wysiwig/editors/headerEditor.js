/**
 * 标题编辑器
 * 函数式实现的标题区块编辑器
 */

import { createBaseEditor } from './sectionEditorFactory.js';
import { createWysiwygOperations } from '../utils/wysiwygOperations.js';
import { createUndoRedoManager } from '../utils/undoRedoManager.js';

/**
 * 创建标题编辑器
 * @param {HTMLElement} section - 标题区块元素
 * @param {Object} toolbar - 工具栏对象
 * @param {Object} state - 编辑器状态
 * @returns {Object} 标题编辑器实例
 */
export const createHeaderEditor = (section, toolbar, state) => {
  const baseEditor = createBaseEditor(section, toolbar, state);
  
  // 标题编辑器特有的状态
  let undoRedoManager = null;
  let wysiwygOperations = null;
  let keyboardHandlerRef = null;

  /**
   * 初始化标题编辑器
   */
  const init = () => {
    if (baseEditor.isInitialized) return headerEditor;

    // 调用基础初始化
    baseEditor.init();

    // 运行个性化节点检查
    runPersonalizationNodeChecks(baseEditor.sectionContainer);

    // 创建撤销重做管理器
    undoRedoManager = createUndoRedoManager(headerEditor, 'text');

    // 创建WYSIWYG操作工具
    wysiwygOperations = createWysiwygOperations(headerEditor);

    // 设置内容可编辑
    baseEditor.sectionContainer.contentEditable = true;

    // 设置光标位置
    wysiwygOperations.collapseCaretOnNode(getSectionSelectionTarget());

    // 初始化事件监听器
    initEventListeners();

    return headerEditor;
  };

  /**
   * 保存标题编辑器
   * @param {string} action - 保存动作类型
   */
  const save = (action = 'apply') => {
    baseEditor.save(action);

    // 销毁撤销重做管理器
    undoRedoManager?.destroy();

    // 移除零宽度空格
    wysiwygOperations?.removeZeroWidthSpace();

    // 检查内容是否为空
    const textContent = baseEditor.sectionContainer.textContent
      .replaceAll('\u200B', '').trim();
    
    if (!textContent.length) {
      baseEditor.sectionContainer.innerHTML = '<div class="userpilot-builder-block userpilot-h2"></div>';
    }

    // 移除事件监听器
    removeEventListeners();

    return headerEditor;
  };

  /**
   * 销毁标题编辑器
   */
  const destroy = () => {
    undoRedoManager?.destroy();
    removeEventListeners();
    baseEditor.destroy();
    return headerEditor;
  };

  /**
   * 初始化事件监听器
   */
  const initEventListeners = () => {
    keyboardHandlerRef = handleKeydown.bind(headerEditor);
    baseEditor.sectionContainer.addEventListener('keydown', keyboardHandlerRef, true);
  };

  /**
   * 移除事件监听器
   */
  const removeEventListeners = () => {
    if (keyboardHandlerRef) {
      baseEditor.sectionContainer.removeEventListener('keydown', keyboardHandlerRef, true);
    }
  };

  /**
   * 处理键盘事件
   * @param {KeyboardEvent} event - 键盘事件
   */
  const handleKeydown = (event) => {
    switch (event.code) {
      case 'Backspace':
        return wysiwygOperations?.handleBackspaceKey(event) ?? true;
      case 'Enter':
        return handleEnterKey(event);
      default:
        return true;
    }
  };

  /**
   * 处理回车键
   * @param {KeyboardEvent} event - 键盘事件
   */
  const handleEnterKey = (event) => {
    // 标题中不允许换行
    event.preventDefault();
    return false;
  };

  /**
   * 获取区块选择目标
   * @returns {HTMLElement} 选择目标元素
   */
  const getSectionSelectionTarget = () => {
    const section = baseEditor.sectionContainer.querySelector('.userpilot-builder-block:last-child');
    
    if (!section) {
      // 如果没有找到构建块，创建一个
      const newBlock = baseEditor.iframeDocument.createElement('div');
      newBlock.classList.add('userpilot-builder-block', 'userpilot-h2');
      newBlock.textContent = '\u200B';
      baseEditor.sectionContainer.appendChild(newBlock);
      return newBlock.lastChild || newBlock;
    }

    if (!section.textContent) {
      section.textContent = '\u200B';
    }

    return section.lastChild || section;
  };

  /**
   * 设置标题级别
   * @param {number} level - 标题级别 (1-6)
   */
  const setHeaderLevel = (level) => {
    if (level < 1 || level > 6) {
      console.error('Header level must be between 1 and 6');
      return;
    }

    const blocks = baseEditor.sectionContainer.querySelectorAll('.userpilot-builder-block');
    blocks.forEach(block => {
      // 移除现有的标题类
      block.classList.remove('userpilot-h1', 'userpilot-h2', 'userpilot-h3', 
                            'userpilot-h4', 'userpilot-h5', 'userpilot-h6');
      
      // 添加新的标题类
      block.classList.add(`userpilot-h${level}`);
    });
  };

  /**
   * 获取当前标题级别
   * @returns {number} 标题级别
   */
  const getHeaderLevel = () => {
    const block = baseEditor.sectionContainer.querySelector('.userpilot-builder-block');
    if (!block) return 2; // 默认为h2

    for (let i = 1; i <= 6; i++) {
      if (block.classList.contains(`userpilot-h${i}`)) {
        return i;
      }
    }

    return 2; // 默认为h2
  };

  /**
   * 设置标题文本
   * @param {string} text - 标题文本
   */
  const setHeaderText = (text) => {
    const block = baseEditor.sectionContainer.querySelector('.userpilot-builder-block');
    if (block) {
      block.textContent = text || '\u200B';
    }
  };

  /**
   * 获取标题文本
   * @returns {string} 标题文本
   */
  const getHeaderText = () => {
    const block = baseEditor.sectionContainer.querySelector('.userpilot-builder-block');
    return block ? block.textContent.replace('\u200B', '') : '';
  };

  /**
   * 验证更改
   * @returns {boolean} 验证是否通过
   */
  const validateChanges = () => {
    const text = getHeaderText().trim();
    return text.length > 0;
  };

  /**
   * 应用标题样式
   * @param {Object} styles - 样式对象
   */
  const applyHeaderStyles = (styles) => {
    const blocks = baseEditor.sectionContainer.querySelectorAll('.userpilot-builder-block');
    
    blocks.forEach(block => {
      Object.entries(styles).forEach(([property, value]) => {
        block.style[property] = value;
      });
    });
  };

  /**
   * 获取标题样式
   * @returns {Object} 样式对象
   */
  const getHeaderStyles = () => {
    const block = baseEditor.sectionContainer.querySelector('.userpilot-builder-block');
    if (!block) return {};

    const computedStyle = baseEditor.iframeWindow.getComputedStyle(block);
    
    return {
      fontSize: computedStyle.fontSize,
      fontFamily: computedStyle.fontFamily,
      fontWeight: computedStyle.fontWeight,
      fontStyle: computedStyle.fontStyle,
      color: computedStyle.color,
      textAlign: computedStyle.textAlign,
      textDecoration: computedStyle.textDecoration,
      textTransform: computedStyle.textTransform,
      lineHeight: computedStyle.lineHeight,
      letterSpacing: computedStyle.letterSpacing,
      marginTop: computedStyle.marginTop,
      marginBottom: computedStyle.marginBottom
    };
  };

  // 标题编辑器对象
  const headerEditor = {
    ...baseEditor,
    init,
    save,
    destroy,
    validateChanges,
    getSectionSelectionTarget,
    
    // 标题编辑器特有方法
    setHeaderLevel,
    getHeaderLevel,
    setHeaderText,
    getHeaderText,
    applyHeaderStyles,
    getHeaderStyles,
    
    // 事件处理器
    handleKeydown,
    handleEnterKey,
    
    // 访问器
    get undoRedoManager() { return undoRedoManager; },
    get wysiwygOperations() { return wysiwygOperations; }
  };

  return headerEditor;
};

/**
 * 运行个性化节点检查
 * @param {HTMLElement} container - 容器元素
 */
const runPersonalizationNodeChecks = (container) => {
  // 这里应该实现个性化节点检查逻辑
  // 暂时为空实现
  console.log('Running personalization node checks for:', container);
};
