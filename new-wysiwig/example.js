/**
 * 函数式 WYSIWYG 编辑器使用示例
 * 展示如何使用新的函数式编辑器
 */

import { createWysiwygBuilder } from './wysiwygBuilder.js';
import { createSectionEditor, registerEditorType } from './editors/sectionEditorFactory.js';
import { sectionDebugTools } from './utils/sectionUtils.js';
import { eventManagerDebugTools } from './events/eventManager.js';

/**
 * 基本使用示例
 */
export const basicUsageExample = () => {
  // 假设我们有一个模板编辑器实例
  const templateEditor = {
    iframeDocument: document.getElementById('editor-iframe').contentDocument,
    iframeWindow: document.getElementById('editor-iframe').contentWindow,
    iframeContainer: document.querySelector('.userpilot-container'),
    userpilotContent: document.querySelector('.userpilot-content'),
    userpilotSlide: document.querySelector('.userpilot-slide'),
    sections: document.querySelectorAll('.userpilot-section'),
    stepType: 'MODAL',
    templateSettings: {
      type: 'MODAL',
      reInitBannerPlaceholder: () => console.log('Reinit banner placeholder')
    },
    stepSettings: {
      action_id: 'step_123'
    },
    stepLocalizedContent: {
      editedSections: []
    },
    setupNewSectionEvents: (section) => console.log('Setup events for:', section),
    saveStepContent: (data) => console.log('Save content:', data),
    updateHideOnMobileAttribute: (id) => console.log('Update mobile attr:', id)
  };

  // 创建编辑器构建器
  const builder = createWysiwygBuilder(templateEditor);

  // 初始化编辑器
  builder.init();

  console.log('✅ 函数式 WYSIWYG 编辑器已初始化');
  
  return builder;
};

/**
 * 自定义编辑器示例
 */
export const customEditorExample = () => {
  // 创建一个自定义的视频编辑器
  const createVideoEditor = (section, toolbar, state) => {
    const baseEditor = createBaseEditor(section, toolbar, state);
    
    let videoElement = null;
    let videoSrc = '';
    let videoControls = true;

    const init = () => {
      if (baseEditor.isInitialized) return videoEditor;
      
      baseEditor.init();
      videoElement = baseEditor.sectionContainer.querySelector('video');
      
      if (videoElement) {
        videoSrc = videoElement.src;
        videoControls = videoElement.controls;
      }
      
      return videoEditor;
    };

    const save = (action = 'apply') => {
      baseEditor.save(action);
      return videoEditor;
    };

    const setVideoSrc = (src) => {
      videoSrc = src;
      if (videoElement) {
        videoElement.src = src;
      }
    };

    const getVideoSrc = () => {
      return videoSrc;
    };

    const setControls = (controls) => {
      videoControls = controls;
      if (videoElement) {
        videoElement.controls = controls;
      }
    };

    const getControls = () => {
      return videoControls;
    };

    const validateChanges = () => {
      return videoSrc && videoSrc.trim().length > 0;
    };

    const videoEditor = {
      ...baseEditor,
      init,
      save,
      validateChanges,
      setVideoSrc,
      getVideoSrc,
      setControls,
      getControls,
      get videoElement() { return videoElement; },
      get videoSrc() { return videoSrc; },
      get videoControls() { return videoControls; }
    };

    return videoEditor;
  };

  // 注册自定义编辑器类型
  registerEditorType('userpilot-video-section', createVideoEditor);

  console.log('✅ 自定义视频编辑器已注册');
};

/**
 * 事件处理示例
 */
export const eventHandlingExample = (builder) => {
  const { eventManager } = builder;

  // 注册自定义事件处理器
  const saveHandlerId = eventManager.registerCustomEventHandler('section-saved', (detail) => {
    console.log('区块已保存:', detail.section);
    
    // 可以在这里添加自定义逻辑，比如：
    // - 发送分析数据
    // - 更新UI状态
    // - 触发其他操作
  });

  const discardHandlerId = eventManager.registerCustomEventHandler('section-discarded', (detail) => {
    console.log('区块更改已撤销:', detail.section);
  });

  const cloneHandlerId = eventManager.registerCustomEventHandler('section-clone', (detail) => {
    console.log('区块已克隆:', detail.section);
  });

  // 监听编辑器状态变化
  const stateChangeHandlerId = eventManager.registerCustomEventHandler('editor-state-changed', (detail) => {
    console.log('编辑器状态变化:', detail);
  });

  // 返回清理函数
  return () => {
    eventManager.unregisterCustomEventHandler('section-saved', saveHandlerId);
    eventManager.unregisterCustomEventHandler('section-discarded', discardHandlerId);
    eventManager.unregisterCustomEventHandler('section-clone', cloneHandlerId);
    eventManager.unregisterCustomEventHandler('editor-state-changed', stateChangeHandlerId);
  };
};

/**
 * 调试工具示例
 */
export const debugToolsExample = (builder) => {
  const { state, eventManager } = builder;

  // 启用调试模式
  state.config.debug = true;

  // 监控事件性能
  eventManagerDebugTools.monitorEventPerformance(eventManager);

  // 追踪特定事件
  eventManagerDebugTools.traceEvent(eventManager, 'section-saved');
  eventManagerDebugTools.traceEvent(eventManager, 'section-clone');

  // 记录所有事件监听器
  eventManagerDebugTools.logAllListeners(eventManager);

  // 调试区块信息
  state.sections.forEach(section => {
    if (section.classList.contains('userpilot-text-section')) {
      sectionDebugTools.logSectionInfo(section);
    }
  });

  // 列出容器中的所有区块
  sectionDebugTools.listSectionsInContainer(state.userpilotContent);

  console.log('✅ 调试工具已启用');
};

/**
 * 高级功能示例
 */
export const advancedFeaturesExample = (builder) => {
  const { state, toolbar, observer } = builder;

  // 自定义工具栏行为
  const originalShow = toolbar.show;
  toolbar.show = function(section, editor) {
    console.log('显示工具栏 for:', section);
    
    // 添加自定义CSS类
    const sectionType = getSectionType(section);
    this.addClass(`toolbar-${sectionType}`);
    
    return originalShow.call(this, section, editor);
  };

  // 监控DOM变化
  observer.ignoreChanges(async () => {
    // 在这个回调中的DOM变化将被忽略
    console.log('执行不被观察的DOM操作...');
    
    // 模拟一些DOM操作
    await new Promise(resolve => setTimeout(resolve, 100));
  });

  // 自定义状态验证
  const originalValidate = state.validators?.isValidState || (() => true);
  if (state.validators) {
    state.validators.isValidState = (stateToValidate) => {
      const baseValid = originalValidate(stateToValidate);
      
      // 添加自定义验证逻辑
      const customValid = stateToValidate.sections?.length > 0;
      
      return baseValid && customValid;
    };
  }

  console.log('✅ 高级功能已配置');
};

/**
 * 性能优化示例
 */
export const performanceOptimizationExample = (builder) => {
  const { state, eventManager } = builder;

  // 批量操作示例
  const batchUpdateSections = (updates) => {
    // 暂停观察器以避免频繁触发
    builder.observer.pause();

    try {
      updates.forEach(({ section, changes }) => {
        Object.assign(section.style, changes);
      });
    } finally {
      // 恢复观察器
      builder.observer.resume();
    }
  };

  // 防抖的保存函数
  const debouncedSave = debounce(() => {
    builder.applyChanges();
  }, 1000);

  // 节流的重新定位函数
  const throttledReposition = throttle(() => {
    builder.toolbar.reposition();
  }, 100);

  // 使用示例
  const sections = Array.from(state.sections);
  const updates = sections.map(section => ({
    section,
    changes: { opacity: '0.8' }
  }));

  batchUpdateSections(updates);

  console.log('✅ 性能优化已应用');

  return {
    debouncedSave,
    throttledReposition,
    batchUpdateSections
  };
};

/**
 * 完整的使用示例
 */
export const fullExample = () => {
  console.log('🚀 开始函数式 WYSIWYG 编辑器完整示例...');

  try {
    // 1. 基本初始化
    const builder = basicUsageExample();

    // 2. 注册自定义编辑器
    customEditorExample();

    // 3. 设置事件处理
    const cleanupEvents = eventHandlingExample(builder);

    // 4. 启用调试工具
    debugToolsExample(builder);

    // 5. 配置高级功能
    advancedFeaturesExample(builder);

    // 6. 应用性能优化
    const performanceUtils = performanceOptimizationExample(builder);

    console.log('✅ 所有示例已成功运行');

    // 返回清理函数
    return () => {
      cleanupEvents();
      builder.disconnect();
      console.log('🧹 编辑器已清理');
    };

  } catch (error) {
    console.error('❌ 示例运行失败:', error);
    throw error;
  }
};

// 工具函数
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

const throttle = (func, limit) => {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// 如果直接运行此文件，执行完整示例
if (typeof window !== 'undefined' && window.location) {
  // 浏览器环境
  document.addEventListener('DOMContentLoaded', () => {
    const cleanup = fullExample();
    
    // 在页面卸载时清理
    window.addEventListener('beforeunload', cleanup);
  });
} else if (typeof module !== 'undefined' && module.exports) {
  // Node.js 环境
  module.exports = {
    basicUsageExample,
    customEditorExample,
    eventHandlingExample,
    debugToolsExample,
    advancedFeaturesExample,
    performanceOptimizationExample,
    fullExample
  };
}
