# 函数式 WYSIWYG 编辑器

这是一个使用现代 JavaScript 函数式编程范式重新实现的 WYSIWYG（所见即所得）编辑器，专为 Userpilot 平台设计。

## 🎯 设计理念

### 函数式编程优势
- **纯函数**: 所有核心函数都是纯函数，输入相同则输出相同
- **不可变性**: 状态管理采用不可变数据结构
- **组合性**: 小函数组合成复杂功能
- **可测试性**: 函数式设计使单元测试更容易
- **可维护性**: 代码结构清晰，易于理解和维护

### 与原版对比
| 特性 | 原版（面向对象） | 新版（函数式） |
|------|------------------|----------------|
| 代码结构 | 类继承体系 | 函数组合 |
| 状态管理 | 实例属性 | 不可变状态 |
| 事件处理 | 方法绑定 | 高阶函数 |
| 扩展性 | 类继承 | 函数组合 |
| 测试难度 | 需要模拟实例 | 直接测试函数 |

## 📁 项目结构

```
new-wysiwig/
├── wysiwygBuilder.js          # 主编辑器构建器
├── state/                     # 状态管理
│   └── editorState.js        # 编辑器状态管理
├── editors/                   # 编辑器实现
│   ├── sectionEditorFactory.js  # 编辑器工厂
│   ├── textEditor.js         # 文本编辑器
│   ├── buttonEditor.js       # 按钮编辑器
│   ├── headerEditor.js       # 标题编辑器
│   ├── imageEditor.js        # 图片编辑器
│   ├── htmlEditor.js         # HTML编辑器
│   ├── emojiEditor.js        # 表情编辑器
│   ├── inputEditor.js        # 输入编辑器
│   ├── embedEditor.js        # 嵌入编辑器
│   └── nodeButtonEditor.js   # 节点按钮编辑器
├── toolbar/                   # 工具栏管理
│   ├── toolbarManager.js     # 工具栏管理器
│   ├── toolbarViews.js       # 工具栏视图
│   └── toolbarEvents.js      # 工具栏事件
├── utils/                     # 工具函数
│   ├── sectionUtils.js       # 区块工具函数
│   ├── observer.js           # DOM观察器
│   ├── wysiwygOperations.js  # WYSIWYG操作
│   └── undoRedoManager.js    # 撤销重做管理
├── events/                    # 事件管理
│   └── eventManager.js       # 事件管理器
└── README.md                  # 项目文档
```

## 🚀 快速开始

### 基本使用

```javascript
import { createWysiwygBuilder } from './new-wysiwig/wysiwygBuilder.js';

// 创建编辑器实例
const builder = createWysiwygBuilder(templateEditor);

// 初始化编辑器
builder.init();

// 为新区块附加事件
builder.attachSectionEvents(newSection);
```

### 创建自定义编辑器

```javascript
import { createBaseEditor, registerEditorType } from './editors/sectionEditorFactory.js';

// 创建自定义编辑器
const createCustomEditor = (section, toolbar, state) => {
  const baseEditor = createBaseEditor(section, toolbar, state);
  
  const init = () => {
    baseEditor.init();
    // 自定义初始化逻辑
    return customEditor;
  };
  
  const customEditor = {
    ...baseEditor,
    init,
    // 其他自定义方法
  };
  
  return customEditor;
};

// 注册自定义编辑器类型
registerEditorType('custom-section-type', createCustomEditor);
```

## 🔧 核心功能

### 1. 状态管理

```javascript
import { createEditorState, updateEditorState } from './state/editorState.js';

// 创建状态
const state = createEditorState(config);

// 更新状态
updateEditorState(state, { editorActive: true });

// 获取只读状态
const readonlyState = getEditorState(state);
```

### 2. 编辑器工厂

```javascript
import { createSectionEditor } from './editors/sectionEditorFactory.js';

// 自动创建对应类型的编辑器
const editor = createSectionEditor(section, toolbar, state);
```

### 3. 工具栏管理

```javascript
import { createToolbar } from './toolbar/toolbarManager.js';

const toolbar = createToolbar(state);
toolbar.show(section, editor);
toolbar.hide();
```

### 4. 事件管理

```javascript
import { createEventManager } from './events/eventManager.js';

const eventManager = createEventManager(state, toolbar);

// 注册自定义事件
eventManager.registerCustomEventHandler('custom-event', handler);

// 触发事件
eventManager.triggerCustomEvent('custom-event', { data: 'value' });
```

### 5. WYSIWYG 操作

```javascript
import { createWysiwygOperations } from './utils/wysiwygOperations.js';

const operations = createWysiwygOperations(editor);

// 插入内容
operations.insertHTML('<strong>Bold text</strong>');
operations.insertText('Plain text');

// 选择操作
operations.selectNodeContents(element);
operations.wrapSelection('span', { class: 'highlight' });
```

### 6. 撤销重做

```javascript
import { createUndoRedoManager } from './utils/undoRedoManager.js';

const undoRedoManager = createUndoRedoManager(editor, 'text');

// 手动保存状态
undoRedoManager.manualSave();

// 执行撤销/重做
undoRedoManager.performUndo();
undoRedoManager.performRedo();
```

## 🎨 支持的编辑器类型

- **文本编辑器**: 富文本编辑，支持格式化、链接等
- **标题编辑器**: 多级标题编辑
- **按钮编辑器**: 按钮样式和动作配置
- **图片编辑器**: 图片上传、Unsplash集成
- **HTML编辑器**: 原始HTML代码编辑
- **表情编辑器**: 表情选择和大小调整
- **输入编辑器**: 表单输入字段配置
- **嵌入编辑器**: 第三方内容嵌入
- **节点按钮编辑器**: 特殊的节点交互按钮

## 🛠️ 工具函数

### 区块工具

```javascript
import { getSectionType, isValidSection, cloneSection } from './utils/sectionUtils.js';

// 获取区块类型
const type = getSectionType(section);

// 验证区块
const isValid = isValidSection(section);

// 克隆区块
const cloned = cloneSection(section, true);
```

### 观察器

```javascript
import { createObserver } from './utils/observer.js';

const observer = createObserver(state);
observer.observe(element);
observer.disconnect();
```

## 🧪 调试工具

每个模块都提供了调试工具：

```javascript
import { sectionDebugTools } from './utils/sectionUtils.js';
import { eventManagerDebugTools } from './events/eventManager.js';

// 调试区块信息
sectionDebugTools.logSectionInfo(section);

// 监控事件性能
eventManagerDebugTools.monitorEventPerformance(eventManager);
```

## 🔄 迁移指南

### 从原版迁移

1. **替换导入**:
   ```javascript
   // 原版
   import WysiwigBuilder from './wysiwyg-builder/wysiwigBuilder.js';
   
   // 新版
   import { createWysiwygBuilder } from './new-wysiwig/wysiwygBuilder.js';
   ```

2. **更新实例化**:
   ```javascript
   // 原版
   const builder = new WysiwigBuilder(templateEditor);
   
   // 新版
   const builder = createWysiwygBuilder(templateEditor);
   ```

3. **方法调用保持一致**:
   ```javascript
   // 两个版本都支持
   builder.init();
   builder.applyChanges();
   builder.disconnect();
   ```

## 📈 性能优化

- **懒加载**: 编辑器按需创建
- **事件防抖**: 避免频繁的DOM操作
- **内存管理**: 自动清理事件监听器
- **观察器优化**: 智能过滤不重要的变化

## 🧩 扩展性

### 添加新的编辑器类型

1. 创建编辑器函数
2. 注册到工厂
3. 添加对应的工具栏视图
4. 实现特定的事件处理

### 自定义工具栏

1. 扩展工具栏视图
2. 添加事件处理器
3. 实现自定义操作

## 🤝 贡献指南

1. 遵循函数式编程原则
2. 保持函数纯净性
3. 添加适当的类型注释
4. 编写单元测试
5. 更新文档

## 📄 许可证

本项目采用与原项目相同的许可证。
