/**
 * 事件管理器
 * 函数式实现的事件管理系统
 */

/**
 * 创建事件管理器
 * @param {Object} state - 编辑器状态
 * @param {Object} toolbar - 工具栏对象
 * @returns {Object} 事件管理器对象
 */
export const createEventManager = (state, toolbar) => {
  
  // 事件监听器存储
  const eventListeners = new Map();
  const customEventHandlers = new Map();

  /**
   * 添加事件监听器
   * @param {HTMLElement} element - 目标元素
   * @param {string} eventType - 事件类型
   * @param {Function} handler - 事件处理器
   * @param {Object} options - 事件选项
   * @returns {string} 监听器ID
   */
  const addEventListener = (element, eventType, handler, options = {}) => {
    if (!element || !eventType || typeof handler !== 'function') {
      console.error('Invalid parameters for addEventListener');
      return null;
    }

    const listenerId = generateListenerId();
    const wrappedHandler = createWrappedHandler(handler, listenerId);

    element.addEventListener(eventType, wrappedHandler, options);

    eventListeners.set(listenerId, {
      element,
      eventType,
      handler: wrappedHandler,
      originalHandler: handler,
      options
    });

    return listenerId;
  };

  /**
   * 移除事件监听器
   * @param {string} listenerId - 监听器ID
   */
  const removeEventListener = (listenerId) => {
    const listener = eventListeners.get(listenerId);
    if (!listener) return;

    listener.element.removeEventListener(
      listener.eventType,
      listener.handler,
      listener.options
    );

    eventListeners.delete(listenerId);
  };

  /**
   * 移除元素的所有事件监听器
   * @param {HTMLElement} element - 目标元素
   */
  const removeAllListenersForElement = (element) => {
    const listenersToRemove = [];
    
    eventListeners.forEach((listener, id) => {
      if (listener.element === element) {
        listenersToRemove.push(id);
      }
    });

    listenersToRemove.forEach(id => removeEventListener(id));
  };

  /**
   * 创建包装的事件处理器
   * @param {Function} handler - 原始处理器
   * @param {string} listenerId - 监听器ID
   * @returns {Function} 包装后的处理器
   */
  const createWrappedHandler = (handler, listenerId) => {
    return function wrappedHandler(event) {
      try {
        // 添加调试信息
        if (state.config?.debug) {
          console.log(`Event triggered: ${event.type}`, { listenerId, event });
        }

        // 调用原始处理器
        return handler.call(this, event);
      } catch (error) {
        console.error('Error in event handler:', error, { listenerId, event });
      }
    };
  };

  /**
   * 生成监听器ID
   * @returns {string} 唯一ID
   */
  const generateListenerId = () => {
    return `listener_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  /**
   * 注册自定义事件处理器
   * @param {string} eventName - 事件名称
   * @param {Function} handler - 事件处理器
   * @returns {string} 处理器ID
   */
  const registerCustomEventHandler = (eventName, handler) => {
    if (!customEventHandlers.has(eventName)) {
      customEventHandlers.set(eventName, new Map());
    }

    const handlerId = generateListenerId();
    customEventHandlers.get(eventName).set(handlerId, handler);

    return handlerId;
  };

  /**
   * 注销自定义事件处理器
   * @param {string} eventName - 事件名称
   * @param {string} handlerId - 处理器ID
   */
  const unregisterCustomEventHandler = (eventName, handlerId) => {
    const handlers = customEventHandlers.get(eventName);
    if (handlers) {
      handlers.delete(handlerId);
      if (handlers.size === 0) {
        customEventHandlers.delete(eventName);
      }
    }
  };

  /**
   * 触发自定义事件
   * @param {string} eventName - 事件名称
   * @param {Object} detail - 事件详情
   * @param {HTMLElement} target - 目标元素
   */
  const triggerCustomEvent = (eventName, detail = {}, target = state.userpilotContainer) => {
    // 触发DOM事件
    const domEvent = new CustomEvent(eventName, {
      detail,
      bubbles: true,
      cancelable: true
    });

    if (target) {
      target.dispatchEvent(domEvent);
    }

    // 触发自定义处理器
    const handlers = customEventHandlers.get(eventName);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(detail, domEvent);
        } catch (error) {
          console.error(`Error in custom event handler for ${eventName}:`, error);
        }
      });
    }
  };

  /**
   * 隐藏窗口组件
   */
  const hideWindowComponents = () => {
    triggerCustomEvent('hide-window-components');
    
    // 如果有Store，调用相应的action
    if (state.templateEditor?.store) {
      // Store.publishAction(Actions.HIDE_WINDOW_COMPONENTS);
      // Store.publishAction(Actions.LOCK_BAR, { hide: true });
    }
  };

  /**
   * 显示窗口组件
   */
  const showWindowComponents = () => {
    triggerCustomEvent('show-window-components');
    
    // 如果有Store，调用相应的action
    if (state.templateEditor?.store) {
      // Store.publishAction(Actions.UNLOCK_BAR, { hide: true });
      // Store.publishAction(Actions.SHOW_WINDOW_COMPONENTS);
    }
  };

  /**
   * 显示警告信息
   * @param {string} severity - 严重程度 ('error', 'warning', 'info', 'success')
   * @param {string} content - 警告内容
   */
  const showAlert = (severity, content) => {
    triggerCustomEvent('show-alert', { severity, content });
    
    // 如果有Alert组件，直接调用
    if (window.Alert) {
      window.Alert({ severity, content });
    } else {
      // 降级到console
      console[severity === 'error' ? 'error' : 'log'](`${severity.toUpperCase()}: ${content}`);
    }
  };

  /**
   * 触发区块变化事件
   */
  const triggerSectionChange = () => {
    triggerCustomEvent('step-sections-change');
    
    // 触发特定的DOM事件
    const stepSectionsSidebarNode = document.querySelector('#userpilot-step-sections-sidebar');
    if (stepSectionsSidebarNode) {
      const event = new CustomEvent('stepSectionsChange');
      stepSectionsSidebarNode.dispatchEvent(event);
    }
  };

  /**
   * 重新调整列区块
   * @param {HTMLElement} container - 容器元素
   */
  const readjustColumnSections = (container) => {
    if (!container) return;

    // 查找所有列区块容器
    const columnContainers = container.querySelectorAll('.userpilot-cols-section');
    
    columnContainers.forEach(colContainer => {
      const sections = Array.from(colContainer.children);
      
      // 如果只有一个区块，可能需要移出列布局
      if (sections.length === 1) {
        const section = sections[0];
        const parent = colContainer.parentNode;
        
        // 将区块移到列容器外
        parent.insertBefore(section, colContainer);
        
        // 移除空的列容器
        colContainer.remove();
      }
      
      // 如果没有区块，直接移除容器
      if (sections.length === 0) {
        colContainer.remove();
      }
    });
  };

  /**
   * 处理拖拽开始
   * @param {DragEvent} event - 拖拽事件
   */
  const handleDragStart = (event) => {
    const section = event.target.closest('[unit_id]');
    if (!section) return;

    event.dataTransfer.setData('text/plain', section.getAttribute('unit_id'));
    event.dataTransfer.effectAllowed = 'move';
    
    triggerCustomEvent('section-drag-start', { section });
  };

  /**
   * 处理拖拽结束
   * @param {DragEvent} event - 拖拽事件
   */
  const handleDragEnd = (event) => {
    triggerCustomEvent('section-drag-end', { event });
  };

  /**
   * 处理放置
   * @param {DragEvent} event - 拖拽事件
   */
  const handleDrop = (event) => {
    event.preventDefault();
    
    const sectionId = event.dataTransfer.getData('text/plain');
    const dropTarget = event.target.closest('[unit_id]');
    
    if (sectionId && dropTarget) {
      triggerCustomEvent('section-drop', { 
        sectionId, 
        dropTarget,
        position: getDropPosition(event, dropTarget)
      });
    }
  };

  /**
   * 获取放置位置
   * @param {DragEvent} event - 拖拽事件
   * @param {HTMLElement} target - 目标元素
   * @returns {string} 位置 ('before', 'after', 'inside')
   */
  const getDropPosition = (event, target) => {
    const rect = target.getBoundingClientRect();
    const y = event.clientY - rect.top;
    const height = rect.height;

    if (y < height * 0.25) return 'before';
    if (y > height * 0.75) return 'after';
    return 'inside';
  };

  /**
   * 初始化全局事件监听器
   */
  const initGlobalListeners = () => {
    // 键盘快捷键
    addEventListener(state.iframeDocument, 'keydown', handleGlobalKeydown, true);
    
    // 拖拽事件
    addEventListener(state.userpilotContainer, 'dragstart', handleDragStart);
    addEventListener(state.userpilotContainer, 'dragend', handleDragEnd);
    addEventListener(state.userpilotContainer, 'drop', handleDrop);
    addEventListener(state.userpilotContainer, 'dragover', (e) => e.preventDefault());

    // 窗口大小变化
    addEventListener(state.iframeWindow, 'resize', () => {
      toolbar.reposition?.();
    });

    // 滚动事件
    addEventListener(state.userpilotContainer, 'scroll', () => {
      toolbar.reposition?.();
    });
  };

  /**
   * 处理全局键盘事件
   * @param {KeyboardEvent} event - 键盘事件
   */
  const handleGlobalKeydown = (event) => {
    const { ctrlKey, metaKey, key } = event;
    
    // Escape键 - 退出编辑模式
    if (key === 'Escape' && state.editorActive) {
      event.preventDefault();
      triggerCustomEvent('editor-escape');
      return;
    }

    // Ctrl/Cmd + S - 保存
    if ((ctrlKey || metaKey) && key === 's') {
      event.preventDefault();
      triggerCustomEvent('editor-save');
      return;
    }

    // Ctrl/Cmd + Enter - 应用更改
    if ((ctrlKey || metaKey) && key === 'Enter' && state.editorActive) {
      event.preventDefault();
      triggerCustomEvent('editor-apply');
      return;
    }
  };

  /**
   * 清理所有事件监听器
   */
  const cleanup = () => {
    // 移除所有DOM事件监听器
    eventListeners.forEach((listener, id) => {
      removeEventListener(id);
    });

    // 清空自定义事件处理器
    customEventHandlers.clear();
  };

  /**
   * 获取事件统计信息
   * @returns {Object} 统计信息
   */
  const getEventStats = () => {
    const domListenerCount = eventListeners.size;
    let customHandlerCount = 0;
    
    customEventHandlers.forEach(handlers => {
      customHandlerCount += handlers.size;
    });

    return {
      domListeners: domListenerCount,
      customHandlers: customHandlerCount,
      customEventTypes: customEventHandlers.size,
      totalEvents: domListenerCount + customHandlerCount
    };
  };

  // 初始化全局监听器
  initGlobalListeners();

  // 事件管理器对象
  const eventManager = {
    // DOM事件管理
    addEventListener,
    removeEventListener,
    removeAllListenersForElement,
    
    // 自定义事件管理
    registerCustomEventHandler,
    unregisterCustomEventHandler,
    triggerCustomEvent,
    
    // 应用特定事件
    hideWindowComponents,
    showWindowComponents,
    showAlert,
    triggerSectionChange,
    readjustColumnSections,
    
    // 拖拽事件
    handleDragStart,
    handleDragEnd,
    handleDrop,
    
    // 工具方法
    cleanup,
    getEventStats,
    
    // 访问器
    get listenerCount() { return eventListeners.size; },
    get customEventCount() { return customEventHandlers.size; }
  };

  return eventManager;
};

/**
 * 事件管理器调试工具
 */
export const eventManagerDebugTools = {
  /**
   * 记录所有事件监听器
   * @param {Object} eventManager - 事件管理器实例
   */
  logAllListeners: (eventManager) => {
    const stats = eventManager.getEventStats();
    console.group('Event Manager Debug');
    console.log('Statistics:', stats);
    console.log('DOM Listeners:', eventManager.listenerCount);
    console.log('Custom Events:', eventManager.customEventCount);
    console.groupEnd();
  },

  /**
   * 监控事件性能
   * @param {Object} eventManager - 事件管理器实例
   */
  monitorEventPerformance: (eventManager) => {
    const startTime = performance.now();
    let eventCount = 0;

    // 代理triggerCustomEvent方法
    const originalTrigger = eventManager.triggerCustomEvent;
    eventManager.triggerCustomEvent = function(eventName, detail, target) {
      eventCount++;
      const eventStart = performance.now();
      
      const result = originalTrigger.call(this, eventName, detail, target);
      
      const eventEnd = performance.now();
      console.log(`Custom event "${eventName}" took ${(eventEnd - eventStart).toFixed(2)}ms`);
      
      return result;
    };

    // 定期报告
    setInterval(() => {
      const elapsed = performance.now() - startTime;
      console.log(`Event Performance: ${eventCount} events in ${elapsed.toFixed(2)}ms`);
    }, 10000);
  },

  /**
   * 追踪特定事件
   * @param {Object} eventManager - 事件管理器实例
   * @param {string} eventName - 要追踪的事件名称
   */
  traceEvent: (eventManager, eventName) => {
    eventManager.registerCustomEventHandler(eventName, (detail, domEvent) => {
      console.group(`Event Trace: ${eventName}`);
      console.log('Detail:', detail);
      console.log('DOM Event:', domEvent);
      console.log('Timestamp:', new Date().toISOString());
      console.trace('Call stack');
      console.groupEnd();
    });
  }
};
