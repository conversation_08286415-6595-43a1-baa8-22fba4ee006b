/**
 * 函数式 WYSIWYG 编辑器构建器
 * 使用现代 JavaScript 函数式编程范式重新实现
 */

import { createEditorState, updateEditorState } from './state/editorState.js';
import { createSectionEditor } from './editors/sectionEditorFactory.js';
import { createToolbar } from './toolbar/toolbarManager.js';
import { createEventManager } from './events/eventManager.js';
import { createObserver } from './utils/observer.js';
import { getSectionType } from './utils/sectionUtils.js';

// 编辑器类型常量
export const EDITOR_TYPES = {
  HEADER: 'userpilot-header-section',
  TEXT: 'userpilot-text-section',
  BUTTON: 'userpilot-button-section',
  IMAGE: 'userpilot-image-section',
  HTML: 'userpilot-html-section',
  EMOJI: 'userpilot-emoji-section',
  INPUT_TEXT: 'userpilot-input-text',
  INPUT_LARGE: 'userpilot-input-text-large',
  INPUT_RADIO: 'userpilot-input-radio',
  LIKERT_SCALE: 'userpilot-input-likert-scale',
  EMBED: 'userpilot-embed-section',
  NODE_BUTTON: 'userpilot-node-button'
};

/**
 * 创建 WYSIWYG 编辑器构建器
 * @param {Object} templateEditor - 模板编辑器实例
 * @returns {Object} 编辑器构建器对象
 */
export const createWysiwygBuilder = (templateEditor) => {
  // 初始化编辑器状态
  const state = createEditorState({
    templateEditor,
    iframeDocument: templateEditor.iframeDocument,
    iframeWindow: templateEditor.iframeWindow,
    userpilotContainer: templateEditor.iframeContainer,
    userpilotContent: templateEditor.userpilotContent,
    userpilotSlide: templateEditor.userpilotSlide,
    sections: templateEditor.sections
  });

  // 创建工具栏
  const toolbar = createToolbar(state);
  
  // 创建事件管理器
  const eventManager = createEventManager(state, toolbar);
  
  // 创建观察者
  const observer = createObserver(state);

  // 编辑器实例映射
  const sectionEditors = new Map();

  /**
   * 初始化编辑器
   */
  const init = () => {
    // 为每个区块附加事件
    state.sections.forEach(section => {
      attachSectionEvents(section);
    });

    // 初始化选择监听器
    initSelectionListener();
    
    // 附加窗口监听器
    attachWindowListeners();

    return builder;
  };

  /**
   * 为区块附加事件
   * @param {HTMLElement} section - 区块元素
   */
  const attachSectionEvents = (section) => {
    if (!sectionEditors.has(section)) {
      const editor = createSectionEditor(section, toolbar, state);
      sectionEditors.set(section, editor);
      section.addEventListener('click', handleSectionClick);
    }
  };

  /**
   * 处理区块点击事件
   * @param {Event} event - 点击事件
   */
  const handleSectionClick = (event) => {
    if (state.editorActive) return;

    const section = event.currentTarget;
    const editor = sectionEditors.get(section);
    
    if (!editor) {
      console.error('DevError: Section must have an editor associated with it');
      return;
    }

    spawnEditor(section, editor);
  };

  /**
   * 启动编辑器
   * @param {HTMLElement} section - 当前编辑的区块
   * @param {Object} editor - 区块编辑器
   */
  const spawnEditor = (section, editor) => {
    // 更新状态
    updateEditorState(state, {
      editorActive: true,
      currentSection: section,
      activeBuilder: editor,
      oldHtml: section.cloneNode(true)
    });

    // 隐藏窗口组件
    eventManager.hideWindowComponents();
    
    // 添加编辑状态样式
    state.userpilotContainer.classList.add('editor-active');
    section.classList.add('under-edit');
    state.userpilotContent.classList.add('under-edit');

    // 显示工具栏
    toolbar.show(section, editor);
    
    // 初始化编辑器
    editor.init();
    
    // 开始观察变化
    observer.observe(section);
  };

  /**
   * 初始化选择监听器
   */
  const initSelectionListener = () => {
    state.userpilotContainer.addEventListener('mouseup', (event) => {
      if (!state.activeBuilder) return;
      if (!state.activeBuilder.wysiwygOperations) return;

      const isOutsideEditor = !state.activeBuilder.sectionContainer.contains(event.target) && 
                             !toolbar.element.contains(event.target);
      
      if (isOutsideEditor) {
        state.activeBuilder.wysiwygOperations.selection.collapse(false);
        state.activeBuilder.wysiwygOperations.resetSectionRange();
      }
    });
  };

  /**
   * 附加窗口监听器
   */
  const attachWindowListeners = () => {
    state.userpilotContainer.addEventListener('mousewheel', () => {
      toolbar.reposition();
    });
  };

  /**
   * 应用更改
   */
  const applyChanges = () => {
    const { activeBuilder } = state;
    
    if (!activeBuilder) return;

    const hasValidator = typeof activeBuilder.validateChanges === 'function';
    const isValid = !hasValidator || activeBuilder.validateChanges();

    if (isValid) {
      activeBuilder.save('apply');
      activeBuilder.saveSelectedUnsplashPhoto?.();
      disconnect();

      // 保存编辑的区块ID
      const sectionId = state.currentSection.getAttribute('unit_id');
      state.templateEditor.stepLocalizedContent.editedSections.push(sectionId);

      // 更新本地化数据
      const localizationData = state.templateEditor.updateHideOnMobileAttribute(sectionId);
      state.templateEditor.saveStepContent(localizationData);

      // 重新初始化横幅占位符（如果需要）
      if (['BANNER', 'BANNER_v2'].includes(state.templateEditor.templateSettings.type)) {
        state.templateEditor.templateSettings.reInitBannerPlaceholder();
      }

      // 触发区块变化事件
      eventManager.triggerSectionChange();
    }
  };

  /**
   * 丢弃更改
   */
  const discardChanges = () => {
    const { activeBuilder, currentSection, oldHtml } = state;
    
    if (!activeBuilder || !currentSection || !oldHtml) return;

    activeBuilder.save('discard');
    currentSection.innerHTML = oldHtml.innerHTML;
    
    // 恢复属性
    Object.keys(oldHtml.attributes).forEach(key => {
      const attr = oldHtml.attributes[key];
      currentSection.setAttribute(attr.name, attr.value);
    });

    disconnect();
  };

  /**
   * 克隆区块
   */
  const cloneSection = () => {
    applyChanges();
    
    const { currentSection } = state;
    if (!currentSection) return;

    // 检查行区块限制
    if (currentSection.parentElement.classList.contains('userpilot-cols-section')) {
      const rowSectionsCount = currentSection.parentElement.children.length;
      if (rowSectionsCount >= 3) {
        eventManager.showAlert('error', 'No more than three sections can be aligned');
        return;
      }
    }

    // 克隆区块
    const div = document.createElement('div');
    div.innerHTML = generateSectionIds(
      state.templateEditor.stepSettings.action_id, 
      currentSection.outerHTML
    );
    const clone = div.firstElementChild;
    
    currentSection.parentNode.insertBefore(clone, currentSection.nextSibling);

    // 处理本地化数据
    handleCloneLocalization(clone, currentSection);

    // 设置新区块事件
    state.templateEditor.setupNewSectionEvents(clone);
    state.templateEditor.saveStepContent(localizationData);

    // 重新初始化横幅占位符
    if (['BANNER', 'BANNER_v2'].includes(state.templateEditor.templateSettings.type)) {
      state.templateEditor.templateSettings.reInitBannerPlaceholder();
    }
  };

  /**
   * 处理克隆的本地化数据
   * @param {HTMLElement} clone - 克隆的元素
   * @param {HTMLElement} currentSection - 当前区块
   */
  const handleCloneLocalization = (clone, currentSection) => {
    const currentSectionId = currentSection.getAttribute('unit_id');
    const appState = state.templateEditor.store.getComponentState();
    const localizationData = appState.experience.localizationData;

    localizationData.forEach(localizationItem => {
      if (localizationItem.isDefault) return;

      const sectionCloned = clone.cloneNode(true);
      const localizedStep = localizationItem.steps[state.templateEditor.stepSettings.action_id];
      const stepContent = document.createElement('div');
      stepContent.innerHTML = localizedStep.data;

      const localizedCurrentSection = stepContent.querySelector(`[unit_id="${currentSectionId}"]`);
      localizedCurrentSection?.parentNode?.insertBefore(sectionCloned, localizedCurrentSection.nextSibling);

      localizedStep.data = stepContent.innerHTML;
    });
  };

  /**
   * 移除区块
   * @param {HTMLElement} section - 要移除的区块
   */
  const removeSection = (section = state.currentSection) => {
    if (!section) return;

    const appState = state.templateEditor.store.getComponentState();
    const localizationData = appState.experience.localizationData;
    const removedSectionId = section.getAttribute('unit_id');

    // 从编辑区块列表中移除
    const removedIndex = state.templateEditor.stepLocalizedContent.editedSections
      .findIndex(id => id === removedSectionId);
    
    if (removedIndex !== -1) {
      state.templateEditor.stepLocalizedContent.editedSections.splice(removedIndex, 1);
    }

    // 移除区块
    section.remove();
    state.templateEditor.sections = state.templateEditor.sections.filter(el => el !== section);
    
    // 从翻译中移除区块
    removeSectionFromTranslations(localizationData, removedSectionId);

    disconnect();
    state.templateEditor.saveStepContent(localizationData);

    // 重新初始化横幅占位符
    if (['BANNER', 'BANNER_v2'].includes(state.templateEditor.templateSettings.type)) {
      state.templateEditor.templateSettings.reInitBannerPlaceholder();
    }
  };

  /**
   * 从翻译中移除区块
   * @param {Array} localizationData - 本地化数据
   * @param {string} removedSectionId - 移除的区块ID
   */
  const removeSectionFromTranslations = (localizationData, removedSectionId) => {
    localizationData.forEach(localizationItem => {
      if (localizationItem.isDefault) return;

      const localizedStep = localizationItem.steps[state.templateEditor.stepSettings.action_id];
      const stepContent = document.createElement('div');
      stepContent.innerHTML = localizedStep.data;

      const localizedSection = stepContent.querySelector(`[unit_id="${removedSectionId}"]`);
      localizedSection?.remove();

      // 重新调整列区块
      eventManager.readjustColumnSections(stepContent);

      localizedStep.data = stepContent.querySelector('.userpilot-builder')?.innerHTML.trim() 
        ? stepContent.innerHTML 
        : '';
    });
  };

  /**
   * 断开编辑器连接
   */
  const disconnect = () => {
    observer.disconnect();
    toolbar.hide();
    
    // 移除编辑状态样式
    state.userpilotContainer.classList.remove('editor-active');
    state.currentSection?.classList?.remove('under-edit');
    state.userpilotContent.classList.remove('under-edit');

    // 重置状态
    updateEditorState(state, {
      activeBuilder: null,
      currentSection: null,
      oldHtml: null,
      editorActive: false
    });

    // 显示窗口组件
    eventManager.showWindowComponents();
  };

  // 构建器对象
  const builder = {
    init,
    attachSectionEvents,
    applyChanges,
    discardChanges,
    cloneSection,
    removeSection,
    disconnect,
    state,
    toolbar,
    eventManager,
    observer
  };

  return builder;
};

// 辅助函数
const generateSectionIds = (actionId, html) => {
  // 这里应该实现生成区块ID的逻辑
  // 暂时返回原始HTML
  return html;
};
