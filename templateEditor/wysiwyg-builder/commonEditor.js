import Common from "../../index.js"
import { AppData } from "../../common/user-data/appData.js";
import Views from "./wysiwigViews.js"
import Popup from "../../generic-utils/popup.js";
import TabSwitcher from "./tabSwitcher.js";
import SolidColorPicker from "../../generic-components/colorPickers/solidColorPicker.js";
import GradientColorPicker from "../../generic-components/colorPickers/gradientColorPicker.js";
import DirectionInput from "../../generic-components/directionInput.js";
import ExperienceBuilderViews from "../../views/experienceBuilderViews.js";
import WysiwygIcons from "./wysiwygIcons.js";
import UndoRedoManager from "./UndoRedoManager/undoRedoManager.js";
import CssOperations from "../../generic-utils/cssOperations.js";
import _ from 'lodash';
import ImageAdder from "./components/imageAdder.js";
import Personalization from "./components/personalization.js";
import htmlTreeOperations from "../../generic-utils/htmlTreeOperations.js";
import RangeOperations from "./rangeOperations.js";
import MessageHandler from "../../routing/messageHandler.js";
import tippy from "tippy.js";
import Unsplash from "../../generic-components/unsplash.js";

export default class BuilderSection {

    static colorPickerTypes = {
        SOLID: "solid",
        GRADIENT: "gradient"
    }

    constructor(el, popupToolbar) {
        this.iframeDocument = Common.shadowRoot.getElementById("tool-editor").contentDocument;
        this.iframeWindow = Common.shadowRoot.getElementById("tool-editor").contentWindow;
        this.sectionContainer = el;
        this.popupToolbar = popupToolbar;
        this.colorPicker = {
            btn: null,
            pickerObj: null
        };
        this.elementsToEdit;
        this.selectedUnsplashPhoto;
        this.personalization;
    }

    init() {
        Views.setWorker(this);
        this.elementsToEdit = Array.from(this.sectionContainer.querySelectorAll(".userpilot-builder-block"));
        this.sectionContainer.setAttribute("draggable", "false");
        this.initToolbarPopupEvents();
    }

    save() {
        this.sectionContainer.contentEditable = false;
        (this.sectionContainer.id === "userpilot-next-button" || this.sectionContainer.id === "userpilot-back-button")
            ? this.sectionContainer.setAttribute("draggable", false)
            : this.sectionContainer.setAttribute("draggable", true);

    }

    createTextnodeSpan(empty = false) {
        const textSpan = document.createElement("span");
        if(!empty) textSpan.textContent = "\u200B";

        return textSpan;
    }

    initToolbarPopupEvents() {
        const popupBtns = Array.from(this.popupToolbar.querySelectorAll(".popup-spawner"));
        popupBtns.forEach(btn => {
            const popupView = Views.getViewElement(btn.id);
            const PopupObj = new Popup(popupView, btn, null, null, {
                autoRepositionOnResize: true,
                setDirectionPriorityCallback: () => { return (this.popupToolbar.offsetTop < this.sectionContainer.offsetTop) ? "top" : "bottom" },
                addClassToParentOnClick: "active",
                topOffset: 10,
                container: btn.parentElement,
            });
            this.initPopupEvents(btn.id, popupView, PopupObj);
            this.initTippy(popupView);
        });
    }

    initTippy = (nodeEl) => {
        tippy(nodeEl.querySelectorAll("[label-on-hover]"), {
            appendTo: Common.shadowRoot,
            content: (el) => {
                const label = el.getAttribute('label-on-hover');
                el.removeAttribute('label-on-hover');

                return ExperienceBuilderViews.getViewElement("tippy-tooltip", { id: "", classes: "template-settings-tooltip", content: label });
            }
        });
    }

    initSectionSpecificEvents() {

    }

    /* JUSTIFY CONTENT */

    initAlignTextEvents(popupContainer, popupObj) {
        const alignBtns = Array.from(popupContainer.querySelectorAll(".align-btn"));
        alignBtns.forEach(btn => {
            btn.addEventListener("click", () => {
                alignBtns.forEach(btn => btn.classList.remove("selected"));
                btn.classList.add("selected");

                if (this.undoRedoManager)
                    this.undoRedoManager.saveForUndo(UndoRedoManager.MUTATION_TYPES.CUSTOM);

                if (this.getSelectionAlignment() === "justify") {
                    this.alignLastLine(btn.getAttribute("value"));
                } else {
                    this.align(btn.getAttribute("value"));
                    this.alignLastLine("");
                }
            });
        });

        const justifyBtn = popupContainer.querySelector(".justify-btn");
        justifyBtn.addEventListener("click", () => {
            const selectionAlignment = this.getSelectionAlignment();
            const lastLineAlignment = this.getLastAlignment();

            if (this.undoRedoManager)
                    this.undoRedoManager.saveForUndo(UndoRedoManager.MUTATION_TYPES.CUSTOM);

            if (selectionAlignment === "justify") {
                this.align(lastLineAlignment);
                this.alignLastLine("");
                justifyBtn.classList.remove("selected");
            } else {
                this.align("justify");
                this.alignLastLine(selectionAlignment);
                justifyBtn.classList.add("selected");
            }
        });

        popupObj.callbackOnPopupSpawn = () => {
            this.highlightActiveAlignments(alignBtns, justifyBtn);
            this.highlightSelectedBlocks();
        }

        popupObj.callbackOnPopupClose = () => {
            alignBtns.forEach(btn => btn.classList.remove("selected"));
            justifyBtn.classList.remove("selected");
            this.unhighlightSelectedBlocks();
        }
    }

    highlightActiveAlignments(alignBtns, justifyBtn) {
        const selectionAlignment = this.getSelectionAlignment();
        const lastLineAlignment = this.getLastAlignment();

        if (selectionAlignment === "justify") {
            alignBtns.find(btn => btn.getAttribute("value") == lastLineAlignment).classList.add("selected");
            justifyBtn.classList.add("selected");
        } else {
            alignBtns.find(btn => btn.getAttribute("value") == selectionAlignment).classList.add("selected");
        }
    }

    getSelectionAlignment() {
        return this.elementsToEdit[0].style.textAlign || getComputedStyle(this.elementsToEdit[0]).textAlign;
    }

    getLastAlignment() {
        return this.elementsToEdit[0].style.textAlignLast || getComputedStyle(this.elementsToEdit[0]).textAlignLast;
    }

    align(position) {
        this.elementsToEdit.forEach(element => element.style.textAlign = position);
    }

    alignLastLine(position) {
        this.elementsToEdit.forEach(element => element.style.textAlignLast = position);
    }

    /* Align whole section */
    initJustifySectionEvents(popupContainer) {
        const justifyOptions = Array.from(popupContainer.querySelectorAll(".align-btn"));
        let activeOption = justifyOptions.find(btn => btn.getAttribute("value") === getComputedStyle(this.sectionContainer).textAlign);
        activeOption.classList.add("selected");

        justifyOptions.forEach(btn => {
            btn.addEventListener("click", () => {
                activeOption.classList.remove("selected");
                btn.classList.add("selected");
                activeOption = btn;
                this.sectionContainer.style.textAlign = btn.getAttribute("value");
            });
        });
    }

    /* Selection Management */
    highlightSelectedBlocks() {
        this.elementsToEdit.forEach(block => block.style.backgroundColor = "rgb(172, 206, 247)");
    }

    unhighlightSelectedBlocks() {
        Array.from(this.sectionContainer.querySelectorAll(".userpilot-builder-block"))
        .forEach(block =>  block.style.backgroundColor = "");
    }

    /* END */

    /* SECTION PADDING  */

    initSectionPaddingEvents(popupContainer) {
        const paddingInputComponent = popupContainer.querySelector("#section-padding");
        new DirectionInput(paddingInputComponent, this.changeSectionPadding.bind(this), this.getSectionPaddingValues());
    }

    getSectionPaddingValues() {
        const style = getComputedStyle(this.sectionContainer);
        return {
            top: parseFloat(style.paddingTop),
            right: parseFloat(style.paddingRight),
            bottom: parseFloat(style.paddingBottom),
            left: parseFloat(style.paddingLeft)
        }
    }

    changeSectionPadding(value, direction) {
        const normalizedDirection = direction[0].toUpperCase() + direction.substr(1).toLowerCase();
        this.sectionContainer.style["padding" + normalizedDirection] = value + "px";
    }


    /* END */

    /* SECTION VISIBILITY */

    initVisibilityEvents(popupContainer, popupObj) {
        const checkbox = popupContainer.querySelector(".hide-on-mobile-checkbox");
        checkbox.checked = this.sectionContainer.hasAttribute("hide-on-mobile");

        checkbox.addEventListener("change", () => {
            checkbox.checked ? this.sectionContainer.setAttribute("hide-on-mobile", "") : this.sectionContainer.removeAttribute("hide-on-mobile");
        });
    }

    /* END */

    /* SECTION BACKGROUND */

    backgroundOptionsProps = {
        toggleOffColorBtn: null,
        deleteColorBtn: null
    }
    initSectionBgEvents(popupContainer, popupObj) {
        this.backgroundOptionsProps.toggleOffColorBtn = this.iframeDocument.createElement("div");
        this.backgroundOptionsProps.toggleOffColorBtn.id = "toggle-off-bg-color";

        this.backgroundOptionsProps.deleteColorBtn = this.iframeDocument.createElement("div");
        this.backgroundOptionsProps.deleteColorBtn.id = "delete-bg-color";

        this.initColorControllersEvents();

        this.initSectionBgTabs(popupContainer);

        const colorPickers = Array.from(popupContainer.querySelectorAll(".add-color"));
        colorPickers.forEach(btn => {
            new SolidColorPicker(btn, this.getSectionBackground.bind(this), {
                onChangeCallback: this.changeBackground.bind(this),
                onHideCallback: this.changeBackground.bind(this),
                onSaveCallback: (color) => {
                    this.changeBackground(color);
                    this.saveBgColor(BuilderSection.colorPickerTypes.SOLID, color);
                }
            });
        });

        const gradientColorPicker = popupContainer.querySelector(".add-gradient-color");
        new GradientColorPicker(gradientColorPicker, this.iframeWindow, popupContainer, this.getSectionBackground.bind(this), {
            onChangeCallback: this.changeBackground.bind(this),
            onHideCallback: this.changeBackground.bind(this),
            onSaveCallback: (color) => {
                this.changeBackground(color);
                this.saveBgColor(BuilderSection.colorPickerTypes.GRADIENT, color);
            }
        });

        const imageAdderComponent = popupContainer.querySelector(".image-upload-container");
        const currentImage = this.getSectionBackgroundImage();
        const imageAdder = new ImageAdder(imageAdderComponent, currentImage, this.updateBgImage.bind(this), this.removeBgImage.bind(this));
        popupObj.callbackOnPopupSpawn = () => imageAdder.resetUrlField();

        const bgColors = Array.from(popupContainer.querySelectorAll(".background-color"));
        bgColors.forEach(color => {
            this.initColorEventListeners(color);
            if (color.style.backgroundColor === this.sectionContainer.style.backgroundColor) this.setActiveColor(color);
        });

        const bgImages = Array.from(popupContainer.querySelectorAll(".background-image"));
        bgImages.forEach(image => {
            image.addEventListener("click", () => {
                this.changeBackground(image.style.background);
                this.backgroundOptionsProps.toggleOffColorBtn.remove();
            });
        });
    }

    initSectionBgTabs(popupContainer) {
        let isUnsplashInitialized = false;
        const initUnsplashCallback = () => {
            if(isUnsplashInitialized) return;

            const isMatchingUnsplashPhoto = (unsplashItem) => this.sectionContainer.style.background.includes(unsplashItem.urls.regular);
            const onSelect = (unsplashItem) => {
                const backgroundStyle = `url(${unsplashItem.urls.regular})`;
                this.changeBackground(backgroundStyle);

                this.selectedUnsplashPhoto = {
                    urls: {
                        regular: unsplashItem.urls.regular,
                        thumb: unsplashItem.urls.thumb,
                    },
                    links: unsplashItem.links,
                }
            }

            new Unsplash({
                container: popupContainer.querySelector("[tab-value='unsplash']"),
                recentImages: Common.userData.app_data.recent_images,
                isMatchingUnsplashPhoto,
                onSelect,
            });
            isUnsplashInitialized = true;
        };

        const tabSwitch = popupContainer.querySelector(".userpilot-tab-switch");
        new TabSwitcher(tabSwitch, popupContainer, "color", { unsplash: initUnsplashCallback });
    }

    updateBgImage(url) {
        this.changeBackground("url(" + url + ")");
        this.backgroundOptionsProps.toggleOffColorBtn.remove();
    }

    removeBgImage() {
        this.sectionContainer.style.background = "";
    }

    changeBackground(value) {
        this.sectionContainer.style.background = value;
        this.sectionContainer.style.backgroundSize = "cover";
        this.sectionContainer.style.maxHeight = "";
        this.sectionContainer.style.minHeight = "";
    }

    getSectionBackground() {
        const style = getComputedStyle(this.sectionContainer);
        return (style.backgroundImage == "none") ? CssOperations.updateRgbaOpacity(style.backgroundColor, 1) : style.backgroundImage;
    }

    getSectionBackgroundImage() {
        const style = getComputedStyle(this.sectionContainer);
        const urlRegex = /(?:\(['"]?)(.*?)(?:['"]?\))/

        return urlRegex.exec(style.backgroundImage)?.[1];
    }

    async saveSelectedUnsplashPhoto() {
        if(this.selectedUnsplashPhoto) {
            const { links, urls } = this.selectedUnsplashPhoto;
            await AppData.saveRecentImage({ urls });

            if(links) MessageHandler.unsplashTrackDownload({ params: { downloadLocation: links.download_location } });
        };
    }

    initColorControllersEvents() {
        this.backgroundOptionsProps.toggleOffColorBtn.addEventListener("click", (event) => {
            event.stopPropagation();
            this.sectionContainer.style.background = "";
            this.backgroundOptionsProps.toggleOffColorBtn.remove();
        });

        this.backgroundOptionsProps.deleteColorBtn.addEventListener("click", (event) => {
            event.stopPropagation();
            const colorElement = this.backgroundOptionsProps.deleteColorBtn.parentElement;
            const backgroundColor = colorElement.getAttribute("value");
            const isSolidColor = backgroundColor.startsWith("#");
            const backgroundColorType = (isSolidColor) ? BuilderSection.colorPickerTypes.SOLID : BuilderSection.colorPickerTypes.GRADIENT;

            if (colorElement.contains(this.backgroundOptionsProps.toggleOffColorBtn)) this.sectionContainer.style.background = "";
            colorElement.remove();
            AppData.removeColor(backgroundColorType, backgroundColor);
        });
    }

    initColorEventListeners(color) {
        color.addEventListener("click", () => {
            this.changeBackground(color.style.background);
            this.setActiveColor(color);
        });

        color.addEventListener("mouseenter", () => {
            color.appendChild(this.backgroundOptionsProps.deleteColorBtn);
            setTimeout(() => {
                this.backgroundOptionsProps.deleteColorBtn.style.opacity = 1;
            }, 200);
        });

        color.addEventListener("mouseleave", () => {
            this.backgroundOptionsProps.deleteColorBtn.style.opacity = 0;
            setTimeout(() => {
                this.backgroundOptionsProps.deleteColorBtn.remove();
            });
        });
    }

    setActiveColor(colorElement) {
        colorElement.appendChild(this.backgroundOptionsProps.toggleOffColorBtn);
    }

    saveBgColor(type = BuilderSection.colorPickerTypes.SOLID, color) {
        const colorElement = Views.getViewElement("background-color", {
            color: color
        });
        colorElement.addEventListener("click", this.changeBackground.bind(this, colorElement.style.background));
        this.initColorEventListeners(colorElement);
        this.setActiveColor(colorElement);

        if (type == BuilderSection.colorPickerTypes.SOLID) {
            const solidColorsContainer = this.popupToolbar.querySelector("#solid-colors");
            solidColorsContainer.insertBefore(colorElement, solidColorsContainer.lastElementChild);
            AppData.addColor(BuilderSection.colorPickerTypes.SOLID, color);
        }
        else if (type == BuilderSection.colorPickerTypes.GRADIENT) {
            const gradientColorsContainer = this.popupToolbar.querySelector("#gradient-colors");
            gradientColorsContainer.insertBefore(colorElement, gradientColorsContainer.lastElementChild);
            AppData.addColor(BuilderSection.colorPickerTypes.GRADIENT, color);
        }
    }

    initPersonalizeEvents(popupContainer, popupObj) {
        const onAdditionCallback = () => popupObj.closePopup();
        const onAttributeElementClickCallback = (attributeElement) => popupObj.spawnPopup(attributeElement);
        this.personalization = new Personalization(popupContainer, this, onAdditionCallback, onAttributeElementClickCallback);

        popupObj.callbackOnPopupSpawn = (self, attributeElement = null) => this.personalization.initAttributeElement(attributeElement);
        popupObj.callbackOnPopupClose = () => this.personalization.resetPersonlization();
    }

    changeFontProperty(value, property, refocusText = true) {
        this.undoRedoManager?.saveForUndo(UndoRedoManager.MUTATION_TYPES.CUSTOM);

        if (RangeOperations.isRangeSingleCaret(this.wysiwygOperations.selection)) {
            const fontSpan = this.createTextnodeSpan()
            fontSpan.style[property] = value;
            RangeOperations.insertNodeAndCollapse(this.iframeDocument, fontSpan, this.wysiwygOperations.selection);

            if (refocusText) {
                this.wysiwygOperations.resetSectionRange();
                this.sectionContainer.focus();
            }
        } else {
            const fontSpan = this.createTextnodeSpan(true)
            const offsets = RangeOperations.getRangeOffsetsBasedOnContainer(this.iframeDocument, this.wysiwygOperations.selection, this.sectionContainer);
            // If one text node is selected
            if ((this.wysiwygOperations.selection.startContainer === this.wysiwygOperations.selection.endContainer) && (this.wysiwygOperations.selection.startContainer === this.wysiwygOperations.selection.commonAncestorContainer)) {
                fontSpan.style[property] = value;
                this.wysiwygOperations.selection.surroundContents(fontSpan);
            } else {
                // If more than one text node is selected, iterate for all text nodes in section
                fontSpan.style[property] = value;
                RangeOperations.surroundSelection(this.iframeDocument, this.sectionContainer, this.wysiwygOperations.selection, fontSpan);
            }
            this.wysiwygOperations.selection = RangeOperations.getRangeBasedOnContainerOffsets(this.iframeDocument, offsets, this.sectionContainer);
        }
    }

    // The purpose of this function is to eliminate adjacent/nested text spans that share the same userpilot styles
    normalizeText(offsets, textContainer, userpilotTextStyles, mainSpansSelector) {
        if (offsets) {
            const excludedNodes = this.elementsToEdit?.filter(block => !block.textContent.replaceAll("\u200B", "").trim().length);
            offsets = RangeOperations.adjustRangeOffsetsBasedOnCharRemoval(this.iframeDocument, offsets, textContainer, "\u200B", excludedNodes);
        }

        const spanTreeWalker = this.iframeDocument.createTreeWalker(textContainer, NodeFilter.SHOW_ELEMENT, {
            acceptNode: (node) => node.tagName === "SPAN" && (node.parentElement.tagName === "SPAN" || node.parentElement.tagName === "UP-CONTAINER")
                ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP
        });

        while (spanTreeWalker.nextNode()) {
            const current = spanTreeWalker.currentNode;
            const parent = current.parentNode;
            const nextElement = current.nextElementSibling;
            const nextSibling = current.nextSibling;

            const isParentSpan = parent.tagName === "SPAN";
            const isNextElementSpan = nextElement?.tagName === "SPAN";
            const hasNextTextNode = nextSibling?.nodeName === "#text" ? nextSibling?.wholeText !== "" : false;

            if (isParentSpan && htmlTreeOperations.isSingleChild(current)) {
                spanTreeWalker.currentNode = textContainer;
                parent.style.cssText += current.style.cssText;
                htmlTreeOperations.unwrap(current);
            } else if (isParentSpan && CssOperations.areComputedStylesEqual(current, parent, userpilotTextStyles)) {
                spanTreeWalker.currentNode = textContainer;
                parent.style.cssText += current.style.cssText;
                htmlTreeOperations.unwrap(current);
            } else if (isNextElementSpan && !hasNextTextNode && CssOperations.areComputedStylesEqual(current, nextElement, userpilotTextStyles)) {
                spanTreeWalker.currentNode = textContainer;
                htmlTreeOperations.unwrapIntoNode(current, nextElement);
            } else if (parent.tagName === "UP-CONTAINER") {
                htmlTreeOperations.unwrap(current);
                htmlTreeOperations.wrapNode(parent, current);
            } else if (!current.innerHTML) {
                spanTreeWalker.currentNode = textContainer;
                current.remove();
            }
        }

        textContainer.normalize();
        this.fixUnderlineInheritance?.();

        if (offsets) {
            this.wysiwygOperations.selection = RangeOperations.getRangeBasedOnContainerOffsets(this.iframeDocument, offsets, textContainer);
            this.wysiwygOperations.resetSectionRange();
        }

        // Fill out empty main spans
        Array.from(textContainer.querySelectorAll(mainSpansSelector))
        .filter(span => span.textContent.trim().length === 0)
        .forEach(span => span.textContent = "\u200B");
    }
}