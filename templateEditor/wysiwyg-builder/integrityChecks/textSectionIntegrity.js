import htmlTreeOperations from "../../../generic-utils/htmlTreeOperations.js";
import Personalization from "../components/personalization.js";
import TextSection from "../textEditor.js";

export const runTextSectionIntegrity = (sectionContainer, parentDocument = document) => {
    runTextnodeChecks(parentDocument, sectionContainer);
    runInlineTagsChecks(sectionContainer);
    runTextSpanChecks(sectionContainer);
    runBuilderBlockChecks(sectionContainer);
    runSectionDivsChecks(sectionContainer);
    runListChecks(sectionContainer);
    Personalization.runPersonalizationNodeChecks(sectionContainer);
    runDeprecatedTagChecks(sectionContainer);
    runEmptyBlockChecks(sectionContainer);
    removeHighlights(sectionContainer);
    removeComments(parentDocument, sectionContainer);
}

const runBuilderBlockChecks = (sectionContainer) => {
    const hasMultiChildren = () => Array.from(sectionContainer.querySelectorAll(".userpilot-builder-block"))
        .some(builderBlocks => builderBlocks.children.length > 1)

    while(hasMultiChildren()) {
        Array.from(sectionContainer.querySelectorAll(".userpilot-builder-block"))
        .filter(builderBlocks => builderBlocks.children.length > 1)
        .forEach(builderBlock => {
            Array.from(builderBlock.children).forEach(child => htmlTreeOperations.wrapNode(child, builderBlock.cloneNode(false)));
            Array.from(builderBlock.querySelectorAll("div:not(.userpilot-builder-block)")).forEach(element => htmlTreeOperations.unwrap(element));
            htmlTreeOperations.unwrap(builderBlock);
        });
    }

    Array.from(sectionContainer.querySelectorAll(".userpilot-builder-block .userpilot-builder-block"))
    .forEach(block => htmlTreeOperations.unwrap(block));

    mergeAdjacentTextSpans(sectionContainer);
}

const runListChecks = (sectionContainer) => {
    const lists = Array.from(sectionContainer.querySelectorAll("ul, ol"))
    .filter(list => list.parentElement !== sectionContainer);

    lists.forEach(list => {
        const parentBuilderBlock = list.parentElement;
        const listElements = Array.from(list.querySelectorAll("li"));

        listElements.forEach(element => {
            const builderBlockClone = parentBuilderBlock.cloneNode(false);
            htmlTreeOperations.wrapNode(element, builderBlockClone);
            htmlTreeOperations.unwrap(element);
        });
        htmlTreeOperations.unwrap(parentBuilderBlock);
    });
}

const runTextnodeChecks = (doc, sectionContainer) => {
    const textTreeWalker = doc.createTreeWalker(sectionContainer, NodeFilter.SHOW_TEXT, {
        acceptNode: (node) => node.wholeText.trim().length !== 0
    });

    while (textTreeWalker.nextNode()) {
        const current = textTreeWalker.currentNode;
        const parent = current.parentElement;

        const isUnderBuilderBlock = parent.tagName === "DIV";
        const isUnderListItem = parent.tagName === "LI";
        const isUnderTextSection = parent.tagName === "DIV" && parent.classList.contains("userpilot-text-section");

        // Text node is under a block but not contained in span, so merge it with a sibling span or wrap it in a span if none exist
        if (isUnderBuilderBlock || isUnderListItem) {
            const prevSibling = current?.previousElementSibling;
            const nextSibling = current?.nextElementSibling;

            if (prevSibling && prevSibling?.tagName === "SPAN") {
                prevSibling.appendChild(current)
            } else if(nextSibling && nextSibling?.tagName === "SPAN") {
                nextSibling.firstChild 
                ? nextSibling.insertBefore(current, nextSibling.firstChild)
                : nextSibling.appendChild(current);
            } else {
                htmlTreeOperations.wrapNode(current, TextSection.getTextnodeSpan())
            }

            textTreeWalker.currentNode = parent.parentNode;
            continue;
        }

        // Text node is under the text section and should be considered as a block by itself (paragraph)
        if (isUnderTextSection) {
            const builderBlock = TextSection.getTextBuilderblock();
            current.parentElement.insertBefore(builderBlock, current);
            builderBlock.querySelector("span").appendChild(current);
            textTreeWalker.currentNode = parent.parentNode;
            continue;
        }
    }
    htmlTreeOperations.mergeTextnodeSiblings(doc, sectionContainer, true);
}

const runInlineTagsChecks = (sectionContainer) => {
    Array.from(sectionContainer.querySelectorAll("a, up-container"))
    .filter(tag => tag.parentElement.tagName !== "SPAN")
    .forEach(tag => htmlTreeOperations.wrapNode(tag, TextSection.getTextnodeSpan()));
}

const runTextSpanChecks = (sectionContainer) => {
    mergeAdjacentTextSpans(sectionContainer);

    Array.from(sectionContainer.querySelectorAll(":scope > span"))
    .forEach(span => htmlTreeOperations.wrapNode(span, TextSection.getTextBuilderblock(false)));


    Array.from(sectionContainer.querySelectorAll("span"))
    .forEach(span => {
        if (!span.classList.contains("userpilot-text-node")) {
            span.classList.add("userpilot-text-node");
        }

        if (span.textContent.trim().length === 0) {
            span.textContent = "\u200B";
        }
    });
}

const runSectionDivsChecks = (sectionContainer) => {
    Array.from(sectionContainer.querySelectorAll(":scope > div:not(.userpilot-builder-block)"))
    .map(item => {
        item.querySelectorAll("div")
        .forEach(div => {
            if(div.nextSibling) htmlTreeOperations.insertAfter(div, document.createElement("br"));
            htmlTreeOperations.unwrap(div);
        })
        return item;
    })
    .forEach(item => {
        const builderBlock = TextSection.getTextBuilderblock();
        builderBlock.querySelector("span").innerHTML = item.innerHTML;
        item.outerHTML = builderBlock.outerHTML;
    });
}

export const mergeAdjacentTextSpans = (sectionContainer) => {
    Array.from(sectionContainer.querySelectorAll("span"))
    .filter(span => span?.nextElementSibling?.tagName === "SPAN" && span?.previousElementSibling?.tagName !== "SPAN" && span.parentElement.tagName !== "SPAN")
    .forEach(span => {
        const siblingSpans = htmlTreeOperations.getAllAdjacentNodesOfSameTag(span);
        const wrapper = TextSection.getTextnodeSpan(true);

        htmlTreeOperations.wrapNode(span, wrapper);
        siblingSpans.forEach(siblingSpan => wrapper.appendChild(siblingSpan));
    });
}

const runDeprecatedTagChecks = (sectionContainer) => {
    Array.from(sectionContainer.querySelectorAll("b, u, i"))
    .forEach(tag => {
        const textSpan = TextSection.getTextnodeSpan(true);
        switch(tag.tagName) {
            case "B":
                textSpan.style.fontWeight = 700;
                break;
            case "U":
                textSpan.style.textDecoration = "underline";
                break;
            case "I":
                textSpan.style.fontStyle = "italic";
                break;
            default:
                console.error("DevError: unhandled tag type");
        }
        htmlTreeOperations.wrapNode(tag, textSpan);
        htmlTreeOperations.unwrap(tag);
    });
}

const runEmptyBlockChecks = (sectionContainer) => {
    Array.from(sectionContainer.querySelectorAll(".userpilot-builder-block"))
    .filter(block => block.textContent.trim().length === 0 && block.firstElementChild?.tagName !== "SPAN")
    .forEach(block => {
        const textSpan = TextSection.getTextnodeSpan();
        block.appendChild(textSpan);
    });
}

const removeHighlights = (sectionContainer) => {
    Array.from(sectionContainer.querySelectorAll("span[style*='background-color']")).forEach(span => {
        span.style.backgroundColor = "";
    });

    Array.from(sectionContainer.querySelectorAll("a[style*='background-color']")).forEach(link => {
        link.style.backgroundColor = "";
    });

    Array.from(sectionContainer.querySelectorAll("u[style*='background-color'], b[style*='background-color'], i[style*='background-color']")).forEach(style => {
        style.style.backgroundColor = "";
    });
}

const removeComments = (doc, sectionContainer) => {
    const iterator = doc.createTreeWalker(sectionContainer, NodeFilter.SHOW_COMMENT);
    const nodes = [];

    let currentNode = null
    while(currentNode = iterator.nextNode()) {
        nodes.push(currentNode);
    }

    nodes.forEach(node => node.remove());
}