import BuilderSection from './commonEditor.js';
import InputSlider from "../../generic-components/inputSlider.js";
import SolidColorPicker from "../../generic-components/colorPickers/solidColorPicker.js";
import TextUndoRedoManager from "./UndoRedoManager/text.js";
import HeaderEditorOperations from './utils/headerEditorOperations.js';
import Personalization from './components/personalization.js';

export default class HeaderSection extends BuilderSection {

    constructor(el, popupToolbar) {
        super(el, popupToolbar);
        this.wysiwygOperations = new HeaderEditorOperations(this);
    }

    /* Initializers */

    init() {
        Personalization.runPersonalizationNodeChecks(this.sectionContainer)
        super.init();
        this.undoRedoManager = new TextUndoRedoManager(this);

        this.sectionContainer.contentEditable = true;
        this.wysiwygOperations.collapseCaretOnNode(this.getSectionSelectionTarget());
        this.initFontColorEventListeners();
        this.sectionContainer.addEventListener("keydown", this.keyboardHandler, true);
    }

    save(_event) {
        super.save();
        this.undoRedoManager.destroy();
        this.wysiwygOperations.removeZeroWidthSpace();

        if (!this.sectionContainer.textContent.replaceAll("\u200B", "").trim().length) {
            this.sectionContainer.innerHTML = '<div class="userpilot-builder-block userpilot-h2"></div>';
        }
    }

    keyboardHandler = (event) => {
        switch(event.code) {
            case "Backspace":
                return this.wysiwygOperations.handleBackspaceKey(event);
            default:
                return true;
        }
    }

    getSectionSelectionTarget() {
        const section = this.sectionContainer.querySelector(".userpilot-builder-block:last-child");
        if (!section.textContent) section.textContent = "\u200B";

        return section.lastChild;
    }

    initPopupEvents(popupName, popupContainer, popupObj) {
        switch (popupName) {
            case "header-size":
                this.initHeaderSizeEvents(popupContainer, popupObj);
                break;
            case "image-resize":
                this.initBackgroundOptionsEvents(popupContainer, popupObj);
                break;
            case "justify-content":
                this.initAlignTextEvents(popupContainer, popupObj);
                break;
            case "section-padding":
                this.initSectionPaddingEvents(popupContainer);
                break;
            case "section-background":
                this.initSectionBgEvents(popupContainer, popupObj);
                break;
            case "text-personalize":
                this.initPersonalizeEvents(popupContainer, popupObj);
                break;
            case "section-visibility":
                this.initVisibilityEvents(popupContainer, popupObj);
                break;
            default:
                break;
        }
    }

    /* END */

    /* Header Size */

    initHeaderSizeEvents(popupContainer, popupObj) {
        const headerOptions = Array.from(popupContainer.querySelectorAll(".raw-option"));

        headerOptions.forEach(option => {
            option.addEventListener("click", (event) => {
                this.undoRedoManager.saveForUndo(TextUndoRedoManager.MUTATION_TYPES.CUSTOM);
                const selectedOption = headerOptions.find(option => option.classList.contains("selected"));
                if (selectedOption) selectedOption.classList.remove("selected");
                option.classList.add("selected");

                const value = option.getAttribute("value");
                this.editHeader(value);
            });
        });

        popupObj.callbackOnPopupSpawn = () => {
            headerOptions.forEach(option => {
                const optionValue = option.getAttribute("value");
                option.classList.remove("selected");
                if (this.elementsToEdit[0].classList.contains(optionValue)) option.classList.add("selected");
            });
            this.highlightSelectedBlocks();
            this.wysiwygOperations.preventNewSelection = true;
        }
        popupObj.callbackOnPopupClose = () => this.unhighlightSelectedBlocks();
    }

    editHeader(type) {
        this.elementsToEdit.forEach(element => {
            element.classList.forEach(cls => {
                if (cls.startsWith("userpilot-h")) element.classList.remove(cls);
            });
            element.classList.add(type);
        });
    }

    /* END */

    /* Header Color */

    initFontColorEventListeners() {
        const fontColorIcon = this.popupToolbar.querySelector("#font-color");
        new SolidColorPicker(fontColorIcon, this.getSelectedBlockColor.bind(this), {
            onInitCallback: this.highlightSelectedBlocks.bind(this),
            onChangeCallback: this.fontColor.bind(this),
            onSaveCallback: this.unhighlightSelectedBlocks.bind(this),
            onHideCallback: this.unhighlightSelectedBlocks.bind(this),
        });

        this.sectionContainer.addEventListener("keyup", this.findCurrentColor.bind(this));
        this.sectionContainer.addEventListener("mouseup", this.findCurrentColor.bind(this));
        this.findCurrentColor();
    }

    findCurrentColor() {
        setTimeout(() => {
            const fontColorButton = this.popupToolbar.querySelector("#font-color");
            const currentColor = this.getSelectedBlockColor();
            fontColorButton.querySelector("svg").style.fill = currentColor;
        }, 50);
    }

    getSelectedBlockColor() {
        return this.elementsToEdit[0].style.color || getComputedStyle(this.elementsToEdit[0]).color;
    }

    fontColor(color) {
        this.undoRedoManager.saveForUndo(TextUndoRedoManager.MUTATION_TYPES.CUSTOM);
        this.elementsToEdit.forEach(element => {
            element.style.color = color;
        });
    }

    /* END */

    /* Background Styles */

    initBackgroundOptionsEvents(popupContainer, popupObj) {
        const sectionStyles = getComputedStyle(this.sectionContainer);
        const imageZoom = parseFloat(sectionStyles.backgroundSize);
        const imageHeight = parseFloat(this.sectionContainer.offsetHeight);

        const zoomSlider = popupContainer.querySelector("#zoom-slider");
        new InputSlider(zoomSlider, this.setImageZoom.bind(this), imageZoom);

        const heightSlider = popupContainer.querySelector("#height-slider");
        new InputSlider(heightSlider, this.setImageHeight.bind(this), imageHeight);

        popupObj.callbackOnPopupSpawn = () => {
            if (this.sectionContainer.style.background.includes("url")) popupContainer.classList.remove("no-image");
            else popupContainer.classList.add("no-image");
        }
    }

    setImageHeight(value) {
        this.sectionContainer.style.minHeight = value + "px";
        this.sectionContainer.style.maxHeight = value + "px";
    }

    setImageZoom(value) {
        this.sectionContainer.style.setProperty("background-size", value + "%", "important");
    }

    /* END */

}