import InputSlider from "../../generic-components/inputSlider";
import { isValidUrl } from "../../generic-utils/urlUtils";
import BuilderSection from "./commonEditor";
import { EMBED_OPTIONS } from "./components/embeds/constants";
import Views from "./wysiwigViews";

export default class EmbedSection extends BuilderSection {
    constructor(el, popupToolbar) {
        super(el, popupToolbar);
    }

    init() {
        const iframeEl = this.sectionContainer.querySelector("iframe");
        if(!iframeEl) this.popupToolbar.classList.add("no-embed");
        else this.popupToolbar.classList.remove("no-embed");
        super.init();
    }

    save(_event) {
        super.save();
    }

    initPopupEvents(popupName, popupContainer, popupObj) {
        switch(popupName) {
            case "embed-link":
                this.initEmbedLink(popupContainer, popupObj);
                break;
            case "embed-size":
                this.initEmbedSizeEvents(popupContainer, popupObj);
                break;
            case "section-padding":
                this.initSectionPaddingEvents(popupContainer);
                break;
            case "section-background":
                this.initSectionBgEvents(popupContainer, popupObj);
                break;
            case "section-visibility":
                this.initVisibilityEvents(popupContainer, popupObj);
                break;
            default:
                break;
        }
    }

    initInputEventListeners(popupContainer) {
        const embedLinkContainerEl = popupContainer.querySelector(".embed-link-container");
        const embedLinkInputEl = popupContainer.querySelector("input");
        const iframeEl = this.sectionContainer.querySelector("iframe");
        embedLinkInputEl.value = iframeEl?.src || "";

        embedLinkInputEl.addEventListener("input", () => {
            if(isValidUrl(embedLinkInputEl.value)) embedLinkContainerEl.classList.remove("invalid");
            else return embedLinkContainerEl.classList.add("invalid");

            if(this.isValidEmbedUrl(embedLinkInputEl.value)) embedLinkContainerEl.classList.remove("invalid-url-type");
            else embedLinkContainerEl.classList.add("invalid-url-type");
        });

        embedLinkInputEl.addEventListener("change", () => {
            if(this.isValidEmbed(embedLinkInputEl.value)) {
                this.updateEmbed(embedLinkInputEl.value);
                const updatedIframeEl = this.sectionContainer.querySelector("iframe");
                embedLinkInputEl.value = updatedIframeEl.src;
            }
            if(!embedLinkInputEl.value) {
                this.popupToolbar.classList.add("no-embed");
                this.sectionContainer.innerHTML = "<empty></empty>";
            }
        });
    }

    initEmbedLink(popupContainer, popupObj) {
        this.initInputEventListeners(popupContainer);

        if(!this.sectionContainer.querySelector("iframe")) {
            popupObj.spawnPopup();
            setTimeout(() => popupContainer.querySelector("input").focus());
        }
    }

    initEmbedSizeEvents(popupContainer, popupObj) {
        let iframeContainer = this.sectionContainer.querySelector("div:first-child");
        const widthSliderView = popupContainer.querySelector("#embed-width-slider");
        const heightSliderView = popupContainer.querySelector("#embed-height-slider");

        const embedMaxWidthCheckbox = popupContainer.querySelector("#embed-max-width");
        const handleCheckboxChange = () => {
            if(embedMaxWidthCheckbox.checked) {
                widthSliderView.parentElement.classList.add("display-none");
                iframeContainer.style.width = "100%";
                widthSlider.changeSliderValue(iframeContainer.offsetWidth);
            } else {
                widthSliderView.parentElement.classList.remove("display-none");
                iframeContainer.style.width = `${iframeContainer.offsetWidth}px`;
            }
        }
        embedMaxWidthCheckbox.addEventListener("input", handleCheckboxChange);
        
        const widthSlider = new InputSlider(widthSliderView, this.changeEmbedWidth);
        const heightSlider = new InputSlider(heightSliderView, this.changeEmbedHeight);

        popupObj.callbackOnPopupSpawn = () => {
            if(!iframeContainer) iframeContainer = this.sectionContainer.querySelector("div:first-child");

            const templateWidth = this.iframeDocument.querySelector(".userpilot-slide").offsetWidth;
            const embedWidth = iframeContainer.style.width;

            widthSlider.changeMax(templateWidth);
            if(embedWidth === "100%") {
                embedMaxWidthCheckbox.checked = true;
                handleCheckboxChange();
            }
            widthSlider.changeSliderValue(iframeContainer.offsetWidth);
            heightSlider.changeSliderValue(iframeContainer.offsetHeight);
        }
    }

    changeEmbedWidth = (value) => {
        const iframeContainer = this.sectionContainer.querySelector("div:first-child");
        iframeContainer.style.width = value + "px";
    }

    changeEmbedHeight = (value) => {
        const iframeContainer = this.sectionContainer.querySelector("div:first-child");
        iframeContainer.style.height = value + "px";
    }

    updateEmbed(embedLink) {
        const iframe = this.sectionContainer.querySelector("iframe");
        if(iframe) return this.updateEmbedLink(iframe, embedLink);

        const embedContent = this.getEmbedContent(embedLink);
        this.sectionContainer.innerHTML = embedContent.outerHTML;
        this.popupToolbar.classList.remove("no-embed");
    }

    updateEmbedLink(iframe, embedLink) {
        if(!this.isValidEmbed(embedLink)) return;
        /**
         * Tried to change the iframe src only but that would trigger
         * a tab update in the background.js and causes the CE to reload.
         */
        const embedContent = this.getEmbedContent(embedLink);
        const newIframe = embedContent.querySelector("iframe");
        iframe.outerHTML = newIframe.outerHTML;
    }

    getEmbedContent(embedLink) {
        if(!this.isValidEmbed(embedLink)) return "<empty></empty>";

        const embedType = this.sectionContainer.getAttribute("type");
        const urlObj = new URL(embedLink);

        switch(embedType) {
            case EMBED_OPTIONS.SYNTHESIA:
                if(urlObj && !embedLink.includes("/embeds/")) {
                    urlObj.pathname = "/embeds/videos" + urlObj.pathname;
                    embedLink = urlObj.href;
                }
                return Views.getViewElement(`${embedType}-embed-section-type`, { embedLink });
                
            case EMBED_OPTIONS.TYPEFORM:
                if(!urlObj.search) {
                    urlObj.search = "?typeform-embed=oembed&typeform-medium=embed-oembed&format=json&disable-auto-focus=true";
                    embedLink = urlObj.href;
                }
                return Views.getViewElement(`${embedType}-embed-section-type`, { embedLink });

            case EMBED_OPTIONS.LOOM:
                if(embedLink.includes("/share/")) {
                    embedLink = embedLink.replace("/share/", "/embed/");
                }
                return Views.getViewElement(`${embedType}-embed-section-type`, { embedLink });

            case EMBED_OPTIONS.CALENDLY:
                return Views.getViewElement(`${embedType}-embed-section-type`, { embedLink });

            default:
                return "";
        }
    }

    isValidEmbedUrl(embedLink) {
        const embedType = this.sectionContainer.getAttribute("type");
        const urlObj = new URL(embedLink);

        switch(embedType) {
            case EMBED_OPTIONS.SYNTHESIA:
                return urlObj.hostname.includes(EMBED_OPTIONS.SYNTHESIA)
                
            case EMBED_OPTIONS.TYPEFORM:
                return urlObj.hostname.includes(EMBED_OPTIONS.TYPEFORM)

            case EMBED_OPTIONS.LOOM:
                return urlObj.hostname.includes(EMBED_OPTIONS.LOOM)

            case EMBED_OPTIONS.CALENDLY:
                return urlObj.hostname.includes(EMBED_OPTIONS.CALENDLY)

            default:
                return "";
        }
    }

    isValidEmbed(embedLink) {
        return isValidUrl(embedLink) && this.isValidEmbedUrl(embedLink);
    }
}
