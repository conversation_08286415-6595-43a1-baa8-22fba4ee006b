import BuilderSection from './commonEditor.js';

// Components
import InputSlider from "../../generic-components/inputSlider.js";
import SolidColorPicker from "../../generic-components/colorPickers/solidColorPicker.js";
import DirectionInput from "../../generic-components/directionInput.js";
import WysiwygOperations from './utils/wysiwygOperations.js';

// Utils
import ButtonUndoRedoManager from "./UndoRedoManager/button.js";
import CssOperations from '../../generic-utils/cssOperations.js';
export default class ButtonSpotlightSection extends BuilderSection {

    constructor(el, popupToolbar, templateEditor) {
        super(el, popupToolbar);
        this.actionSelected = '';
        this.button;
        this.wysiwygOperations = new WysiwygOperations(this);
        this.templateEditor = templateEditor;

        popupToolbar.querySelector("#controller-toolbar .section #remove-section")?.parentNode?.remove()
    }

    /* Inititalizers */

    init() {
        this.button = this.sectionContainer.querySelector('.userpilot-btn');
        this.buttonStyles = getComputedStyle(this.button);
        this.button.contentEditable = true;

        super.init();
        this.undoRedoManager = new ButtonUndoRedoManager(this);

        const targetSelectionElement = this.getSectionSelectionTarget();
        this.wysiwygOperations.selection.selectNodeContents(targetSelectionElement);
        this.wysiwygOperations.selection.collapse(false);
        this.wysiwygOperations.resetSectionRange();
    }

    save(event = 'apply') {
        event == "apply" && this.apply();
        super.save();
        this.button.contentEditable = false;
    }

    apply() {
        const fontWeight = parseInt(getComputedStyle(this.button).fontWeight);
        const fontColor = this.getButtonFontColor();
        const fontHoverColor = this.getButtonFontHoverColor();
        const backgroundColor = this.getButtonBackgroundColor();
        const backgroundHoverColor = this.getButtonBackgroundHoverColor();
        const padding = `${this.buttonStyles.paddingTop} ${this.buttonStyles.paddingRight} ${this.buttonStyles.paddingBottom} ${this.buttonStyles.paddingLeft}`;
        const text = this.button.innerText.trim();

        this.templateEditor.templateSettings.themeManager.setJsonSetting(["button", "background_color"], !backgroundColor.indexOf("#") ? backgroundColor : CssOperations.rgbToHexa(backgroundColor));
        this.templateEditor.templateSettings.themeManager.setJsonSetting(["button", "background_color_hover"], backgroundHoverColor);
        this.templateEditor.templateSettings.themeManager.setJsonSetting(["button", "font", "color"], !fontColor.indexOf("#") ? fontColor : CssOperations.rgbToHex(fontColor));
        this.templateEditor.templateSettings.themeManager.setJsonSetting(["button", "font", "color_hover"], fontHoverColor);

        this.templateEditor.templateSettings.themeManager.setJsonSetting(["button", "font", "weight"], fontWeight);
        this.templateEditor.templateSettings.themeManager.setJsonSetting(["button", "box_border", "padding"], padding);

        text && this.templateEditor.templateSettings.themeManager.setJsonSetting(["individual", "label", "text"], text);

        this.templateEditor.templateSettings.reInitGroupSettingsNode();

    }

    getSectionSelectionTarget() {
        const section = this.button;
        if (section.firstChild) return section.firstChild;
        else return section;
    }

    initPopupEvents(popupName, popupContainer, popupObj) {
        switch (popupName) {
            case "button-typography":
                this.initButtonTypographyEvents(popupContainer, popupObj);
                break;
            case "button-colors":
                this.initButtonColorsEvents(popupContainer, popupObj);
                break;
            case "button-font-colors":
                this.initButtonFontColorsEvents(popupContainer, popupObj);
                break;
            case "button-configv2":
                this.initButtonConfig(popupContainer, popupObj);
                break;
            default:
                break;
        }
    }

    /* END */

    changeButtonStyle(property, value) {
        this.undoRedoManager.saveForUndo(ButtonUndoRedoManager.MUTATION_TYPES.BUTTON_STYLE);
        this.button.style[property] = value;
    }

    changeButtonHoverStyle(property, value) {
        this.undoRedoManager.saveForUndo(ButtonUndoRedoManager.MUTATION_TYPES.BUTTON_STYLE);
        this.button.setAttribute(property, value);
    }

    /* Button Styles */

    initButtonTypographyEvents(popupContainer, popupObj) {

        const updateCallback = (value) => {
            this.changeButtonStyle("fontWeight", value)
        }

        const fontWeightSliderView = popupContainer.querySelector("#font-weight-slider");
        const fontWeightSlider = new InputSlider(fontWeightSliderView, updateCallback, parseInt(this.buttonStyles.fontWeight));

        popupObj.callbackOnPopupSpawn = () => {
            const fontWeight = parseInt(getComputedStyle(this.button).fontWeight);
            fontWeightSlider.changeSliderValue(parseInt(fontWeight));
        }
    }

    /* END */

    /* Button Styles */


    getButtonFontColor() {
        const colorType = this.button.getAttribute("color-type") || "automatic"

        if (colorType == "1") return this.button.getAttribute("font-orignal-font-color");

        return this.button.style.color || this.buttonStyles.color;
    }

    initButtonColorsEvents(popupContainer, popupObj) {
        const options = {
            rgba_response: true
        }
        const buttonBgColorPicker = popupContainer.querySelector("#button-bg-color");
        new SolidColorPicker(buttonBgColorPicker, this.getButtonBackgroundColor.bind(this), {
            onChangeCallback: (color) => {
                this.changeButtonStyle("background", color.hexa);
                buttonBgColorPicker.style.background = color.hexa;
            }
        }, options);

        const buttonBgHoverColorPicker = popupContainer.querySelector("#button-bg-hover-color");
        new SolidColorPicker(buttonBgHoverColorPicker, this.getButtonBackgroundHoverColor, {
            onChangeCallback: (color) => {
                this.changeButtonHoverStyle("bg-hover-color", color.hexa);
                buttonBgHoverColorPicker.style.background = color.hexa;
            }
        }, options);

        this.initButtonBackgroundHoverColor();
        this.initButtonFontHoverColor();

        popupObj.callbackOnPopupSpawn = () => {
            buttonBgColorPicker.style.background = this.getButtonBackgroundColor();
            buttonBgHoverColorPicker.style.background = this.getButtonBackgroundHoverColor();
        }
    }

    initButtonFontColorsEvents(popupContainer, popupObj) {
        const colorType = this.button.getAttribute("color-type") || "automatic"
        if (colorType == "automatic") {
            const fontColorBtn = this.popupToolbar.querySelector("#button-font-colors");
            fontColorBtn.style.display = "none";
            return false;
        }
        const buttonFontColorPicker = popupContainer.querySelector("#button-font-color");
        new SolidColorPicker(buttonFontColorPicker, this.getButtonFontColor.bind(this), {
            onChangeCallback: (color) => {
                this.changeButtonStyle("color", color);
                buttonFontColorPicker.style.background = color;
            }
        });

        const buttonFontHoverColorPicker = popupContainer.querySelector("#button-font-hover-color");
        new SolidColorPicker(buttonFontHoverColorPicker, this.getButtonFontHoverColor, {
            onChangeCallback: (color) => {
                this.changeButtonHoverStyle("font-hover-color", color);
                buttonFontHoverColorPicker.style.background = color;
            }
        });

        this.initButtonBackgroundHoverColor();
        this.initButtonFontHoverColor();

        popupObj.callbackOnPopupSpawn = () => {
            buttonFontColorPicker.style.background = this.getButtonFontColor();
            buttonFontHoverColorPicker.style.background = this.getButtonFontHoverColor();
        }
    }

    getButtonBackgroundColor() {
        return this.button.style.background || this.buttonStyles.backgroundColor;
    }

    getButtonBorderColor() {
        return this.button.style.borderColor || this.buttonStyles.borderColor;
    }

    getButtonBackgroundHoverColor = () => {
        return this.button.getAttribute("bg-hover-color") || this.getThemeButtonBackgroundHoverColor();
    }

    getButtonFontHoverColor = () => {
        const colorType = this.button.getAttribute("color-type") || "automatic"
        if (colorType == "1") return this.button.getAttribute("font-orignal-font-color-hover");

        return this.button.getAttribute("font-hover-color") || this.getThemeButtonFontHoverColor();
    }

    getThemeButtonBackgroundHoverColor = () => {
        return this.templateEditor.templateSettings.themeManager.getCurrentSettingValue(["button", "background_color_hover",]);
    }

    getThemeButtonFontHoverColor = () => {
        return this.templateEditor.templateSettings.themeManager.getCurrentSettingValue(["button", "font", "color_hover"]);
    }

    initButtonBackgroundHoverColor = () => {
        const backgroundHoverColor = this.getButtonBackgroundHoverColor();
        this.button.style.setProperty("--badge-background-hover", backgroundHoverColor);

        this.button.addEventListener('mouseenter', () => {
            const backgroundHoverColor = this.button.getAttribute("bg-hover-color");
            if (!backgroundHoverColor) return;

            this.button.classList.add("hovered-on");
            this.button.style.setProperty("--badge-background-hover", backgroundHoverColor);
        })

        this.button.addEventListener('mouseleave', () => {
            this.button.classList.remove("hovered-on");
            this.button.style.setProperty("--badge-background-hover", "");
        })
    }

    initButtonFontHoverColor = () => {
        const fontHoverColor = this.getButtonFontHoverColor();
        this.button.style.setProperty("--badge-font-hover", fontHoverColor);

        this.button.addEventListener('mouseenter', () => {
            const fontHoverColor = this.button.getAttribute("font-hover-color");
            if (!fontHoverColor) return;

            this.button.classList.add("hovered-on");
            this.button.style.setProperty("--badge-font-hover", fontHoverColor);
        })

        this.button.addEventListener('mouseleave', () => {
            this.button.classList.remove("hovered-on");
            this.button.style.setProperty("--badge-font-hover", "");
        })
    }

    initButtonConfig(popupContainer, popupObj) {
        const paddingInputComponent = popupContainer.querySelector("#button-padding");
        const directionInputs = new DirectionInput(paddingInputComponent, this.changeButtonPadding.bind(this), this.getButtonPaddingValues());

        popupObj.callbackOnPopupSpawn = () => {
            directionInputs.setValues(this.getButtonPaddingValues());
        }
    }

    getButtonPaddingValues() {
        return {
            top: parseFloat(this.buttonStyles.paddingTop),
            right: parseFloat(this.buttonStyles.paddingRight),
            bottom: parseFloat(this.buttonStyles.paddingBottom),
            left: parseFloat(this.buttonStyles.paddingLeft),
        }
    }

    changeButtonPadding(value, direction) {
        const normalizedDirection = direction[0].toUpperCase() + direction.substr(1).toLowerCase();
        this.changeButtonStyle("padding" + normalizedDirection, value + "px");
    }

    changeBorderWidth(value) {
        if (value == 0) this.changeButtonStyle("borderWidth", "");
        else this.changeButtonStyle("borderWidth", value + "px");
    }

    getShadowWidth() {
        const shadowWidth = this.button.style.boxShadow?.split(" ")?.pop();
        return (shadowWidth) ? parseFloat(shadowWidth) : 0;
    }

    changeShadow(value) {
        this.changeButtonStyle("boxShadow", "0px 0px " + parseFloat(value) + "px black");
    }

    /* END */

    /* Helpers */


    setActiveStyles() {
        const toolbarFontSizeContainer = this.popupToolbar.querySelector("#font-size");
        const input = toolbarFontSizeContainer.querySelector("#font-size input");

        const size = parseFloat(this.button.style.fontSize || this.buttonStyles.fontSize);
        input.value = size;

        const fontColorBtn = this.popupToolbar.querySelector("#font-color");
        const fontColorIcon = fontColorBtn.querySelector("svg");
        fontColorIcon.style.fill = this.getButtonFontColor();
    }

    /* END */
}