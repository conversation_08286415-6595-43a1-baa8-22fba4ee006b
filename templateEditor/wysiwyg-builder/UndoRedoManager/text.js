import UndoRedoManager from "./undoRedoManager";
import TextSection from "../textEditor";

// Utils
import htmlTreeOperations from "../../../generic-utils/htmlTreeOperations.js";
import RangeOperations from '../rangeOperations.js';

export default class TextUndoRedoManager extends UndoRedoManager {
    constructor(editor) {
        super(editor);
        
        this.textContainer = editor.sectionContainer;
    }

    saveForUndo(type) {
        if (!UndoRedoManager.MUTATION_TYPES[type])
            console.error("DevError: Mutation type not supported");
                
        if (this.readyToPush) this.readyToPush = false;
        else return;

        const currentHTML = this.getCleanHTML();
        const offsets = RangeOperations.getRangeOffsetsBasedOnContainer(this.iframeDocument, this.editor.wysiwygOperations.selection, this.textContainer);
        setTimeout(() => {
            this.undoStack.push({
                    type: type,
                    html: currentHTML,
                    offsets: offsets
                });
            this.readyToPush = true;
        }, 300);
    }

    saveForRedo(type) {
        if (!UndoRedoManager.MUTATION_TYPES[type])
            console.error("DevError: Mutation type not supported");
        
        this.redoStack.push({
                type: type,
                html: this.textContainer.innerHTML,
                offsets: RangeOperations.getRangeOffsetsBasedOnContainer(this.iframeDocument, this.editor.wysiwygOperations.selection, this.textContainer)
            });
    }

    performRevertAction(event, type) {
        const isUndo = type == "undo";
        const stack = isUndo ? this.undoStack : this.redoStack;

        event.preventDefault();
        if (stack.length == 0) return;
        this.mutationObserver.disconnect();

        this.iframeDocument.dispatchEvent(new Event("userpilotClosePopup"));

        const top = stack[stack.length - 1];
        isUndo ? this.saveForRedo(top.type) : this.saveForUndo(top.type);

        top.type == UndoRedoManager.MUTATION_TYPES.NATIVE ? this.iframeDocument.execCommand(type) : this.revertText(top);
        stack.pop();

        this.mutationObserver.observe(this.textContainer, UndoRedoManager.observerConfig);

        return false;
    }

    revertText(lastState) {
        this.textContainer.innerHTML = lastState.html;
        this.editor.wysiwygOperations.selection = RangeOperations.getRangeBasedOnContainerOffsets(this.iframeDocument, lastState.offsets, this.textContainer);

        this.editor.wysiwygOperations.resetSectionRange();
        this.editor.elementsToEdit = htmlTreeOperations.getSelectedBuilderBlock(this.editor.wysiwygOperations.selection, this.editor.sectionContainer);

        if (this.editor instanceof TextSection) {
            this.editor.personalization.initAttributeElementsEvents();
            this.editor.initAnchorElementsEvents();
            this.editor.findActiveStyles();
        }
    }

    getCleanHTML() {
        const temp = document.createElement("div");
        temp.innerHTML = this.textContainer.innerHTML;

        temp.querySelector("#fake-caret")?.remove();

        Array.from(temp.querySelectorAll("span[style*='background-color']")).forEach(span => {
            span.style.backgroundColor = ""
            if (!span.getAttribute("style")) htmlTreeOperations.unwrap(span);
        });

        Array.from(temp.querySelectorAll("a[style*='background-color']")).forEach(link => {
            link.style.backgroundColor = ""
        });

        Array.from(temp.querySelectorAll(".userpilot-builder-block"))
        .forEach(block =>  block.style.backgroundColor = "");

        return temp.innerHTML;
    }
}