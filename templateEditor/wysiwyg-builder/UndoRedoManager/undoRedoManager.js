import { OStypes ,getOS } from "../../../generic-utils/os.js";
import Common from "../../../index.js";

export default class UndoRedoManager {
    static MUTATION_TYPES = {
        NATIVE: "NATIVE",
        CUSTOM: "CUSTOM"
    }

    static observerConfig = {
        attributes: true,
        characterData: true,
        childList: true,
        subtree: true
    }

    constructor(editor) {
        this.editor = editor;
        this.iframeDocument = Common.shadowRoot.getElementById("tool-editor").contentDocument;
        this.textContainer = editor.sectionContainer;
        this.os = getOS();

        this.undoStack = [];
        this.redoStack = [];
        this.readyToPush = true;

        this.buttons = {
            Meta: false,
            Shift: false,
            Control: false,
            z: false,
            y: false
        }

        this.handleKeyEventRef = this.handleKeyEvent.bind(this);

        this.mutationObserver = this.initMutationObserver();
        this.initEventListeners();
    }

    initMutationObserver() {
        const observer = new MutationObserver(this.saveMutation.bind(this));

        setTimeout(() => observer.observe(this.textContainer, UndoRedoManager.observerConfig), 500);
        return observer;
    }

    // Only save typing characters mutations
    saveMutationTimeout;
    saveMutation(mutationList) {
        clearTimeout(this.saveMutationTimeout);
        this.saveMutationTimeout = setTimeout(() => {
            if (mutationList.some(mutation => mutation.type == "childList" || mutation.type == "attributes")) return;
            this.saveForUndo(UndoRedoManager.MUTATION_TYPES.NATIVE);
        }, 500);
    }

    initEventListeners() {
        this.iframeDocument.addEventListener("keydown", this.handleKeyEventRef, true);
        this.iframeDocument.addEventListener("keyup", this.handleKeyEventRef, true);
    }

    destroy() {
        this.undoStack  = [];
        this.mutationObserver.disconnect();

        this.iframeDocument.removeEventListener("keydown", this.handleKeyEventRef, true);
        this.iframeDocument.removeEventListener("keyup", this.handleKeyEventRef, true);
    }

    handleKeyEvent(event) {
        if (!Object.keys(this.buttons).includes(event.key)) return;
        if (event.type != "keydown" && event.type != "keyup") return;

        this.buttons[event.key] = (event.type == "keydown") ? true : false;
        
        const {isUndo, isRedo} = this.getOsCombinations(event);

        if (isUndo) return this.performRevertAction(event, "undo");
        if (isRedo) return this.performRevertAction(event, "redo");
    }

    getOsCombinations(event) {
        switch (this.os) {
            case OStypes.MacOS:
                return this.macCombinations(event);
            case OStypes.WINDOWS:
            case OStypes.LINUX:
                return this.windowsLinuxCombinations(event);
            default:
                console.error("DevError: No undo/redo key combination for OS ", this.os);
        }
    }

    macCombinations(event) {
        return {
            isUndo: this.buttons.Meta && !this.buttons.Shift && event.key == "z" && event.type == "keydown",
            isRedo: this.buttons.Meta && this.buttons.Shift && event.key == "z" && event.type == "keydown"
        }
    }

    windowsLinuxCombinations(event)  {
        return {
            isUndo: this.buttons.Control && !this.buttons.Shift && event.key == "z" && event.type == "keydown",
            isRedo: (this.buttons.Control && this.buttons.Shift && event.key == "z" && event.type == "keydown")
                || (this.buttons.Control && !this.buttons.Shift && event.key == "y" && event.type == "keydown")
        }
    }
}