import UndoRedoManager from "./undoRedoManager";

export default class ButtonUndoRedoManager extends UndoRedoManager {
    static MUTATION_TYPES = {
        NATIVE: "NATIVE",
        BUTTON_STYLE: "BUTTON_STYLE",
        SECTION_STYLE: "SECTION_STYLE"
    }
    
    constructor(editor) {
        super(editor);
    }

    saveForUndo(type) {
        if (!ButtonUndoRedoManager.MUTATION_TYPES[type])
            console.error("DevError: Mutation type not supported");
                
        if (this.readyToPush) this.readyToPush = false;
        else return;

        const sectionStyles = this.editor.sectionContainer.style.cssText;
        const buttonStyles = this.editor.button.style.cssText;

        setTimeout(() => {
            this.undoStack.push(
                {
                    type: type,
                    section_style: sectionStyles,
                    button_style: buttonStyles
                }
            );
            this.readyToPush = true;
        }, 300);
    }

    saveForRedo(type) {
        if (!ButtonUndoRedoManager.MUTATION_TYPES[type])
            console.error("DevError: Mutation type not supported");
        
        this.redoStack.push(
            {
                type: type,
                section_style: this.editor.sectionContainer.style.cssText,
                button_style: this.editor.button.style.cssText
            }
            
        )
    }

    performRevertAction(event, type) {
        const isUndo = type == "undo";
        const stack = isUndo ? this.undoStack : this.redoStack;

        event.preventDefault();
        if (stack.length == 0) return;
        const top = stack[stack.length - 1];

        this.mutationObserver.disconnect();
        this.iframeDocument.dispatchEvent(new Event("userpilotClosePopup"));
        
        isUndo ? this.saveForRedo(top.type) : this.saveForUndo(top.type);
        this.revertButton(top, type);
        stack.pop();

        this.mutationObserver.observe(this.editor.sectionContainer, UndoRedoManager.observerConfig);

        return false;
    }

    revertButton(lastState, type) {
        switch (lastState.type) {
            case ButtonUndoRedoManager.MUTATION_TYPES.NATIVE:
                this.iframeDocument.execCommand(type);
                break;

            case ButtonUndoRedoManager.MUTATION_TYPES.BUTTON_STYLE:
                this.editor.button.style = lastState.button_style;
                this.editor.setActiveStyles();
                break;
            
            case ButtonUndoRedoManager.MUTATION_TYPES.SECTION_STYLE:
                this.editor.sectionContainer.style = lastState.section_style;
                break;
                
            default:
                console.error("DevError: Unknown mutation type ", type);
        }
    }
}


