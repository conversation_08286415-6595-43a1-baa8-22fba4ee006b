import htmlTreeOperations from "../../generic-utils/htmlTreeOperations.js";

export default class RangeOperations {
    static rangeIntersectsNode(range, node) {
        var nodeRange;
        if (range.intersectsNode) {
            return range.intersectsNode(node);
        } else {
            nodeRange = node.ownerDocument.createRange();
            try {
                nodeRange.selectNode(node);
            } catch (e) {
                nodeRange.selectNodeContents(node);
            }

            return range.compareBoundaryPoints(Range.END_TO_START, nodeRange) == -1 &&
                range.compareBoundaryPoints(Range.START_TO_END, nodeRange) == 1;
        }
    }
    /* Get highlighted selected tags -- end */


    /* caret placement -- start */




    static setCaretPosition(element, offset) {
        /* get caret position within the text */

        // if (!element.length) {
        //     placeCaretAtEnd(element)
        //     return;
        // }
        var range = document.createRange();
        var sel = document.defaultView.getSelection();
        var currentNode = null;
        var previousNode = null;
        var t_offset = offset;
        var temp = "";
        for (var i = 0; i < element.childNodes.length; i++) {
            previousNode = currentNode;
            currentNode = element.childNodes[i];
            while (currentNode.childNodes.length > 0) {
                currentNode = currentNode.childNodes[0];
            }
            if (previousNode != null) {
                offset -= previousNode.textContent.length;
            }
            if (offset <= currentNode.length) {
                break;
            }
        }
        if (currentNode != null) {
            try {
                range.setStart(currentNode, offset);
                range.collapse(true);
                sel.removeAllRanges();
                sel.addRange(range);
            } catch (e) {}
        }
    }


    static placeCaretAtEnd(el) {
        /* position the caret at the end of the selected element */
        el.focus();
        if (typeof window.getSelection != "undefined" &&
            typeof document.createRange != "undefined") {
            var range = document.createRange();
            range.selectNodeContents(el);
            range.collapse(false);
            var sel = window.getSelection();
            sel.removeAllRanges();
            sel.addRange(range);
        } else if (typeof document.body.createTextRange != "undefined") {
            var textRange = document.body.createTextRange();
            textRange.moveToElementText(el);
            textRange.collapse(false);
            textRange.select();
        }
    }


    static replaceSelectedText(html, range) {
        /* Replaces highlighted text */
        range.deleteContents();
        range.insertNode(html);
    }

    static insertAtCaret(html) {
        /* insert html at caret position */
        var sel, range;
        if (window.getSelection) {

            sel = document.getSelection();
            if (sel.getRangeAt && sel.rangeCount) {
                range = sel.getRangeAt(0);
                range.deleteContents();

                var el = document.createElement("span");
                el.innerHTML = html;
                var frag = document.createDocumentFragment(),
                    node, lastNode;
                while ((node = el.firstChild)) {
                    lastNode = frag.appendChild(node);
                }
                range.insertNode(frag);
                if (lastNode) {
                    range = range.cloneRange();
                    range.setStartAfter(lastNode);
                    range.collapse(true);
                    sel.removeAllRanges();
                    sel.addRange(range);
                }
                return frag;
            }
        } else if (document.selection && document.selection.type != "Control") {
            document.selection.createRange().pasteHTML(html);
        }
    }


    /* caret placement -- end */
    /* get caret position from a selected element  -- start */
    static getCaretCharacterOffsetWithin(element) {
        var caretOffset = 0;
        var doc = element.ownerDocument || element.document;
        var win = doc.defaultView || doc.parentWindow;
        var sel;
        if (typeof document.defaultView.getSelection != "undefined") {
            sel = document.defaultView.getSelection();
            if (sel.rangeCount > 0) {
                try {
                    var range = document.getSelection().getRangeAt(0);
                    var preCaretRange = range.cloneRange();
                    preCaretRange.selectNodeContents(element);
                    preCaretRange.setEnd(range.endContainer, range.endOffset);
                    caretOffset = preCaretRange.toString().length;

                } catch (e) {

                }
            }
        } else if ((sel = doc.selection) && sel.type != "Control") {
            var textRange = sel.createRange();
            var preCaretTextRange = doc.body.createTextRange();
            preCaretTextRange.moveToElementText(element);
            preCaretTextRange.setEndPoint("EndToEnd", textRange);
            caretOffset = preCaretTextRange.text.length;
        }
        return caretOffset;
    }
    /* get caret position from a selected element  -- end */

    /* New Functions */

    static surroundSelection(documentObj, section, range, surroundingElement) {
        const sectionTreeWalker = documentObj.createTreeWalker(section, NodeFilter.SHOW_TEXT, {
            acceptNode: (node) => {
                return (range.intersectsNode(node));
            }
        });

        const intersectedNodes = [];
        while (sectionTreeWalker.nextNode()) {
            sectionTreeWalker.currentNode.nodeValue && intersectedNodes.push(sectionTreeWalker.currentNode);
        }

        intersectedNodes.forEach(node => {
            const tempRange = documentObj.createRange();
            // Find out the node's position in the selection range (first, mid, or last);
            if (node === range.startContainer) {
                tempRange.setStart(node, range.startOffset);
                tempRange.setEnd(node, node.length);
            } else if (node === range.endContainer) {
                tempRange.setStart(node, 0);
                tempRange.setEnd(node, range.endOffset);
            } else {
                node.parentElement.getAttribute("contenteditable") === "false"
                    ? tempRange.selectNode(node.parentElement) : tempRange.selectNodeContents(node);
            }
            node.nodeValue.trim() && tempRange.surroundContents(surroundingElement.cloneNode(true));
        });
    }

    // Default to remove 'a' tags
    static replaceSelectionWithNode(documentObj, section, range, node, tagsToRemove = []) {
        tagsToRemove.forEach(tag =>
            this.getAllNodeTypeInRange(documentObj, range, section, tag).forEach(node => node.remove()));
        range.deleteContents();
        range.insertNode(node);
    }

    static unwrapAllNodesOfTypeInRange(documentObj, section, range, tagsToRemove = []) {
        tagsToRemove.forEach(tag =>
            this.getAllNodeTypeInRange(documentObj, range, section, tag).forEach(node => htmlTreeOperations.unwrap(node)));
    }

    // Get the start and end offsets of a range in a text container
    static getRangeOffsetsBasedOnContainer(documentObj, range, textContainer) {
        const offsets = {
            start: null,
            end: null
        };
        
        const textTreeWalker = documentObj.createTreeWalker(textContainer, NodeFilter.SHOW_TEXT, {
            acceptNode: (node) => node.wholeText.trim().length ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP
        });

        let iteratedTextLength = 0;
        while (textTreeWalker.nextNode()) {
            const current = textTreeWalker.currentNode;

            if (current === range.startContainer || range.startContainer.firstChild === current) {
                offsets.start = iteratedTextLength + range.startOffset;
            }

            if (current === range.endContainer || range.endContainer.firstChild === current) {
                offsets.end = iteratedTextLength + range.endOffset;
                break;
            }

            iteratedTextLength += current.length;
        }
        if (offsets.start === null || offsets.end === null) console.warn("DevWarning: Something went wrong here, One of the offsets is null.");
        return offsets;
    }

    static adjustRangeOffsetsBasedOnCharRemoval(documentObj, offsets, textContainer, char = "\u200B", excludedNodes = []) {
        if (offsets.start === null || offsets.end === null) console.warn("DevError: offset obj must contain start and end numbers");

        const textTreeWalker = documentObj.createTreeWalker(textContainer, NodeFilter.SHOW_TEXT, {
            acceptNode: (node) => node.wholeText.trim().length ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP
        });

        let iteratedTextLength = 0;
        let removedCharsBeforeStart = 0;
        let removedCharsBeforeEnd = 0;

        let iteratedStartNode = false;
        let iteratedEndNode = false;

        while (textTreeWalker.nextNode()) {
            const current = textTreeWalker.currentNode;
            const isCurrentSingleSelection = (iteratedTextLength + current.length) === offsets.start && (iteratedTextLength + current.length) === offsets.end;
            const currentContainsStart = (iteratedTextLength + current.length) > offsets.start && !iteratedStartNode;
            const currentContainsEnd = (iteratedTextLength + current.length) >= offsets.end && !iteratedEndNode;
            const previousNode = textTreeWalker.previousNode();
            textTreeWalker.currentNode = current;

            if (isCurrentSingleSelection) {
                iteratedStartNode = true;
                iteratedEndNode = true
                iteratedTextLength += current.length;
                continue;
            }

            const isExcluded = excludedNodes.some(node => node.contains(current));
            const isPrevEditable = previousNode?.parentElement?.getAttribute("contenteditable") !== "false";
            if (isExcluded || !isPrevEditable) {
                iteratedTextLength += current.length;
                continue;
            }

            if (currentContainsStart && currentContainsEnd) {
                iteratedStartNode = true;
                iteratedEndNode = true;
                removedCharsBeforeStart += current.textContent.substring(0, offsets.start - iteratedTextLength).split(char).length - 1;
                removedCharsBeforeEnd += current.textContent.substring(offsets.start - iteratedTextLength, offsets.end).split(char).length - 1;
            }

            if (currentContainsStart && !currentContainsEnd) {
                iteratedStartNode = true;
                removedCharsBeforeStart += current.textContent.substring(0, offsets.start - iteratedTextLength).split(char).length - 1;
                removedCharsBeforeEnd += current.textContent.substring(offsets.start - iteratedTextLength, current.length).split(char).length - 1;
                iteratedTextLength += current.length;
                current.textContent = current.textContent.replaceAll("\u200B", "");
                continue;
            } else if (!iteratedStartNode) {
                removedCharsBeforeStart += current.textContent.split(char).length - 1;
                removedCharsBeforeEnd += current.textContent.split(char).length - 1;
            }

            if (currentContainsEnd && !currentContainsStart) {
                iteratedEndNode = true;
                removedCharsBeforeEnd += current.textContent.substring(0, offsets.end - iteratedTextLength).split(char).length - 1;
            } else if (!iteratedEndNode && iteratedStartNode) {
                removedCharsBeforeEnd += current.textContent.split(char).length - 1;
            }

            iteratedTextLength += current.length;
            current.textContent = current.textContent.replaceAll("\u200B", "");
        }

        return { start: offsets.start - removedCharsBeforeStart, end: offsets.end - removedCharsBeforeEnd };
    }

    static getRangeBasedOnContainerOffsets(documentObj, offsets, textContainer) {
        const tempRange = documentObj.createRange();
        const isStartSet = () => { return tempRange.startContainer !== documentObj }

        const textTreeWalker = documentObj.createTreeWalker(textContainer, NodeFilter.SHOW_TEXT, {
            acceptNode: (node) => node.wholeText.trim().length ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP
        });

        let iteratedTextLength = 0;
        while (textTreeWalker.nextNode()) {
            const current = textTreeWalker.currentNode;

            if ((iteratedTextLength + current.length) > offsets.start && !isStartSet()) {
                tempRange.setStart(current, offsets.start - iteratedTextLength);
            }

            if ((iteratedTextLength + current.length) >= offsets.end) {
                tempRange.setEnd(current, offsets.end - iteratedTextLength);
                if(!isStartSet()) tempRange.setStart(current, offsets.start - iteratedTextLength);
                break;
            }

            iteratedTextLength += current.length;
        }
        return tempRange.cloneRange();
    }

    static getAllNodeTypeInRange(documentObj, range, section, nodeType) {
        const nodes = [];
        const treeWalker = documentObj.createTreeWalker(section, NodeFilter.SHOW_ELEMENT, {
            acceptNode: (node) => {
                return (range.intersectsNode(node))
            }
        });

        while (treeWalker.nextNode())
            if (treeWalker.currentNode.tagName == nodeType.toUpperCase()) nodes.push(treeWalker.currentNode);
        return nodes;
    }

    static getAllNodesOfType(documentObj, section, nodeType) {
        const nodes = [];
        const treeWalker = documentObj.createTreeWalker(section, NodeFilter.SHOW_ELEMENT, {
            acceptNode: (node) => node.tagName === nodeType.toUpperCase()
        });

        while (treeWalker.nextNode()) nodes.push(treeWalker.currentNode);
        return nodes;
    }

    static getLastNodeOfType(documentObj, section, nodeType) {
        return this.getAllNodesOfType(documentObj, section, nodeType).slice(-1)[0];
    }

    static getAllTextNodes(documentObj, section) {
        const nodes = [];
        const treeWalker = documentObj.createTreeWalker(section, NodeFilter.SHOW_TEXT);

        while (treeWalker.nextNode()) if(treeWalker.currentNode.textContent.trim()) nodes.push(treeWalker.currentNode); 

        return nodes;
    }

    static getFirstTextNode(documentObj, section) {
        return this.getAllTextNodes(documentObj, section)[0];
    }

    static getLastTextNode(documentObj, section) {
        return this.getAllTextNodes(documentObj, section).slice(-1)[0];
    }

    static getFirstNodeTypeInRange(documentObj, range, section, nodeType) {
        const treeWalker = documentObj.createTreeWalker(section, NodeFilter.SHOW_ELEMENT, {
            acceptNode: (node) => {
                return (range.intersectsNode(node))
            }
        });

        while (treeWalker.nextNode())
            if (treeWalker.currentNode.tagName == nodeType.toUpperCase()) return treeWalker.currentNode;
        return null;
    }

    static isRangeSingleCaret(range) {
        return (range.startContainer === range.endContainer && range.endContainer === range.commonAncestorContainer && range.startOffset === range.endOffset);
    }

    static insertNodeAndCollapse(documentObj, node, range, toStart = false) {
        if (!node.textContent.trim().length) console.warn("DevError: You cannot collapse on a node with no textnodes");

        const targetTextnode = toStart ? this.getFirstTextNode(documentObj, node) : this.getLastTextNode(documentObj, node);
        range.insertNode(node);
        range.selectNodeContents(targetTextnode);
        range.collapse(toStart);
    }

    static setTextCaretAfterNode(range, node) {
        const textNode = document.createTextNode("\u200B");
        htmlTreeOperations.insertAfter(node, textNode);
        range.setStart(textNode, 1);
        range.setEnd(textNode, 1);
    }

    static highlightRange(range) {
        const highlightSpan = document.createElement("span");
        highlightSpan.style.backgroundColor = "#ACCEF7";
        range.surroundContents(highlightSpan);
        range.selectNodeContents(highlightSpan.firstChild);
    }

    static highlightElements(elements = []) {
        const highlightSpan = document.createElement("span");
        highlightSpan.style.backgroundColor = "#ACCEF7";
        
        elements.forEach(element => {
            htmlTreeOperations.wrapNode(element, highlightSpan.cloneNode(true));
        });
        return elements.map(element => element.parentElement);
    }

    static placeCaretAfterNode(rootElement, node){
        const selection = rootElement.getSelection();

        if(!selection.rangeCount) return;

        const range = selection.getRangeAt(0);
        range.setStartAfter(node);
        range.setEndAfter(node);
    }

    static isMultipleBlocks(range) {
        let blocksCount = 0;
        const rangeContent = range.cloneContents();
        const endContainerIsText = range.endContainer.nodeName === "#text";
        const validElementsLength = endContainerIsText ? rangeContent.childNodes.length : (rangeContent.childNodes.length -1);

        for(let nodeIndex = 0; nodeIndex < validElementsLength; nodeIndex++) {

            const isContentBlock = rangeContent.childNodes[nodeIndex] &&
            rangeContent.childNodes[nodeIndex].classList && 
            rangeContent.childNodes[nodeIndex].classList.contains('userpilot-builder-block');

            if(isContentBlock){
                blocksCount++;
            }

            if(blocksCount > 1) break;
        }

        return blocksCount > 1 ? true: false;
    }
}