import tippy from 'tippy.js';
import Views from './wysiwigViews.js';
import HeaderSection from './headerEditor.js';
import TextSection from './textEditor.js';
import ButtonSection from './buttonEditor.js';
import ImageSection from './imageEditor.js';
import HTMLSection from './htmlEditor.js';
import EmojiSection from './emojiEditor.js';
import ButtonSpotlightSection from './buttonSpotlightEditor.js';
import { SingleInputSection, LargeInputSection, RadioInputSection, LikertScaleSection } from './formEditor.js';
import { Alert } from "../../generic-components/alert.js";
import EmbedSection from './embedEditor.js';

// Store
import Actions from "../../store/constants/actions.js";
import { Store } from "../../store/store.js";
import { generateSectionIds } from '../../userpilot-utils/userpilotHtml.js';
import { spotlightTypes } from '../../store/constants/experiences/spotlightTypes.js';
import { experienceTypes } from '../../store/constants/experiences/constants.js';
import App from '../../components/app.js';
import SectionAdditionManager from '../sectionManager/sectionAddition.js';
import Common from '../../index.js';

export default class WysiwigBuilder {

    static getSectionType(section) {
        const elementClasses = section.classList;
        if (elementClasses.contains("userpilot-button")) {
            return "userpilot-node-button";
        } else
        if (elementClasses.contains("userpilot-header-section")) {
            return "userpilot-header-section";
        } else if (elementClasses.contains("userpilot-text-section")) {
            return "userpilot-text-section";
        } else if (elementClasses.contains("userpilot-image-section")) {
            return "userpilot-image-section";
        } else if (elementClasses.contains("userpilot-button-section")) {
            return "userpilot-button-section";
        } else if (elementClasses.contains("userpilot-html-section") || elementClasses.contains("userpilot-video-section")) {
            return "userpilot-html-section";
        } else if (elementClasses.contains("userpilot-emoji-section")) {
            return "userpilot-emoji-section";
        } else if (elementClasses.contains("userpilot-input-radio")) {
            return "userpilot-input-radio";
        } else if (elementClasses.contains("userpilot-input-text")) {
            return "userpilot-input-text";
        } else if (elementClasses.contains("userpilot-input-text-large")) {
            return "userpilot-input-text-large";
        } else if (elementClasses.contains("userpilot-input-likert-scale")) {
            return "userpilot-input-likert-scale";
        } else if (elementClasses.contains("userpilot-embed-section")) {
            return "userpilot-embed-section";
        }
    }

    constructor(templateEditor) {
        this.templateEditor = templateEditor;

        this.iframeDocument = templateEditor.iframeDocument;
        this.iframeWindow = templateEditor.iframeWindow;
        this.userpilotContainer = templateEditor.iframeContainer;
        this.userpilotContent = templateEditor.userpilotContent;
        this.userpilotSlide = templateEditor.userpilotSlide;
        this.sections = templateEditor.sections;

        this.wysiwygToolbar = this.getWysiwygToolbar();

        this.activeBuilder = null;
        this.currentSection = null;
        this.oldHtml = null;
        this.editorActive = false;
        this.sectionsMap = new Map();

        this.attachWindowListeners();

        this.sections.forEach(section => {
            this.attachSectionEvents(section);
        });
        this.initSelectionListener();

        this.observer = new MutationObserver(() => {
            setTimeout(() => {
                this.respositionToolbar();
            });
        });
    }

    initSelectionListener() {
        this.userpilotContainer.addEventListener("mouseup", (event) => {
            if (!this.activeBuilder) return;
            if(!this.activeBuilder.wysiwygOperations) return;

            if (!this.activeBuilder.sectionContainer.contains(event.target) && !this.wysiwygToolbar.contains(event.target)) {
                this.activeBuilder.wysiwygOperations.selection.collapse(false);
                this.activeBuilder.wysiwygOperations.resetSectionRange();
            }
        });
    }

    attachSectionEvents(section) {
        if (!this.sectionsMap.has(section)) {
            const builder = this.getSectionEditor(section);
            this.sectionsMap.set(section, builder);
            section.addEventListener('click', this.spawnWorker.bind(this));
        } else {
            console.error("DevError: this function should be called on initialising the template builder or on new added sections");
        }
    }

    getSectionEditor(section) {
        const sectionType = WysiwigBuilder.getSectionType(section);
        switch (sectionType) {
            case 'userpilot-header-section':
                return new HeaderSection(section, this.wysiwygToolbar);
            case 'userpilot-text-section':
                return new TextSection(section, this.wysiwygToolbar);
            case 'userpilot-button-section':
                return new ButtonSection(section, this.wysiwygToolbar, this.templateEditor);
            case 'userpilot-html-section' || 'userpilot-video-section':
                return new HTMLSection(section, this.wysiwygToolbar);
            case 'userpilot-image-section':
                return new ImageSection(section, this.wysiwygToolbar);
            case 'userpilot-emoji-section':
                return new EmojiSection(section, this.wysiwygToolbar);
            case 'userpilot-input-text':
                return new SingleInputSection(section, this.wysiwygToolbar);
            case 'userpilot-input-text-large':
                return new LargeInputSection(section, this.wysiwygToolbar);
            case 'userpilot-input-radio':
                return new RadioInputSection(section, this.wysiwygToolbar);
            case 'userpilot-input-likert-scale':
                return new LikertScaleSection(section, this.wysiwygToolbar);
            case 'userpilot-embed-section':
                return new EmbedSection(section, this.wysiwygToolbar);
            case 'userpilot-node-button':
                return new ButtonSpotlightSection(section, this.wysiwygToolbar, this.templateEditor);
            default:
                console.error("DevError: " + sectionType + " is an unsupported type of section");
        }
    }

    getWysiwygToolbar() {
        const wysiwygToolbar = Views.getViewElement("wysiwyg-toolbar");
        this.sectionToolbar = wysiwygToolbar.querySelector("#section-toolbar");

        const controllerToolbar = wysiwygToolbar.querySelector("#controller-toolbar");
        this.initControllerToolbarEvents(controllerToolbar);

        tippy(wysiwygToolbar.querySelectorAll("[label-on-hover]"), { appendTo: this.userpilotContainer, zIndex: 1000, onTrigger: () => {} });

        return wysiwygToolbar;
    }

    initControllerToolbarEvents(toolbar) {
        const saveSectionBtn = toolbar.querySelector("#save-section");
        saveSectionBtn.addEventListener("click", this.applyChanges.bind(this));

        const discardChangesBtn = toolbar.querySelector("#discard-section");
        discardChangesBtn.addEventListener("click", this.discardChanges.bind(this));

        const removeSectionBtn = toolbar.querySelector("#remove-section");
        removeSectionBtn.addEventListener("click", this.removeSection.bind(this, this.currentSection));

        const cloneSectionBtn = toolbar.querySelector("#clone-section");

        if (this.templateEditor.stepType === experienceTypes.BANNER_v2) {
            cloneSectionBtn.remove();
        } else {
            cloneSectionBtn.addEventListener("click", this.cloneSection.bind(this));
        }
    }

    attachWindowListeners() {
        // Register the appropriate event listeners to the section builder elements
        this.userpilotContainer.addEventListener('mousewheel', this.respositionToolbar.bind(this));
    }

    spawnWorker(event) {
        if (this.editorActive) return;

        Store.publishAction(Actions.HIDE_WINDOW_COMPONENTS);
        Store.publishAction(Actions.LOCK_BAR, { hide: true });
        this.editorActive = true;
        this.userpilotContainer.classList.add("editor-active");

        this.currentSection = event.currentTarget;
        this.currentSection.classList.add("under-edit");
        this.userpilotContent.classList.add("under-edit");
        this.oldHtml = this.currentSection.cloneNode(true);
        this.activeBuilder = this.sectionsMap.get(this.currentSection);

        if (this.activeBuilder == null) {
            console.error("DevError: Section" + this.currentSection + " must have a worker/editor associated with it \
            /n make sure the the sections map was populated correctly");
        }

        this.sectionToolbar.innerHTML = this.getSectionToolbar(this.currentSection);
        this.userpilotContent.appendChild(this.wysiwygToolbar);

        tippy(this.sectionToolbar.querySelectorAll("[label-on-hover]"), { appendTo: this.userpilotContainer, zIndex: 1000, onTrigger: () => {} });

        this.activeBuilder.init();
        this.positionToolbar();

        ["userpilot-next-button", "userpilot-back-button"].includes(this.currentSection.id)
            ? this.wysiwygToolbar.classList.add("progression-section")
            : this.wysiwygToolbar.classList.remove("progression-section");

        const config = {
            attributes: true,
            childList: true,
            characterData: true,
            subtree: true
        };
        this.observer.observe(this.currentSection, config);
    }

    positionToolbar() {
        // Vertical positioning
        this.wysiwygToolbar.style.top = (this.currentSection.offsetTop + this.currentSection.offsetHeight + 20) + "px";
        if (this.templateEditor.templateSettings.type == experienceTypes.BANNER_v2) {
            this.wysiwygToolbar.style.left = this.currentSection.offsetLeft + "px";
        } else{
            this.wysiwygToolbar.style.left = "0px";
        }
        this.wysiwygToolbar.style.right = "unset";
        this.respositionToolbar();
    }

    respositionToolbar() {
        if (!this.editorActive) return;
        const coords = this.wysiwygToolbar.getBoundingClientRect();
        const sectionCoords = this.currentSection.getBoundingClientRect();
        const windowHeight = this.iframeWindow.innerHeight || this.iframeDocument.documentElement.clientHeight;

        if (sectionCoords.bottom + this.wysiwygToolbar.offsetHeight + (windowHeight / 3.5) <= windowHeight)
            this.wysiwygToolbar.style.top = ((this.currentSection.offsetTop - this.templateEditor.userpilotSlideContainer.scrollTop) + this.currentSection.offsetHeight + 20) + "px";
        else
            this.wysiwygToolbar.style.top = ((this.currentSection.offsetTop - this.templateEditor.userpilotSlideContainer.scrollTop) - this.wysiwygToolbar.offsetHeight - 10) + "px";

        if (coords.right >= (this.iframeWindow.innerWidth || this.iframeDocument.documentElement.clientWidth)) {
            this.wysiwygToolbar.style.left = "unset";
            this.wysiwygToolbar.style.right = "0px";
        }

        if(parseInt(this.wysiwygToolbar.style.top) < 0) {
            switch(this.templateEditor.stepType) {
                case spotlightTypes.BANNER:
                    this.wysiwygToolbar.style.top = `${this.calculateHeightUntilSectionEnd(this.currentSection) + 25}px`;
                    break;
                default:
                    break;
            }
        }
    };

    calculateHeightUntilSectionEnd = (currentElement) => {
        let height = currentElement.clientHeight;
        let element = currentElement.classList.contains("userpilot-col-section") ? currentElement.parentElement.previousElementSibling : currentElement.previousElementSibling;

        while(element) {
            height += element.clientHeight;
            element = element.previousElementSibling;
        }

        return height;
    }

    getSectionToolbar(section) {
        const type = WysiwigBuilder.getSectionType(section);
        switch (type) {
            case 'userpilot-header-section':
                return Views.getView('header-section-toolbar');
            case 'userpilot-text-section':
                return Views.getView('textEditor');
            case 'userpilot-button-section':
                return Views.getView('buttonEditor');
            case 'userpilot-html-section' || 'userpilot-video-section':
                return Views.getView('htmlEditor');
            case 'userpilot-image-section':
                return Views.getView('imageEditor');
            case 'userpilot-emoji-section':
                return Views.getView('emojiEditor')
            case 'userpilot-input-text':
            case 'userpilot-input-text-large':
            case 'userpilot-input-radio':
            case 'userpilot-input-likert-scale':
                return Views.getView('inputEditor');
            case 'userpilot-embed-section':
                return Views.getView("embedEditor");
            case 'userpilot-node-button':
                return Views.getView("nodeButton");
            default:
                console.error("DevError: Could not fetch a view for section " + section);
        }
    }

    disconnect() {
        this.observer.disconnect();
        this.wysiwygToolbar.remove();
        this.wysiwygToolbar.classList.remove("no-image");
        this.wysiwygToolbar.classList.remove("no-embed");

        this.userpilotContainer.classList.remove("editor-active");
        this.currentSection?.classList?.remove("under-edit");
        this.userpilotContent.classList.remove("under-edit");
        this.activeBuilder = null;

        Store.publishAction(Actions.UNLOCK_BAR, { hide: true });
        Store.publishAction(Actions.SHOW_WINDOW_COMPONENTS);

        // Because observer disconnect is not async
        setTimeout(() => {
            this.editorActive = false;
        }, 100);
    }

    restoreAttributes(setAttributesTo, setAttributesFrom) {
        Object.keys(setAttributesFrom.attributes).forEach(key => {
            setAttributesTo.setAttribute(setAttributesFrom.attributes[key].name, setAttributesFrom.attributes[key].value)
        });
    }

    discardChanges() {
        this.activeBuilder.save('discard');
        this.currentSection.innerHTML = this.oldHtml.innerHTML;
        this.restoreAttributes(this.currentSection, this.oldHtml);
        this.disconnect();
    }

    cloneSection() {
        this.applyChanges();
        //show error message when try to add new section for row of 3 sections
        if(this.currentSection.parentElement.classList.contains("userpilot-cols-section")){
            const rowSectionsCount = this.currentSection.parentElement.children.length;
            if(rowSectionsCount >=3) {
                Alert({
                    severity: "error",
                    content: "No more than three sections can be aligned"
                });
                return;
            }
        }
        const div = document.createElement("div");
        div.innerHTML = generateSectionIds(this.templateEditor.stepSettings.action_id, this.currentSection.outerHTML);
        const clone = div.firstElementChild;
        this.currentSection.parentNode.insertBefore(clone, this.currentSection.nextSibling);

        const currentSectionId = this.currentSection.getAttribute("unit_id");
        const appState = Store.getComponentState(App);
        const localizationData = appState.experience.localizationData;
        localizationData.forEach(localizationItem => {
            if(localizationItem.isDefault) return;

            const sectionCloned = clone.cloneNode(true);
            const localizedStep = localizationItem.steps[this.templateEditor.stepSettings.action_id];
            const stepContent = document.createElement("div");
            stepContent.innerHTML = localizedStep.data;

            const localizedCurrentSection = stepContent.querySelector(`[unit_id="${currentSectionId}"]`);
            localizedCurrentSection?.parentNode?.insertBefore(sectionCloned, localizedCurrentSection.nextSibling);

            localizedStep.data = stepContent.innerHTML;
        });


        this.templateEditor.setupNewSectionEvents(clone);
        this.templateEditor.saveStepContent(localizationData);

        [spotlightTypes.BANNER, experienceTypes.BANNER_v2].includes(this.templateEditor.templateSettings.type) && this.templateEditor.templateSettings.reInitBannerPlaceholder();
    }

    removeSection(section) {
        section = section || this.currentSection;

        const appState = Store.getComponentState(App);
        const localizationData = (appState.experience.localizationData);

        const removedSectionId = section.getAttribute("unit_id");
        const removedSectionIdIndex = this.templateEditor.stepLocalizedContent.editedSections.findIndex(editedSectionId => editedSectionId === removedSectionId);
        if(removedSectionIdIndex !== -1) this.templateEditor.stepLocalizedContent.editedSections.splice(removedSectionIdIndex, 1);

        section.remove();
        this.templateEditor.sections = this.templateEditor.sections.filter(element => element !== section);
        this.removeSectionFromTranslations(localizationData, removedSectionId);

        this.disconnect();
        this.templateEditor.saveStepContent(localizationData);

        [spotlightTypes.BANNER, experienceTypes.BANNER_v2].includes(this.templateEditor.templateSettings.type) && this.templateEditor.templateSettings.reInitBannerPlaceholder();
    }

    removeSectionFromTranslations(localizationData, removedSectionId) {
        localizationData.forEach(localizationItem => {
            if(localizationItem.isDefault) return;

            const localizedStep = localizationItem.steps[this.templateEditor.stepSettings.action_id];
            const stepContent = document.createElement("div");
            stepContent.innerHTML = localizedStep.data;

            const localizedSection = stepContent.querySelector(`[unit_id="${removedSectionId}"]`);
            localizedSection?.remove();
            SectionAdditionManager.readjustColumnSections(stepContent);

            localizedStep.data = stepContent.querySelector(".userpilot-builder")?.innerHTML.trim() ? stepContent.innerHTML : "";
        });
    }

    applyChanges() {
        const changesValidator = typeof this.activeBuilder.validateChanges === 'function';
        if((changesValidator && this.activeBuilder.validateChanges()) || !changesValidator){
            this.activeBuilder.save('apply');
            this.activeBuilder.saveSelectedUnsplashPhoto();
            this.disconnect();

            const sectionId = this.currentSection.getAttribute("unit_id");
            this.templateEditor.stepLocalizedContent.editedSections.push(sectionId);

            const localizationData = this.templateEditor.updateHideOnMobileAttribute(sectionId);
            this.templateEditor.saveStepContent(localizationData);

            [spotlightTypes.BANNER, experienceTypes.BANNER_v2].includes(this.templateEditor.templateSettings.type) && this.templateEditor.templateSettings.reInitBannerPlaceholder();

            const stepSectionsSidebarNode = Common.shadowRoot.querySelector("#userpilot-step-sections-sidebar");
            const event = new CustomEvent("stepSectionsChange");
            stepSectionsSidebarNode.dispatchEvent(event);
        }
    }
}