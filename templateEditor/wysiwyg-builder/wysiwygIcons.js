export default class WysiwygIcons {
    /* Bottom Bar (Sections Controller) */

    static getApplyIcon() {
        return '<svg width="9" height="8" viewBox="0 0 9 8" fill="none" xmlns="http://www.w3.org/2000/svg">\
        <path d="M2.69507 5.94571L0.703556 3.9542L0.0253906 4.62759L2.69507 7.29727L8.42605 1.56629L7.75266 0.892899L2.69507 5.94571Z" fill="white"/>\
        </svg>'
    }

    static getDiscardIcon() {
        return '<svg width="10" height="9" viewBox="0 0 10 9" fill="#A3B0B8" xmlns="http://www.w3.org/2000/svg">\
        <path d="M9.40383 1.05621L8.54626 0.198639L5.14637 3.59852L1.74649 0.198639L0.888916 1.05621L4.2888 4.4561L0.888916 7.85598L1.74649 8.71355L5.14637 5.31367L8.54626 8.71355L9.40383 7.85598L6.00395 4.4561L9.40383 1.05621Z"/>\
        </svg>'
    }

    static getCloneIcon() {
        return '<svg width="10" height="12" viewBox="0 0 10 12" fill="#A3B0B8" xmlns="http://www.w3.org/2000/svg">\
        <path fill-rule="evenodd" clip-rule="evenodd" d="M7.29771 0.84169H1.56674C1.0414 0.84169 0.611572 1.27151 0.611572 1.79685V8.48299H1.56674V1.79685H7.29771V0.84169ZM8.73046 2.75202H3.47706C2.95172 2.75202 2.5219 3.18184 2.5219 3.70718V10.3933C2.5219 10.9187 2.95172 11.3485 3.47706 11.3485H8.73046C9.2558 11.3485 9.68562 10.9187 9.68562 10.3933V3.70718C9.68562 3.18184 9.2558 2.75202 8.73046 2.75202ZM3.47706 10.3933H8.73046V3.70718H3.47706V10.3933Z"/>\
        </svg>'
    }

    /* Upper Bar (Sections Editor) */
    static getHeaderSizeIcon() {
        return '<svg width="17" height="12" viewBox="0 0 17 12"  xmlns="http://www.w3.org/2000/svg" fill="#A8A8AC">\
        <path d="M7.375 0.583328H9.625V11.4167H7.375V7.08333H2.875V11.4167H0.625V0.583328H2.875V4.91666H7.375V0.583328ZM14.7663 0.583328C14.0987 1.61188 13.0744 2.37949 11.875 2.74999V3.83333H14.125V11.4167H16.375V0.583328H14.7663Z"/>\
        </svg>';
    }

    static getFontColorIcon() {
        return `<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="14" height="14" rx="1" fill="inherit"/>
        </svg>`;
    }

    static getBackgroundIcon() {
        return '<svg width="19" height="19" viewBox="0 0 19 19"  xmlns="http://www.w3.org/2000/svg" fill="#3D4A52">\
        <path fill-rule="evenodd" clip-rule="evenodd" d="M16.6955 0.605545H2.58073C1.47171 0.605545 0.564331 1.51293 0.564331 2.62195V16.7368C0.564331 17.8458 1.47171 18.7532 2.58073 18.7532H16.6955C17.8046 18.7532 18.712 17.8458 18.712 16.7368V2.62195C18.712 1.51293 17.8046 0.605545 16.6955 0.605545ZM16.6955 2.62195V16.7368H2.58073V2.62195H16.6955ZM8.77109 13.4399L11.7957 9.53821L15.6873 14.7204H3.58893L6.61354 10.8287L8.77109 13.4399Z"/>\
        </svg>';
    }

    static getDimensionIcon() {
        return `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M3.50048 1.18639C3.749 1.4349 3.749 1.83783 3.50048 2.08634L3.17268 2.41414H7.87782L7.55002 2.08634C7.30151 1.83783 7.30151 1.4349 7.55002 1.18639C7.79854 0.937871 8.20146 0.937871 8.44998 1.18639L9.86412 2.60053C10.1126 2.84904 10.1126 3.25197 9.86412 3.50048L8.44998 4.91462C8.20146 5.16314 7.79854 5.16314 7.55002 4.91462C7.30151 4.66611 7.30151 4.26318 7.55002 4.01467L7.87782 3.68687H3.17268L3.50048 4.01467C3.749 4.26318 3.749 4.66611 3.50048 4.91462C3.25197 5.16314 2.84904 5.16314 2.60053 4.91462L1.18639 3.50048C0.937871 3.25197 0.937871 2.84904 1.18639 2.60053L2.60053 1.18639C2.84904 0.937871 3.25197 0.937871 3.50048 1.18639ZM3.05051 7.22222C2.84423 7.22222 2.64639 7.30417 2.50053 7.45003C2.35467 7.59589 2.27273 7.79372 2.27273 8V12.9495C2.27273 13.1558 2.35467 13.3536 2.50053 13.4995C2.64639 13.6453 2.84423 13.7273 3.05051 13.7273H8C8.20628 13.7273 8.40411 13.6453 8.54997 13.4995C8.69583 13.3536 8.77778 13.1558 8.77778 12.9495V8C8.77778 7.79372 8.69583 7.59589 8.54997 7.45003C8.40411 7.30417 8.20628 7.22222 8 7.22222H3.05051ZM1.60058 6.55007C1.98512 6.16553 2.50668 5.94949 3.05051 5.94949H8C8.54383 5.94949 9.06538 6.16553 9.44993 6.55007C9.83447 6.93462 10.0505 7.45617 10.0505 8V12.9495C10.0505 13.4933 9.83447 14.0149 9.44993 14.3994C9.06538 14.784 8.54383 15 8 15H3.05051C2.50668 15 1.98512 14.784 1.60058 14.3994C1.21603 14.0149 1 13.4933 1 12.9495V8C1 7.45617 1.21603 6.93462 1.60058 6.55007ZM12.4995 6.13588C12.748 5.88737 13.151 5.88737 13.3995 6.13588L14.8136 7.55002C15.0621 7.79854 15.0621 8.20146 14.8136 8.44998C14.5651 8.69849 14.1622 8.69849 13.9137 8.44998L13.5859 8.12218V12.8273L13.9137 12.4995C14.1622 12.251 14.5651 12.251 14.8136 12.4995C15.0621 12.748 15.0621 13.151 14.8136 13.3995L13.3995 14.8136C13.151 15.0621 12.748 15.0621 12.4995 14.8136L11.0854 13.3995C10.8369 13.151 10.8369 12.748 11.0854 12.4995C11.3339 12.251 11.7368 12.251 11.9853 12.4995L12.3131 12.8273V8.12218L11.9853 8.44998C11.7368 8.69849 11.3339 8.69849 11.0854 8.44998C10.8369 8.20146 10.8369 7.79854 11.0854 7.55002L12.4995 6.13588Z" fill="#A8A8AC"/>
        </svg>`;
    }

    static getTypograpghyIcon() {
        return `<svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M15.3654 6.36364H10.25C10.176 6.36364 10.1154 6.425 10.1154 6.5V8.40909C10.1154 8.48409 10.176 8.54545 10.25 8.54545H11.0577C11.1317 8.54545 11.1923 8.48409 11.1923 8.40909V7.45455H12.2019V12.9091H11.4279C11.3538 12.9091 11.2933 12.9705 11.2933 13.0455V13.8636C11.2933 13.9386 11.3538 14 11.4279 14H14.1875C14.2615 14 14.3221 13.9386 14.3221 13.8636V13.0455C14.3221 12.9705 14.2615 12.9091 14.1875 12.9091H13.4135V7.45455H14.4231V8.40909C14.4231 8.48409 14.4837 8.54545 14.5577 8.54545H15.3654C15.4394 8.54545 15.5 8.48409 15.5 8.40909V6.5C15.5 6.425 15.4394 6.36364 15.3654 6.36364ZM10.9231 4.31818V2.13636C10.9231 2.06136 10.8625 2 10.7885 2H1.63462C1.56058 2 1.5 2.06136 1.5 2.13636V4.31818C1.5 4.39318 1.56058 4.45455 1.63462 4.45455H2.57692C2.65096 4.45455 2.71154 4.39318 2.71154 4.31818V3.22727H5.53846V12.7727H3.99038C3.91635 12.7727 3.85577 12.8341 3.85577 12.9091V13.8636C3.85577 13.9386 3.91635 14 3.99038 14H8.43269C8.50673 14 8.56731 13.9386 8.56731 13.8636V12.9091C8.56731 12.8341 8.50673 12.7727 8.43269 12.7727H6.88462V3.22727H9.71154V4.31818C9.71154 4.39318 9.77212 4.45455 9.84615 4.45455H10.7885C10.8625 4.45455 10.9231 4.39318 10.9231 4.31818Z" fill="#A8A8AC"/>
        </svg>`;
    }

    static getBoldIcon() {
        return `<svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9.35611 1C10.2139 0.999892 11.0519 1.23341 11.7618 1.67035C12.4718 2.10729 13.0208 2.72752 13.338 3.4508C13.6552 4.17408 13.7259 4.9671 13.541 5.72726C13.3561 6.48741 12.9241 7.17971 12.3008 7.71456C13.1159 8.12677 13.7604 8.76918 14.1365 9.54418C14.5126 10.3192 14.5997 11.1844 14.3845 12.0084C14.1693 12.8324 13.6637 13.5701 12.9443 14.1095C12.225 14.6489 11.3313 14.9605 10.3991 14.9969L10.2131 15H3.44272C3.21042 15.0001 2.98625 14.9224 2.81322 14.7817C2.64019 14.641 2.53048 14.4473 2.50514 14.2378L2.5 14.1444V1.85556C2.49988 1.64473 2.58554 1.44129 2.74053 1.28426C2.89553 1.12723 3.10896 1.02767 3.33987 1.00467L3.44272 1H9.35611ZM10.2131 8.77778H4.21403V13.4444H10.2131C10.895 13.4444 11.549 13.1986 12.0311 12.761C12.5133 12.3234 12.7842 11.7299 12.7842 11.1111C12.7842 10.4923 12.5133 9.89878 12.0311 9.46119C11.549 9.02361 10.895 8.77778 10.2131 8.77778ZM9.35611 2.55556H4.21403V7.22222H9.35611C10.038 7.22222 10.692 6.97639 11.1741 6.53881C11.6563 6.10122 11.9272 5.50773 11.9272 4.88889C11.9272 4.27005 11.6563 3.67656 11.1741 3.23897C10.692 2.80139 10.038 2.55556 9.35611 2.55556Z" fill="#A8A8AC"/>
        </svg>`;
    }

    static getItalicIcon() {
        return `<svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M10.7327 1.00021H7C6.58579 1.00021 6.25 1.34842 6.25 1.77797C6.25 2.20751 6.58579 2.55572 7 2.55572H9.66775L5.73025 13.4443H3.25C2.83579 13.4443 2.5 13.7925 2.5 14.222C2.5 14.6516 2.83579 14.9998 3.25 14.9998H6.2325C6.24412 15.0001 6.25571 15.0001 6.26728 14.9998H10C10.4142 14.9998 10.75 14.6516 10.75 14.222C10.75 13.7925 10.4142 13.4443 10 13.4443H7.33225L11.2697 2.55572H13.75C14.1642 2.55572 14.5 2.20751 14.5 1.77797C14.5 1.34842 14.1642 1.00021 13.75 1.00021H10.7675C10.7559 0.999929 10.7443 0.99993 10.7327 1.00021Z" fill="#A8A8AC"/>
        </svg>`;
    }

    static getUnderlineIcon() {
        return `<svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M4.125 1C4.60825 1 5 1.39175 5 1.875V6.25C5 7.17826 5.36875 8.0685 6.02513 8.72487C6.6815 9.38125 7.57174 9.75 8.5 9.75C9.42826 9.75 10.3185 9.38125 10.9749 8.72487C11.6313 8.0685 12 7.17826 12 6.25V1.875C12 1.39175 12.3918 1 12.875 1C13.3582 1 13.75 1.39175 13.75 1.875V6.25C13.75 7.64239 13.1969 8.97774 12.2123 9.96231C11.2277 10.9469 9.89239 11.5 8.5 11.5C7.10761 11.5 5.77226 10.9469 4.78769 9.96231C3.80312 8.97774 3.25 7.64239 3.25 6.25V1.875C3.25 1.39175 3.64175 1 4.125 1ZM1.5 14.125C1.5 13.6418 1.89175 13.25 2.375 13.25H14.625C15.1082 13.25 15.5 13.6418 15.5 14.125C15.5 14.6082 15.1082 15 14.625 15H2.375C1.89175 15 1.5 14.6082 1.5 14.125Z" fill="#A8A8AC"/>
        </svg>`;
    }

    static getAlignmentIcon() {
        return `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9.55556 12.5882C9.7538 12.5884 9.94447 12.6573 10.0886 12.7808C10.2328 12.9043 10.3195 13.0731 10.3311 13.2528C10.3428 13.4324 10.2784 13.6092 10.1511 13.7472C10.0239 13.8851 9.84341 13.9738 9.64656 13.9951L9.55556 14H1.77778C1.57954 13.9998 1.38886 13.9309 1.24471 13.8074C1.10056 13.6839 1.01382 13.5151 1.0022 13.3355C0.990582 13.1559 1.05497 12.979 1.18221 12.8411C1.30944 12.7031 1.48993 12.6144 1.68678 12.5932L1.77778 12.5882H9.55556ZM14.2222 9.05882C14.4285 9.05882 14.6263 9.13319 14.7722 9.26557C14.9181 9.39795 15 9.5775 15 9.76471C15 9.95192 14.9181 10.1315 14.7722 10.2638C14.6263 10.3962 14.4285 10.4706 14.2222 10.4706H1.77778C1.5715 10.4706 1.37367 10.3962 1.22781 10.2638C1.08194 10.1315 1 9.95192 1 9.76471C1 9.5775 1.08194 9.39795 1.22781 9.26557C1.37367 9.13319 1.5715 9.05882 1.77778 9.05882H14.2222ZM9.55556 5.52941C9.7538 5.52961 9.94447 5.5985 10.0886 5.72201C10.2328 5.84552 10.3195 6.01432 10.3311 6.19393C10.3428 6.37353 10.2784 6.55039 10.1511 6.68835C10.0239 6.82632 9.84341 6.91499 9.64656 6.93624L9.55556 6.94118H1.77778C1.57954 6.94098 1.38886 6.87209 1.24471 6.74858C1.10056 6.62507 1.01382 6.45627 1.0022 6.27666C0.990582 6.09706 1.05497 5.9202 1.18221 5.78224C1.30944 5.64427 1.48993 5.5556 1.68678 5.53435L1.77778 5.52941H9.55556ZM14.2222 2C14.4205 2.0002 14.6111 2.06909 14.7553 2.1926C14.8994 2.31611 14.9862 2.48491 14.9978 2.66451C15.0094 2.84412 14.945 3.02097 14.8178 3.15894C14.6906 3.29691 14.5101 3.38557 14.3132 3.40682L14.2222 3.41176H1.77778C1.57954 3.41157 1.38886 3.34267 1.24471 3.21917C1.10056 3.09566 1.01382 2.92686 1.0022 2.74725C0.990582 2.56764 1.05497 2.39079 1.18221 2.25282C1.30944 2.11486 1.48993 2.02619 1.68678 2.00494L1.77778 2H14.2222Z" fill="#A8A8AC"/>
        </svg>`;
    }

    static getPositionAlignmentIcon(position) {
        switch(position) {
            case "center":
                return '<svg width="16" height="16" viewBox="0 0 16 16" fill="#ACB5BD" xmlns="http://www.w3.org/2000/svg">\
                <path fill-rule="evenodd" clip-rule="evenodd" d="M0.5 2.16667V0.5H15.5V2.16667H0.5ZM3.83333 3.83333V5.5H12.1667V3.83333H3.83333ZM15.5\
                 8.83333H0.5V7.16667H15.5V8.83333ZM3.83333 10.5V12.1667H12.1667V10.5H3.83333ZM0.5 15.5H15.5V13.8333H0.5V15.5Z"/>\
                </svg>';
            case "left":
                return '<svg width="16" height="16" viewBox="0 0 16 16" fill="#ACB5BD" xmlns="http://www.w3.org/2000/svg">\
                <path fill-rule="evenodd" clip-rule="evenodd" d="M0.53418 2.22354V0.534126H15.7389V2.22354H0.53418ZM10.6707 3.91296H0.53418V5.60238H10.6707V3.91296ZM10.6707\
                 10.6706H0.53418V12.3601H10.6707V10.6706ZM15.7389 8.98122H0.53418V7.2918H15.7389V8.98122ZM0.53418 15.7389H15.7389V14.0495H0.53418V15.7389Z"/>\
                </svg>';
            case "right":
                return '<svg width="16" height="16" viewBox="0 0 16 16" fill="#ACB5BD" xmlns="http://www.w3.org/2000/svg">\
                <path fill-rule="evenodd" clip-rule="evenodd" d="M0.53418 2.22354V0.534126H15.7389V2.22354H0.53418ZM5.60243 5.60238H15.7389V3.91296H5.60243V5.60238ZM15.7389\
                 8.98122H0.53418V7.2918H15.7389V8.98122ZM5.60243 12.3601H15.7389V10.6706H5.60243V12.3601ZM0.53418 15.7389H15.7389V14.0495H0.53418V15.7389Z"/>\
                </svg>';
            case "justify":
                return '<svg width="16" height="16" viewBox="0 0 16 16" fill="#ACB5BD" xmlns="http://www.w3.org/2000/svg">\
                <path fill-rule="evenodd" clip-rule="evenodd" d="M0.5 2.16667V0.5H15.5V2.16667H0.5ZM0.5 5.5H15.5V3.83333H0.5V5.5ZM15.5 8.83333H0.5V7.16667H15.5V8.83333ZM0.5 12.1667H15.5V10.5H0.5V12.1667ZM0.5 15.5H15.5V13.8333H0.5V15.5Z"/>\
                </svg>'
            default:
                console.error("Unsupported alignment position");
        }
    }

    static getListIcon() {
        return `<svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M2.5 5C3.05228 5 3.5 4.55228 3.5 4C3.5 3.44772 3.05228 3 2.5 3C1.94772 3 1.5 3.44772 1.5 4C1.5 4.55228 1.94772 5 2.5 5Z" fill="#A8A8AC"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M15.5 4C15.5 4.41421 15.0655 4.75 14.5294 4.75L5.47059 4.75C4.93455 4.75 4.5 4.41421 4.5 4C4.5 3.58579 4.93455 3.25 5.47059 3.25L14.5294 3.25C15.0655 3.25 15.5 3.58579 15.5 4Z" fill="#A8A8AC"/>
        <path d="M2.5 9C3.05228 9 3.5 8.55228 3.5 8C3.5 7.44772 3.05228 7 2.5 7C1.94772 7 1.5 7.44772 1.5 8C1.5 8.55228 1.94772 9 2.5 9Z" fill="#A8A8AC"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M15.5 8C15.5 8.41421 15.0655 8.75 14.5294 8.75L5.47059 8.75C4.93455 8.75 4.5 8.41421 4.5 8C4.5 7.58579 4.93455 7.25 5.47059 7.25L14.5294 7.25C15.0655 7.25 15.5 7.58579 15.5 8Z" fill="#A8A8AC"/>
        <path d="M2.5 13C3.05228 13 3.5 12.5523 3.5 12C3.5 11.4477 3.05228 11 2.5 11C1.94772 11 1.5 11.4477 1.5 12C1.5 12.5523 1.94772 13 2.5 13Z" fill="#A8A8AC"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M15.5 12C15.5 12.4142 15.0655 12.75 14.5294 12.75H5.47059C4.93455 12.75 4.5 12.4142 4.5 12C4.5 11.5858 4.93455 11.25 5.47059 11.25H14.5294C15.0655 11.25 15.5 11.5858 15.5 12Z" fill="#A8A8AC"/>
        </svg>`;
    }

    static getListIconV2() {
        return '<svg width="16" height="14" viewBox="0 0 16 14" fill="#ACB5BD" xmlns="http://www.w3.org/2000/svg">\
        <path fill-rule="evenodd" clip-rule="evenodd" d="M0.323242 2.06821C0.323242 1.3671 0.889197 0.801147 1.59031 0.801147C2.29141 0.801147 2.85737 1.3671 2.85737 2.06821C2.85737 2.76932 2.29141 3.33527 1.59031 3.33527C0.889197 3.33527 0.323242 2.76932 0.323242 2.06821ZM0.323242 7.13647C0.323242 6.43536 0.889197 5.8694 1.59031 5.8694C2.29141 5.8694 2.85737 6.43536 2.85737 7.13647C2.85737 7.83757 2.29141 8.40353 1.59031 8.40353C0.889197 8.40353 0.323242 7.83757 0.323242 7.13647ZM1.59031 10.9377C0.889197 10.9377 0.323242 11.5121 0.323242 12.2047C0.323242 12.8974 0.897644 13.4718 1.59031 13.4718C2.28297 13.4718 2.85737 12.8974 2.85737 12.2047C2.85737 11.5121 2.29141 10.9377 1.59031 10.9377ZM15.9504 13.0494H4.12443V11.36H15.9504V13.0494ZM4.12443 7.98117H15.9504V6.29176H4.12443V7.98117ZM4.12443 2.91292V1.2235H15.9504V2.91292H4.12443Z"/>\
        </svg>'
    }

    static getNumberedListIcon() {
        return '<svg width="17" height="14" viewBox="0 0 17 14" fill="#ACB5BD" xmlns="http://www.w3.org/2000/svg">\
        <path fill-rule="evenodd" clip-rule="evenodd" d="M15.3161 3.75762H14.4714V1.22349H13.6267V0.378784H15.3161V3.75762ZM15.3161 11.7824V11.36H13.6267V10.5153H16.1608V13.8941H13.6267V13.0494H15.3161V12.6271H14.4714V11.7824H15.3161ZM13.6267 6.29175H15.1471L13.6267 8.06564V8.82587H16.1608V7.98116H14.6403L16.1608 6.20728V5.44704H13.6267V6.29175ZM11.9373 1.22349H0.111328V2.91291H11.9373V1.22349ZM0.111328 11.36H11.9373V13.0494H0.111328V11.36ZM11.9373 6.29175H0.111328V7.98117H11.9373V6.29175Z"/>\
        </svg>'
    }

    static getEmojiIcon() {
        return `<svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M11.213 9.39275C11.3086 9.01817 10.9866 8.7 10.6 8.7C10.2134 8.7 9.91395 9.03193 9.72861 9.37121C9.66521 9.48726 9.58514 9.59476 9.48995 9.68995C9.2274 9.9525 8.8713 10.1 8.5 10.1C8.1287 10.1 7.7726 9.9525 7.51005 9.68995C7.41486 9.59476 7.33479 9.48726 7.27139 9.37121C7.08605 9.03193 6.7866 8.7 6.4 8.7C6.0134 8.7 5.69138 9.01817 5.78704 9.39275C5.91039 9.87576 6.16163 10.3214 6.5201 10.6799C7.0452 11.205 7.75739 11.5 8.5 11.5C9.24261 11.5 9.9548 11.205 10.4799 10.6799C10.8384 10.3214 11.0896 9.87576 11.213 9.39275ZM7.1 6.6C7.1 6.78565 7.02625 6.9637 6.89497 7.09497C6.7637 7.22625 6.58565 7.3 6.4 7.3C6.21435 7.3 6.0363 7.22625 5.90503 7.09497C5.77375 6.9637 5.7 6.78565 5.7 6.6C5.7 6.41435 5.77375 6.2363 5.90503 6.10503C6.0363 5.97375 6.21435 5.9 6.4 5.9C6.58565 5.9 6.7637 5.97375 6.89497 6.10503C7.02625 6.2363 7.1 6.41435 7.1 6.6ZM10.6 7.3C10.7857 7.3 10.9637 7.22625 11.095 7.09497C11.2262 6.9637 11.3 6.78565 11.3 6.6C11.3 6.41435 11.2262 6.2363 11.095 6.10503C10.9637 5.97375 10.7857 5.9 10.6 5.9C10.4143 5.9 10.2363 5.97375 10.105 6.10503C9.97375 6.2363 9.9 6.41435 9.9 6.6C9.9 6.78565 9.97375 6.9637 10.105 7.09497C10.2363 7.22625 10.4143 7.3 10.6 7.3Z" fill="#A8A8AC"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M15.5 8C15.5 11.8661 12.3661 15 8.5 15C4.6339 15 1.5 11.8661 1.5 8C1.5 4.1339 4.6339 1 8.5 1C12.3661 1 15.5 4.1339 15.5 8ZM14.1 8C14.1 9.48521 13.51 10.9096 12.4598 11.9598C11.4096 13.01 9.98521 13.6 8.5 13.6C7.01479 13.6 5.59041 13.01 4.5402 11.9598C3.49 10.9096 2.9 9.48521 2.9 8C2.9 6.51479 3.49 5.09041 4.5402 4.0402C5.59041 2.99 7.01479 2.4 8.5 2.4C9.98521 2.4 11.4096 2.99 12.4598 4.0402C13.51 5.09041 14.1 6.51479 14.1 8Z" fill="#A8A8AC"/>
        </svg>`;
    }

    static getUrlLinkIcon() {
        return `<svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M9.01745 2.11223C9.7296 1.40008 10.6955 1 11.7026 1C12.7097 1 13.6756 1.40008 14.3878 2.11223C15.0999 2.82438 15.5 3.79026 15.5 4.79739C15.5 5.80452 15.0999 6.7704 14.3878 7.48255L12.2521 9.61748C11.54 10.3293 10.5743 10.7292 9.56742 10.7292C8.56053 10.7292 7.59485 10.3293 6.88272 9.61751C6.57892 9.31383 6.57881 8.82137 6.88249 8.51756C7.18617 8.21376 7.67863 8.21365 7.98244 8.51733C8.40286 8.93758 8.97297 9.17366 9.56742 9.17366C10.1619 9.17366 10.732 8.93758 11.1524 8.51733L13.2878 6.38261C13.7082 5.96219 13.9444 5.39192 13.9444 4.79739C13.9444 4.20282 13.7083 3.6326 13.2878 3.21217C12.8674 2.79175 12.2972 2.55556 11.7026 2.55556C11.108 2.55556 10.5378 2.79175 10.1174 3.21217L9.04997 4.27959C8.74623 4.58334 8.25377 4.58334 7.95003 4.27959C7.64629 3.97585 7.64629 3.48339 7.95003 3.17965L9.01745 2.11223ZM4.74788 6.3826C5.46001 5.67076 6.42568 5.27088 7.43258 5.27088C8.43947 5.27088 9.40515 5.67076 10.1173 6.3826C10.4211 6.68627 10.4212 7.17874 10.1175 7.48254C9.81383 7.78634 9.32137 7.78645 9.01756 7.48277C8.59714 7.06252 8.02702 6.82644 7.43258 6.82644C6.83814 6.82644 6.26804 7.06251 5.84762 7.48274L3.71217 9.6175C3.2918 10.0379 3.05556 10.6082 3.05556 11.2027C3.05556 11.7973 3.29175 12.3675 3.71217 12.7879C4.1326 13.2084 4.70282 13.4445 5.29739 13.4445C5.89196 13.4445 6.46218 13.2084 6.88261 12.7879L7.95003 11.7205C8.25377 11.4168 8.74623 11.4168 9.04997 11.7205C9.35371 12.0242 9.35371 12.5167 9.04997 12.8205L7.98255 13.8879C7.2704 14.6 6.30452 15.0001 5.29739 15.0001C4.29026 15.0001 3.32438 14.6 2.61223 13.8879C1.90008 13.1757 1.5 12.2098 1.5 11.2027C1.5 10.1956 1.90008 9.2297 2.61223 8.51755L4.74788 6.3826Z" fill="#A8A8AC"/>
        </svg>`;
    }

    static getUnlinkIcon() {
        return '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 9.94 9.67"><path d="M9.12,1.2A3.28,3.28,0,0,0,4.48.89l-.19.16L4.07.41A.61.61,0,0,0,3.29,0h0a.61.61,0,0,0-.38.78L5.2,7.38l-.55.48a2.08,2.08,0,0,1-1.5.51,\
        2.11,2.11,0,0,1-1.42-.7,2.06,2.06,0,0,1-.5-1.5,2,2,0,0,1,.7-1.42l.67-.59a.61.61,0,1,0-.81-.92l-.67.59A3.29,3.29,0,0,0,3.07,9.59h.22a3.28,3.28,0,0,0,2.17-.81l.17-.15.22.63a.61.61,0,0,0,.78.37h0A.59.59,0,0,0,7,8.86L4.73,\
        2.3l.56-.49a2,2,0,0,1,1.5-.51A2.06,2.06,0,0,1,8.2,2,2.07,2.07,0,0,1,8,4.92l-.67.59a.61.61,0,0,0-.***********,0,0,0,.86.06l.68-.59A3.29,3.29,0,0,0,9.12,1.2Z"/>\
        </svg>'
    }

    static getPersonalizationIcon() {
        return `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M1 3.71429C1 3.3198 1.3134 3 1.7 3H9.4C9.7866 3 10.1 3.3198 10.1 3.71429C10.1 4.10877 9.7866 4.42857 9.4 4.42857H1.7C1.3134 4.42857 1 4.10877 1 3.71429ZM1 7.28571C1 6.89123 1.3134 6.57143 1.7 6.57143H9.4C9.7866 6.57143 10.1 6.89123 10.1 7.28571C10.1 7.6802 9.7866 8 9.4 8H1.7C1.3134 8 1 7.6802 1 7.28571ZM12.2 7.28571C12.5866 7.28571 12.9 7.60551 12.9 8V9.42857H14.3C14.6866 9.42857 15 9.74837 15 10.1429C15 10.5373 14.6866 10.8571 14.3 10.8571H12.9V12.2857C12.9 12.6802 12.5866 13 12.2 13C11.8134 13 11.5 12.6802 11.5 12.2857V10.8571H10.1C9.7134 10.8571 9.4 10.5373 9.4 10.1429C9.4 9.74837 9.7134 9.42857 10.1 9.42857H11.5V8C11.5 7.60551 11.8134 7.28571 12.2 7.28571ZM1 10.8571C1 10.4627 1.3134 10.1429 1.7 10.1429H6.6C6.9866 10.1429 7.3 10.4627 7.3 10.8571C7.3 11.2516 6.9866 11.5714 6.6 11.5714H1.7C1.3134 11.5714 1 11.2516 1 10.8571Z" fill="#A8A8AC"/>
        </svg>`;
    }

    static getSectionPaddingIcon() {
        return `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M4.25 3.33301C4.25 2.91879 4.58579 2.58301 5 2.58301H11C11.4142 2.58301 11.75 2.91879 11.75 3.33301C11.75 3.74722 11.4142 4.08301 11 4.08301H5C4.58579 4.08301 4.25 3.74722 4.25 3.33301Z" fill="#A8A8AC"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M12.6665 11.75C12.2523 11.75 11.9165 11.4142 11.9165 11V5C11.9165 4.58579 12.2523 4.25 12.6665 4.25C13.0807 4.25 13.4165 4.58579 13.4165 5V11C13.4165 11.4142 13.0807 11.75 12.6665 11.75Z" fill="#A8A8AC"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M3.3335 11.75C2.91928 11.75 2.5835 11.4142 2.5835 11V5C2.5835 4.58579 2.91928 4.25 3.3335 4.25C3.74771 4.25 4.0835 4.58579 4.0835 5V11C4.0835 11.4142 3.74771 11.75 3.3335 11.75Z" fill="#A8A8AC"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M4.25 12C4.25 11.5858 4.58579 11.25 5 11.25H11C11.4142 11.25 11.75 11.5858 11.75 12C11.75 12.4142 11.4142 12.75 11 12.75H5C4.58579 12.75 4.25 12.4142 4.25 12Z" fill="#A8A8AC"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M14 2H2V14H14V2ZM2 1C1.44772 1 1 1.44772 1 2V14C1 14.5523 1.44772 15 2 15H14C14.5523 15 15 14.5523 15 14V2C15 1.44772 14.5523 1 14 1H2Z" fill="#A8A8AC"/>
        </svg>`;
    }

    static getButtonStyleIcon() {
        return '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 8.84 9.67">\
        <path d="M8.5.05,8.78.3h0a.19.19,0,0,1,0,.27L3.83,7l0,0h0L2.36,5.66h0l0,0L8.24.05h0a.19.19,0,0,1,.26,0Z"/>\
        <polygon points="2.95 7.91 2.95 7.91 3.67 7.11 3.67 7.11 2.23 5.8 2.23 5.8 1.51 6.6 1.51 6.6 2.95 7.91"/>\
        <path d="M.23,8.64A3.49,3.49,0,0,1,.84,7.2a1,1,0,0,1,.64-.37L2.71,8a1.31,1.31,0,0,1-.38.79,3.72,3.72,0,0,1-1.67.79l-.4.13H.13A.21.21,0,0,1,0,9.41H0a6.49,6.49,0,0,0,.22-.77Z"/>\
        </svg>'
    }

    static getUploadIcon() {
        return `<svg width="17" height="17" viewBox="0 0 17 17" fill="#FFFFFF" xmlns="http://www.w3.org/2000/svg">
        <path d="M8.89474 15.2105H1.47368C1.34806 15.2105 1.22757 15.1606 1.13874 15.0718C1.04991 14.983 1 14.8625 1 14.7368V1.47368C1 1.34806 1.04991 1.22757 1.13874 1.13874C1.22757 1.04991 1.34806 1 1.47368 1H14.7368C14.8625 1 14.983 1.04991 15.0718 1.13874C15.1606 1.22757 15.2105 1.34806 15.2105 1.47368V8.89474" stroke="#3D4A52" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M1 11.2632L6.52632 8.89478L10.8684 10.8685" stroke="#3D4A52" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M11.2633 6.5263C10.8445 6.5263 10.4429 6.35995 10.1468 6.06384C9.85068 5.76773 9.68433 5.36612 9.68433 4.94736C9.68433 4.52859 9.85068 4.12698 10.1468 3.83087C10.4429 3.53476 10.8445 3.36841 11.2633 3.36841C11.682 3.36841 12.0836 3.53476 12.3798 3.83087C12.6759 4.12698 12.8422 4.52859 12.8422 4.94736C12.8422 5.36612 12.6759 5.76773 12.3798 6.06384C12.0836 6.35995 11.682 6.5263 11.2633 6.5263V6.5263Z" stroke="#3D4A52" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M13.6316 13.6316V16M11.2632 13.6316H13.6316H11.2632ZM16 13.6316H13.6316H16ZM13.6316 13.6316V11.2632V13.6316Z" stroke="#3D4A52" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>`
    }

    static getGenericImageIcon() {
        return `<svg width="14" height="12" viewBox="0 0 14 12" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M12.5742 1.60547H1.30078C1.18579 1.60547 1.0755 1.65115 0.994184 1.73247C0.91287 1.81378 0.867188 1.92407 0.867188 2.03906V9.84375C0.867188 9.95875 0.91287 10.069 0.994184 10.1503C1.0755 10.2317 1.18579 10.2773 1.30078 10.2773H12.5742C12.6892 10.2773 12.7995 10.2317 12.8808 10.1503C12.9621 10.069 13.0078 9.95875 13.0078 9.84375V2.03906C13.0078 1.92407 12.9621 1.81378 12.8808 1.73247C12.7995 1.65115 12.6892 1.60547 12.5742 1.60547ZM1.30078 0.738281C0.955793 0.738281 0.624934 0.875327 0.38099 1.11927C0.137046 1.36322 0 1.69407 0 2.03906L0 9.84375C0 10.1887 0.137046 10.5196 0.38099 10.7635C0.624934 11.0075 0.955793 11.1445 1.30078 11.1445H12.5742C12.9192 11.1445 13.2501 11.0075 13.494 10.7635C13.738 10.5196 13.875 10.1887 13.875 9.84375V2.03906C13.875 1.69407 13.738 1.36322 13.494 1.11927C13.2501 0.875327 12.9192 0.738281 12.5742 0.738281H1.30078Z" fill="#495057"/>
        <path d="M9.23379 5.63438C9.29819 5.57016 9.38116 5.52781 9.47096 5.51334C9.56076 5.49886 9.65283 5.513 9.73415 5.55373L13.0095 7.24215V10.2773H0.868896V9.41012L3.16347 7.36876C3.23435 7.29814 3.32747 7.2542 3.42705 7.2444C3.52662 7.2346 3.62652 7.25954 3.7098 7.31499L6.01652 8.85251L9.23379 5.63525V5.63438Z" fill="#495057"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M3.90405 5.07422C4.07487 5.07422 4.24402 5.04057 4.40184 4.9752C4.55966 4.90983 4.70306 4.81402 4.82384 4.69323C4.94463 4.57244 5.04045 4.42904 5.10582 4.27122C5.17119 4.11341 5.20483 3.94426 5.20483 3.77344C5.20483 3.60262 5.17119 3.43347 5.10582 3.27565C5.04045 3.11783 4.94463 2.97443 4.82384 2.85365C4.70306 2.73286 4.55966 2.63704 4.40184 2.57167C4.24402 2.5063 4.07487 2.47266 3.90405 2.47266C3.55906 2.47266 3.22821 2.6097 2.98426 2.85365C2.74032 3.09759 2.60327 3.42845 2.60327 3.77344C2.60327 4.11843 2.74032 4.44929 2.98426 4.69323C3.22821 4.93717 3.55906 5.07422 3.90405 5.07422Z" fill="#495057"/>
        </svg>`
    }

    static getDeleteImageIcon() {
        return `<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M2.42212 3.84424H15.2215L14.819 11.8933C14.686 14.5543 12.4896 16.6436 9.82524 16.6436H7.81833C5.15396 16.6436 2.95762 14.5543 2.82457 11.8933L2.42212 3.84424Z" stroke="#ACB5BD" stroke-width="1.5"/>
        <path d="M5.26648 3.84424V3.84424C5.26648 2.27338 6.53991 0.999941 8.11077 0.999941H9.53292C11.1038 0.999941 12.3772 2.27338 12.3772 3.84424V3.84424" stroke="#ACB5BD" stroke-width="1.5"/>
        <path d="M1 3.84424H16.6436" stroke="#ACB5BD" stroke-width="1.5" stroke-linecap="round"/>
        <path d="M10.9551 8.1106V11.666" stroke="#ACB5BD" stroke-width="1.5" stroke-linecap="round"/>
        <path d="M6.6886 8.1106V11.666" stroke="#ACB5BD" stroke-width="1.5" stroke-linecap="round"/>
        </svg>`;
    }

    static getAccessibilityIcon() {
        return `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 1C8.73333 1 9.33333 1.63 9.33333 2.4C9.33333 3.17 8.73333 3.8 8 3.8C7.26667 3.8 6.66667 3.17 6.66667 2.4C6.66667 1.63 7.26667 1 8 1ZM13.3333 5.9H10V14.3C10 14.685 9.7 15 9.33333 15C8.96667 15 8.66667 14.685 8.66667 14.3V10.8H7.33333V14.3C7.33333 14.685 7.03333 15 6.66667 15C6.3 15 6 14.685 6 14.3V5.9H2.66667C2.3 5.9 2 5.585 2 5.2C2 4.815 2.3 4.5 2.66667 4.5H13.3333C13.7 4.5 14 4.815 14 5.2C14 5.585 13.7 5.9 13.3333 5.9Z" fill="#A8A8AC"/>
        </svg>`;
    }

    static getAddIcon() {
        return '<svg width="9" height="9" viewBox="0 0 9 9" fill="#A3B0B8" xmlns="http://www.w3.org/2000/svg">\
        <path d="M8.29159 5.04167H5.04159V8.29167H3.95825V5.04167H0.708252V3.95833H3.95825V0.708332H5.04159V3.95833H8.29159V5.04167Z"/>\
        </svg>'
    }

    static getLineHeightIcon() {
        return '<svg width="14" height="14" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">\
        <path d="M12 1.33333V0H0V1.33333H12Z" fill="#ACB5BD"/>\
        <path d="M12 10.6667V12.0001H0V10.6667H12Z" fill="#ACB5BD"/>\
        <path fill-rule="evenodd" clip-rule="evenodd" d="M6.00008 2.91728C5.8665 2.90918 5.73357 2.94148 5.61859 3.00997C5.50362 3.07845 5.41191 3.17996 5.35541 3.30128L3.10141 8.13461C3.06279 8.21413 3.04037 8.30055 3.03545 8.38882C3.03054 8.47709 3.04323 8.56546 3.07279 8.64878C3.10235 8.7321 3.14819 8.8087 3.20764 8.87414C3.26708 8.93958 3.33895 8.99254 3.41906 9.02994C3.49916 9.06734 3.58591 9.08844 3.67425 9.092C3.76258 9.09556 3.85075 9.08151 3.9336 9.05068C4.01646 9.01984 4.09235 8.97284 4.15687 8.91239C4.22139 8.85195 4.27325 8.77928 4.30941 8.69861L4.63541 7.99994H7.36475L7.69075 8.69861C7.76545 8.85889 7.90076 8.98293 8.06692 9.04344C8.23308 9.10395 8.41647 9.09598 8.57675 9.02128C8.73703 8.94657 8.86107 8.81126 8.92158 8.6451C8.98209 8.47895 8.97412 8.29556 8.89942 8.13528L6.64541 3.30128C6.58887 3.17985 6.49707 3.07827 6.38196 3.00977C6.26685 2.94128 6.13377 2.90904 6.00008 2.91728ZM6.74341 6.66661H5.25675L6.00008 5.07328L6.74341 6.66661Z" fill="#ACB5BD"/>\
        </svg>'
    }

    static getLetterSpaceIcon() {
        return '<svg width="14" height="14" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">\
        <path d="M11.4583 12.875H12.8749V0.125H11.4583V12.875Z" fill="#ACB5BD"/>\
        <path d="M1.54167 12.875H0.125V0.125H1.54167V12.875Z" fill="#ACB5BD"/>\
        <path fill-rule="evenodd" clip-rule="evenodd" d="M4.70364 9.36733L5.05002 8.625H7.94994L8.29631 9.36733C8.37568 9.53763 8.51945 9.66942 8.696 9.73371C8.87254 9.79801 9.06739 9.78954 9.23769 9.71016C9.40798 9.63079 9.53977 9.48702 9.60407 9.31048C9.66836 9.13394 9.65989 8.93909 9.58052 8.76879L7.18565 3.63337C7.12579 3.50409 7.02833 3.39586 6.90601 3.32282C6.78369 3.24979 6.64218 3.21533 6.49998 3.22396C6.35805 3.21536 6.21681 3.24967 6.09465 3.32244C5.97248 3.39521 5.87505 3.50306 5.81502 3.63196L3.42014 8.76737C3.34406 8.93708 3.33777 9.1299 3.40263 9.3042C3.46749 9.47851 3.59831 9.62031 3.76683 9.69898C3.93535 9.77766 4.12805 9.78691 4.30333 9.72472C4.4786 9.66254 4.62239 9.53392 4.70364 9.36662V9.36733ZM6.49998 5.51541L5.71019 7.20833H7.28977L6.49998 5.51541Z" fill="#ACB5BD"/>\
        </svg>'
    }

    static getButtonTypgraphyIcon() {
        return '<svg width="15" height="17" viewBox="0 0 15 17" fill="#3D4A52" xmlns="http://www.w3.org/2000/svg">\
        <path fill-rule="evenodd" clip-rule="evenodd" d="M4.4375 8.325H8.8125L9.6 10.25H11.4375L7.28125 0.625H5.96875L1.8125 10.25H3.65L4.4375 8.325ZM14.5 13.75L11.875 11.125V12.875H0.5V14.625H11.875V16.375L14.5 13.75ZM8.26125 6.75L6.625 2.3575L4.98875 6.75H8.26125Z"/>\
        </svg>'
    }

    static getButtonColorIcon() {
        return `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8.00016 14.6663C4.32683 14.6663 1.3335 11.673 1.3335 7.99967C1.3335 4.32634 4.32683 1.33301 8.00016 1.33301C11.6735 1.33301 14.6668 4.02634 14.6668 7.33301C14.6668 9.53967 12.8735 11.333 10.6668 11.333H9.48683C9.30016 11.333 9.1535 11.4797 9.1535 11.6663C9.1535 11.7463 9.18683 11.8197 9.24016 11.8863C9.5135 12.1997 9.66683 12.593 9.66683 12.9997C9.66683 13.4417 9.49123 13.8656 9.17867 14.1782C8.86611 14.4907 8.44219 14.6663 8.00016 14.6663ZM8.00016 2.66634C5.06016 2.66634 2.66683 5.05967 2.66683 7.99967C2.66683 10.9397 5.06016 13.333 8.00016 13.333C8.18683 13.333 8.3335 13.1863 8.3335 12.9997C8.33142 12.9132 8.29829 12.8304 8.24016 12.7663C7.96683 12.4597 7.82016 12.0663 7.82016 11.6663C7.82016 11.2243 7.99576 10.8004 8.30832 10.4878C8.62088 10.1753 9.0448 9.99967 9.48683 9.99967H10.6668C12.1402 9.99967 13.3335 8.80634 13.3335 7.33301C13.3335 4.75967 10.9402 2.66634 8.00016 2.66634Z" fill="#A8A8AC"/>
        <path d="M4.3335 8.66634C4.88578 8.66634 5.3335 8.21863 5.3335 7.66634C5.3335 7.11406 4.88578 6.66634 4.3335 6.66634C3.78121 6.66634 3.3335 7.11406 3.3335 7.66634C3.3335 8.21863 3.78121 8.66634 4.3335 8.66634Z" fill="#A8A8AC"/>
        <path d="M6.3335 5.99967C6.88578 5.99967 7.3335 5.55196 7.3335 4.99967C7.3335 4.44739 6.88578 3.99967 6.3335 3.99967C5.78121 3.99967 5.3335 4.44739 5.3335 4.99967C5.3335 5.55196 5.78121 5.99967 6.3335 5.99967Z" fill="#A8A8AC"/>
        <path d="M9.66683 5.99967C10.2191 5.99967 10.6668 5.55196 10.6668 4.99967C10.6668 4.44739 10.2191 3.99967 9.66683 3.99967C9.11454 3.99967 8.66683 4.44739 8.66683 4.99967C8.66683 5.55196 9.11454 5.99967 9.66683 5.99967Z" fill="#A8A8AC"/>
        <path d="M11.6668 8.66634C12.2191 8.66634 12.6668 8.21863 12.6668 7.66634C12.6668 7.11406 12.2191 6.66634 11.6668 6.66634C11.1145 6.66634 10.6668 7.11406 10.6668 7.66634C10.6668 8.21863 11.1145 8.66634 11.6668 8.66634Z" fill="#A8A8AC"/>
        </svg>`;
    }

    static getOpacityIcon() {
        return '<svg width="14" height="18" viewBox="0 0 14 18" fill="#3D4A52" xmlns="http://www.w3.org/2000/svg">\
        <path fill-rule="evenodd" clip-rule="evenodd" d="M7.13646 0.980957L11.9175 5.75356C13.2353 7.07131 13.8941 8.8283 13.8941 10.5177C13.8941 12.2071 13.2353 13.9895 11.9175 15.3072C10.5998 16.625 8.86811 17.2923 7.13646 17.2923C5.4048 17.2923 3.67315 16.625 2.3554 15.3072C1.03766\
         13.9895 0.378784 12.2071 0.378784 10.5177C0.378784 8.8283 1.03766 7.07131 2.3554 5.75356L7.13646 0.980957ZM3.55489 7.1051C2.59192 8.05962 2.07665 9.1324 2.0682 10.8218H12.2047C12.1963 9.1324 11.681 8.09341 10.718 7.14733L7.13646 3.44751L3.55489 7.1051Z"/>\
        </svg>'
    }

    static getbuttonConfigIcon() {
        return '<svg width="16" height="16" viewBox="0 0 16 16" fill="#3D4A52" xmlns="http://www.w3.org/2000/svg">\
        <path fill-rule="evenodd" clip-rule="evenodd" d="M0.0800781 0.53418V15.7389H1.7695V2.2236H15.2848V0.53418H0.0800781ZM15.2848 8.98127H13.5954V7.29185H15.2848V8.98127ZM13.5954 12.3601H15.2848V10.6707H13.5954V12.3601ZM3.45891 15.7389H5.14833V14.0495H3.45891V15.7389ZM11.906\
         15.7389H10.2166V14.0495H11.906V15.7389ZM15.2848 15.7389H13.5954V14.0495H15.2848V15.7389ZM8.52717 15.7389H6.83775V14.0495H8.52717V15.7389ZM15.2848 5.60243H13.5954V3.91302H15.2848V5.60243Z"/>\
        </svg>'
    }

    static getEclipseIcon() {
        return '<svg width="15" height="15" viewBox="0 0 15 15" fill="#A3B0B8" xmlns="http://www.w3.org/2000/svg">\
        <path fill-rule="evenodd" clip-rule="evenodd" d="M0.416748 7.50008C0.416748 3.59008 3.59008 0.416748 7.50008 0.416748C11.4101 0.416748 14.5834 3.59008 14.5834 7.50008C14.5834 11.4101 11.4101 14.5834 7.50008 14.5834C3.59008 14.5834 0.416748 11.4101 0.416748 7.50008ZM1.83341\
        7.50008C1.83341 10.3901 4.00091 12.7701 6.79175 13.1172V1.883C3.99383 2.23008 1.83341 4.61008 1.83341 7.50008ZM8.20842 1.883C8.938 1.97508 9.62508 2.20175 10.2413 2.54175H8.20842V1.883ZM11.9201 3.95841H8.20842V4.66675H12.4017C12.2601 4.41883 12.0972 4.178 11.9201 3.95841ZM8.20842\
        6.08341H12.9826C13.0392 6.31716 13.0888 6.55091 13.1172 6.79175H8.20842V6.08341ZM8.20842 12.4584V13.1172C8.938 13.0251 9.62508 12.7984 10.2413 12.4584H8.20842ZM11.9201 11.0417H8.20842V10.3334H12.4017C12.2601 10.5813 12.0972 10.8222 11.9201 11.0417ZM8.20842 8.91675H12.9826C13.0392\
        8.683 13.0888 8.44925 13.1172 8.20842H8.20842V8.91675Z" fill-opacity="0.5"/>\
        </svg>'
    }

    static getUnifyInputIcon(isActive) {
        if(isActive) {
            return `<svg class="active-unify-icon" width="9" height="15" viewBox="0 0 9 15" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M2.375 6.79167L2.37501 3.95834C2.37501 2.78959 3.33126 1.83334 4.50001 1.83334C5.66875 1.83334 6.62501 2.78959 6.625 3.95834L6.625 6.79167L8.04167 6.79167L8.04167 3.95834C8.04167 2.00334 6.45501 0.416672 4.50001 0.416672C2.54501 0.416672 0.958339 2.00334 0.958339 3.95834L0.958338 6.79167L2.375 6.79167ZM8.04167 11.0417L8.04167 8.20834L6.625 8.20834L6.625 11.0417C6.625 12.2104 5.66875 13.1667 4.5 13.1667C3.33125 13.1667 2.375 12.2104 2.375 11.0417L2.375 8.20834L0.958338 8.20834L0.958338 11.0417C0.958338 12.9967 2.545 14.5833 4.5 14.5833C6.455 14.5833 8.04167 12.9967 8.04167 11.0417ZM5.20834 10.3333L5.20834 4.66667L3.79167 4.66667L3.79167 10.3333L5.20834 10.3333Z" fill="white"/>
            </svg>`;
        }

        return `<svg class="inactive-unify-icon" width="9" height="15" viewBox="0 0 9 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M2.37501 3.95833L2.375 6.79166L0.958338 6.79166L0.958339 3.95833C0.958339 2.00333 2.54501 0.416664 4.50001 0.416664C6.45501 0.416664 8.04167 2.00333 8.04167 3.95833L8.04167 6.79166L6.625 6.79166L6.625 3.95833C6.62501 2.78958 5.66875 1.83333 4.50001 1.83333C3.33126 1.83333 2.37501 2.78958 2.37501 3.95833ZM8.04167 8.20833L8.04167 11.0417C8.04167 12.9967 6.455 14.5833 4.5 14.5833C2.545 14.5833 0.958338 12.9967 0.958338 11.0417L0.958338 8.20833L2.375 8.20833L2.375 11.0417C2.375 12.2104 3.33125 13.1667 4.5 13.1667C5.66875 13.1667 6.625 12.2104 6.625 11.0417L6.625 8.20833L8.04167 8.20833Z" fill="white"/>
        </svg>`;
    }

    static getExpandIcon() {
        return `<svg width="20" height="20" viewBox="0 0 20 20" fill="#3D4A52" xmlns="http://www.w3.org/2000/svg">
        <path d="M0.983965 10.1457C1.52745 10.1457 1.96794 10.5862 1.96794 11.1297V16.6404L16.6404 1.96796H11.1296C10.5862 1.96796 10.1457 1.52746 10.1457 0.983978C10.1457 0.440494 10.5862 0 11.1296 0H19.0159C19.5593 0 19.9999 0.440494 19.9999 0.983978V8.87023C19.9999 9.41371 19.5594 9.85421 19.0159 9.85421C18.4724 9.85421 18.0319 9.41371 18.0319 8.87023V3.35963L3.35951 18.032H8.87022C9.4137 18.032 9.85419 18.4725 9.85419 19.016C9.85419 19.5595 9.4137 20 8.87022 20H0.983965C0.440481 20 -1.14441e-05 19.5595 -1.14441e-05 19.016V11.1298C-1.14441e-05 10.5863 0.440481 10.1457 0.983965 10.1457Z"/>
        </svg>`;
    }

    static getSearchIcon() {
        return `<svg width="59" height="53" viewBox="0 0 59 53" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M16.6011 30.1875C18.0681 30.1875 19.475 29.6047 20.5123 28.5674C21.5496 27.5301 22.1324 26.1232 22.1324 24.6562C22.1324 23.1893 21.5496 21.7824 20.5123 20.7451C19.475 19.7078 18.0681 19.125 16.6011 19.125C15.1342 19.125 13.7273 19.7078 12.69 20.7451C11.6526 21.7824 11.0699 23.1893 11.0699 24.6562C11.0699 26.1232 11.6526 27.5301 12.69 28.5674C13.7273 29.6047 15.1342 30.1875 16.6011 30.1875Z" fill="#A3B0B8"/>
        <path d="M51.6324 44.9375C51.6324 46.8935 50.8554 48.7693 49.4723 50.1524C48.0892 51.5355 46.2134 52.3125 44.2574 52.3125H7.38239C5.42641 52.3125 3.55055 51.5355 2.16747 50.1524C0.784392 48.7693 0.00738525 46.8935 0.00738525 44.9375V15.4375C0.00738428 13.4828 0.783379 11.6081 2.16487 10.2252C3.54635 8.84232 5.42032 8.06445 7.37501 8.0625C7.37501 6.10653 8.15202 4.23067 9.5351 2.84759C10.9182 1.46451 12.794 0.6875 14.75 0.6875H51.625C53.581 0.6875 55.4568 1.46451 56.8399 2.84759C58.223 4.23067 59 6.10653 59 8.0625V37.5625C59 39.5172 58.224 41.3919 56.8425 42.7748C55.461 44.1577 53.5871 44.9355 51.6324 44.9375ZM51.625 4.375H14.75C13.772 4.375 12.8341 4.7635 12.1426 5.45504C11.451 6.14658 11.0625 7.08451 11.0625 8.0625H44.2574C46.2134 8.0625 48.0892 8.83951 49.4723 10.2226C50.8554 11.6057 51.6324 13.4815 51.6324 15.4375V41.25C52.6091 41.248 53.5451 40.8587 54.2351 40.1674C54.925 39.476 55.3125 38.5392 55.3125 37.5625V8.0625C55.3125 7.08451 54.924 6.14658 54.2325 5.45504C53.5409 4.7635 52.603 4.375 51.625 4.375ZM7.38239 11.75C6.4044 11.75 5.46647 12.1385 4.77493 12.83C4.08339 13.5216 3.69489 14.4595 3.69489 15.4375V44.9375L13.452 36.2571C13.7534 35.9568 14.1494 35.77 14.5728 35.7283C14.9962 35.6867 15.421 35.7927 15.7751 36.0285L25.5839 42.5664L39.2645 28.8858C39.538 28.6121 39.8906 28.4313 40.2724 28.3691C40.6543 28.3069 41.046 28.3664 41.3922 28.5392L47.9449 35.7188V15.4375C47.9449 14.4595 47.5564 13.5216 46.8648 12.83C46.1733 12.1385 45.2354 11.75 44.2574 11.75H7.38239Z" fill="#A3B0B8"/>
        </svg>`;
    }

    static getUnsplashLogo() {
        return `<svg height="17" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 457.19 104.19" style="&#10;">
        <path d="M59.88 38.66h27.53v48.75H0V38.66h27.53v24.37h32.35zm93 25c0 8.25-5.45 13.13-12.9 13.13-7.28 0-12.81-4.88-12.81-13.13V24.41h-12.22v39.13c0 15.45 11 25.21 25.06 25.21s25.15-9.76 25.15-25.21V24.41h-12.25zm43.7-21.13c-4.7 0-9.94 2-12.6 6.57v-5.41h-11.45v43.64h11.81v-25.1c0-5 3-9 8.16-9 5.68 0 8.08 3.82 8.08 8.7v25.4h11.8V59.82c.03-9.59-4.94-17.31-15.77-17.31zm43.31 18.37l-6.48-1.33c-2.47-.5-4-1.77-4-3.9 0-2.49 2.23-4.35 5.33-4.35 4.36 0 6.09 2.25 6.51 4.88h10.18c-.08-6-4.83-13.84-16.51-13.84-9.41 0-16.33 6.47-16.33 14.28 0 6.13 3.81 11.19 12.24 13l6.05 1.33c3.37.71 4.7 2.31 4.7 4.26 0 2.31-2.14 4.35-6 4.35-4.71 0-7.27-2.68-7.87-5.79h-10.5c.59 6.53 5.32 14.84 18.46 14.84 11.45 0 17.22-7.28 17.22-14.38-.01-6.36-4.36-11.59-12.97-13.37zm63.19 4.53c0 13.22-8.26 23-20.59 23-6 0-10.48-2.4-12.61-5.33v21.13h-11.8V43.67h11.45v5.41c2-3.37 6.83-6.39 13.4-6.39 12.81 0 20.18 9.76 20.18 22.72zm-11.63.09c0-7.72-4.79-12.25-10.83-12.25s-10.91 4.53-10.91 12.25 4.88 12.33 10.91 12.33 10.91-4.54 10.91-12.35zm68-21.83h11.45v43.64h-11.8v-5.31c-2 3.5-6.57 6.38-12.61 6.38-12.33 0-20.59-9.77-20.59-23 0-13 7.37-22.72 20.15-22.72 6.57 0 11.32 3.05 13.4 6.39zm-.18 21.83c0-7.72-4.88-12.25-10.91-12.25s-10.83 4.51-10.83 12.23 4.79 12.33 10.83 12.33 10.92-4.6 10.92-12.33zm-50.66 21.81h11.8V24.41h-11.8zm132.35-44.81c-4.17 0-9 1.41-11.81 4.78V24.41h-11.8v62.91h11.8V61.68c.27-4.8 3.2-8.52 8.17-8.52 5.68 0 8.08 3.83 8.07 8.71v25.47h11.81V59.82c-.01-9.59-5.15-17.3-16.24-17.3zm-42 18.36l-6.43-1.33c-2.47-.5-4-1.77-4-3.9 0-2.49 2.22-4.35 5.33-4.35 4.35 0 6.08 2.25 6.5 4.88h10.17c-.08-6-4.83-13.84-16.51-13.84-9.41 0-16.33 6.47-16.33 14.28 0 6.13 3.82 11.19 12.25 13l6 1.33c3.37.71 4.7 2.31 4.7 4.26 0 2.31-2.14 4.35-6 4.35-4.71 0-7.27-2.68-7.87-5.79h-10.49c.58 6.53 5.31 14.84 18.45 14.84 11.45 0 17.22-7.28 17.22-14.38 0-6.34-4.35-11.57-12.95-13.35zM59.88 0H27.53v24.37h32.35z"/>
        </svg>`;
    }

    static getSettingsIcon() {
        return `<svg width="17" height="17" viewBox="0 0 17 17" fill="#3D4A52" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M12.875 5.875H11.125V0.625H12.875V2.375H16.375V4.125H12.875V5.875ZM0.625 4.125V2.375H9.375V4.125H0.625ZM0.625 14.625V12.875H5.875V14.625H0.625ZM9.375 16.375V14.625H16.375V12.875H9.375V11.125H7.625V16.375H9.375ZM4.125 7.625V5.875H5.875V11.125H4.125V9.375H0.625V7.625H4.125ZM16.375 9.375V7.625H7.625V9.375H16.375Z"/>
        </svg>`;
    }

    static getActionIcon() {
        return `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M8.1864 2.02786C8.46276 2.11212 8.65217 2.37192 8.65217 2.66671V5.87881H12.3478C12.5874 5.87881 12.8076 6.01305 12.9216 6.22847C13.0355 6.44388 13.0247 6.70579 12.8934 6.91066L8.54562 13.6985C8.38764 13.9451 8.08996 14.0564 7.8136 13.9721C7.53724 13.8879 7.34783 13.6281 7.34783 13.3333V10.1212H3.65217C3.41263 10.1212 3.19236 9.98695 3.07845 9.77154C2.96454 9.55612 2.97533 9.29421 3.10655 9.08935L7.45438 2.30153C7.61236 2.05489 7.91004 1.94361 8.1864 2.02786ZM4.85873 8.78787H8C8.36019 8.78787 8.65217 9.08635 8.65217 9.45453V11.0981L11.1413 7.21213H8C7.63981 7.21213 7.34783 6.91365 7.34783 6.54547V4.9019L4.85873 8.78787Z" fill="#A8A8AC"/>
        </svg>`;
    }

    static getEmbedLinkIcon() {
        return `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9.12279 6.74385C8.49179 6.11311 7.63613 5.75879 6.74395 5.75879C5.85177 5.75879 4.99611 6.11311 4.36511 6.74385L1.98551 9.12268C1.3545 9.75369 1 10.6095 1 11.5019C1 12.3943 1.3545 13.2501 1.98551 13.8811C2.61651 14.5121 3.47235 14.8666 4.36473 14.8666C5.25711 14.8666 6.11294 14.5121 6.74395 13.8811L7.93337 12.6917" stroke="#3D4A52" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M6.74374 9.12279C7.37474 9.75352 8.2304 10.1078 9.12258 10.1078C10.0148 10.1078 10.8704 9.75352 11.5014 9.12279L13.881 6.74395C14.512 6.11294 14.8665 5.25711 14.8665 4.36473C14.8665 3.47235 14.512 2.61651 13.881 1.98551C13.25 1.3545 12.3942 1 11.5018 1C10.6094 1 9.75359 1.3545 9.12258 1.98551L7.93316 3.17492" stroke="#3D4A52" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>`;
    }

    static getEmbedResizeIcon() {
        return `<svg width="18" height="14" viewBox="0 0 18 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12.6123 2.74773C12.7334 2.8672 12.8 3.03019 12.8 3.2V5.33333C12.8 5.50307 12.7326 5.66586 12.6125 5.78588C12.4925 5.9059 12.3297 5.97333 12.16 5.97333C11.9903 5.97333 11.8275 5.9059 11.7075 5.78588C11.5874 5.66586 11.52 5.50307 11.52 5.33333V4.74453L10.0523 6.21227C9.9932 6.27336 9.92256 6.32209 9.84446 6.35559C9.76636 6.3891 9.68237 6.40671 9.59739 6.40741C9.51241 6.40811 9.42815 6.39188 9.34951 6.35966C9.27087 6.32744 9.19944 6.27989 9.13937 6.21977C9.07931 6.15965 9.03182 6.08817 8.99968 6.0095C8.96754 5.93083 8.95138 5.84655 8.95216 5.76157C8.95294 5.67659 8.97063 5.59262 9.00421 5.51455C9.03779 5.43649 9.08658 5.36589 9.14773 5.30688L10.6155 3.84H10.0267C9.85693 3.84 9.69414 3.77257 9.57412 3.65255C9.4541 3.53252 9.38667 3.36974 9.38667 3.2C9.38667 3.03026 9.4541 2.86748 9.57412 2.74745C9.69414 2.62743 9.85693 2.56 10.0267 2.56H12.16C12.3297 2.56015 12.4924 2.62768 12.6123 2.74773ZM4.26667 10.4533C4.26667 10.6231 4.3341 10.7859 4.45412 10.9059C4.57414 11.0259 4.73693 11.0933 4.90667 11.0933H7.04085C7.21059 11.0933 7.37338 11.0259 7.4934 10.9059C7.61343 10.7859 7.68085 10.6231 7.68085 10.4533C7.68085 10.2836 7.61343 10.1208 7.4934 10.0008C7.37338 9.88076 7.21059 9.81333 7.04085 9.81333H6.45205L7.91979 8.3456C8.03631 8.22484 8.10075 8.06314 8.09921 7.89534C8.09767 7.72753 8.03029 7.56704 7.91157 7.44844C7.79285 7.32983 7.6323 7.2626 7.46449 7.26122C7.29669 7.25984 7.13505 7.32443 7.0144 7.44107L5.54667 8.90709V8.31829C5.54667 8.14855 5.47924 7.98577 5.35921 7.86574C5.23919 7.74572 5.07641 7.67829 4.90667 7.67829C4.73693 7.67829 4.57414 7.74572 4.45412 7.86574C4.3341 7.98577 4.26667 8.14855 4.26667 8.31829V10.4516V10.4533ZM0 2.34667C0 1.72429 0.247237 1.12741 0.687323 0.687323C1.12741 0.247237 1.72429 0 2.34667 0H14.72C15.3424 0 15.9393 0.247237 16.3793 0.687323C16.8194 1.12741 17.0667 1.72429 17.0667 2.34667V11.3067C17.0667 11.6148 17.006 11.92 16.888 12.2047C16.7701 12.4894 16.5973 12.7481 16.3793 12.966C16.1614 13.1839 15.9027 13.3568 15.618 13.4747C15.3333 13.5926 15.0282 13.6533 14.72 13.6533H2.34667C2.0385 13.6533 1.73335 13.5926 1.44864 13.4747C1.16393 13.3568 0.905231 13.1839 0.687323 12.966C0.469415 12.7481 0.29656 12.4894 0.178629 12.2047C0.0606983 11.92 0 11.6148 0 11.3067V2.34667ZM2.34667 1.28C1.75787 1.28 1.28 1.75787 1.28 2.34667V11.3067C1.28 11.8955 1.75787 12.3733 2.34667 12.3733H14.72C15.3088 12.3733 15.7867 11.8955 15.7867 11.3067V2.34667C15.7867 1.75787 15.3088 1.28 14.72 1.28H2.34667Z" fill="#3D4A52"/>
        </svg>`;
    }

    static getAskAIIcon() {
        return `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M3.05051 1C3.40196 1 3.68687 1.28491 3.68687 1.63636V2.41414H4.46465C4.8161 2.41414 5.10101 2.69905 5.10101 3.05051C5.10101 3.40196 4.8161 3.68687 4.46465 3.68687H3.68687V4.46465C3.68687 4.8161 3.40196 5.10101 3.05051 5.10101C2.69905 5.10101 2.41414 4.8161 2.41414 4.46465V3.68687H1.63636C1.28491 3.68687 1 3.40196 1 3.05051C1 2.69905 1.28491 2.41414 1.63636 2.41414H2.41414V1.63636C2.41414 1.28491 2.69905 1 3.05051 1ZM8.70707 1C8.98097 1 9.22414 1.17526 9.31077 1.4351L10.8311 5.99551L14.5871 7.40416C14.8355 7.49731 15 7.73474 15 8C15 8.26526 14.8355 8.50269 14.5871 8.59584L10.8311 10.0045L9.31077 14.5649C9.22414 14.8247 8.98097 15 8.70707 15C8.43317 15 8.19 14.8247 8.10337 14.5649L6.58301 10.0045L2.82704 8.59584C2.57868 8.50269 2.41414 8.26526 2.41414 8C2.41414 7.73474 2.57868 7.49731 2.82704 7.40416L6.58301 5.99551L8.10337 1.4351C8.19 1.17526 8.43317 1 8.70707 1ZM8.70707 3.64846L7.69441 6.68601C7.63368 6.86816 7.49395 7.01316 7.31417 7.08059L4.86268 8L7.31417 8.91941C7.49395 8.98684 7.63368 9.13184 7.69441 9.31399L8.70707 12.3515L9.71974 9.31399C9.78046 9.13184 9.92019 8.98684 10.1 8.91941L12.5515 8L10.1 7.08059C9.92019 7.01316 9.78046 6.86816 9.71974 6.68601L8.70707 3.64846ZM3.75758 10.899C4.10903 10.899 4.39394 11.1839 4.39394 11.5354V12.3131H5.17172C5.52317 12.3131 5.80808 12.598 5.80808 12.9495C5.80808 13.3009 5.52317 13.5859 5.17172 13.5859H4.39394V14.3636C4.39394 14.7151 4.10903 15 3.75758 15C3.40612 15 3.12121 14.7151 3.12121 14.3636V13.5859H2.34343C1.99198 13.5859 1.70707 13.3009 1.70707 12.9495C1.70707 12.598 1.99198 12.3131 2.34343 12.3131H3.12121V11.5354C3.12121 11.1839 3.40612 10.899 3.75758 10.899Z" fill="#A8A8AC"/>
        </svg>`;
    }

    static getCheckIcon() {
        return `<svg width="10" height="9" viewBox="0 0 10 9" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M9.71648 2.29085C9.80483 2.20075 9.87534 2.09336 9.92397 1.97478C9.97261 1.85621 9.99842 1.72879 9.99993 1.5998C10.0014 1.4708 9.97863 1.34276 9.93279 1.22298C9.88695 1.1032 9.81898 0.994029 9.73277 0.901698C9.64655 0.809368 9.54378 0.735686 9.43032 0.684862C9.31686 0.634038 9.19493 0.607066 9.07149 0.605485C8.94805 0.603905 8.82553 0.627747 8.71091 0.675651C8.59629 0.723554 8.49182 0.794582 8.40347 0.884678L3.3594 6.03279L1.58157 4.16706C1.40376 3.99324 1.16833 3.89833 0.924875 3.90233C0.681424 3.90633 0.448965 4.00893 0.276473 4.18851C0.103982 4.36809 0.00492736 4.61063 0.000178917 4.86502C-0.00456953 5.11942 0.0853584 5.36581 0.251017 5.55228L2.68535 8.10539C2.85995 8.28869 3.0967 8.3926 3.34418 8.39456C3.59166 8.39652 3.82989 8.29637 4.00713 8.11586L9.71648 2.29085Z" fill="#6765E8"/>
        </svg>`;
    }

    static getContinueWritingIcon() {
        return `<svg width="10" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9.40909 0.5H2.59091C1.71227 0.5 1 1.17157 1 2V5C1 5.82843 1.71227 6.5 2.59091 6.5H9.40909C10.2877 6.5 11 5.82843 11 5V2C11 1.17157 10.2877 0.5 9.40909 0.5Z" stroke="#6765E8" stroke-width="0.5"/>
        <path d="M5.16699 2.16699V4.83366" stroke="#6765E8" stroke-width="0.5" stroke-linecap="round"/>
        <path d="M4.12357 4.8333C3.8969 4.8333 3.72677 4.77463 3.6121 4.65783C3.5001 4.53836 3.4441 4.3645 3.4441 4.13516V3.2877H3.14917C3.13737 3.28791 3.12565 3.2857 3.11473 3.2812C3.10381 3.27671 3.09394 3.27002 3.0857 3.26156C3.06887 3.24323 3.05954 3.21925 3.05957 3.19436V2.98156C3.05957 2.9565 3.0681 2.9357 3.0857 2.9181C3.09394 2.90964 3.10381 2.90295 3.11473 2.89846C3.12565 2.89396 3.13737 2.89175 3.14917 2.89196H3.4441V2.27223C3.4441 2.24503 3.45264 2.2237 3.47024 2.20876C3.47847 2.2003 3.48835 2.19362 3.49926 2.18912C3.51018 2.18463 3.5219 2.18242 3.5337 2.18263H3.8361C3.8633 2.18263 3.88464 2.19116 3.89957 2.20876C3.90842 2.21669 3.91537 2.22652 3.9199 2.23751C3.92442 2.2485 3.92641 2.26037 3.9257 2.27223V2.89196H4.39237C4.41957 2.89196 4.4409 2.9005 4.45584 2.9181C4.46469 2.92603 4.47164 2.93585 4.47616 2.94684C4.48069 2.95783 4.48267 2.9697 4.48197 2.98156V3.19436C4.48244 3.20676 4.48037 3.21912 4.47587 3.23067C4.47138 3.24223 4.46456 3.25275 4.45584 3.26156C4.4476 3.27002 4.43773 3.27671 4.42681 3.2812C4.41589 3.2857 4.40417 3.28791 4.39237 3.2877H3.9257V4.09783C3.9257 4.2045 3.94437 4.28556 3.9817 4.3405C4.01904 4.39543 4.08144 4.42263 4.16837 4.42263H4.42597C4.45104 4.42263 4.47184 4.43116 4.48944 4.44876C4.4979 4.457 4.50458 4.46688 4.50908 4.47779C4.51357 4.48871 4.51578 4.50043 4.51557 4.51223V4.7437C4.51578 4.7555 4.51357 4.76722 4.50908 4.77814C4.50458 4.78905 4.4979 4.79893 4.48944 4.80716C4.48151 4.81602 4.47168 4.82297 4.46069 4.82749C4.4497 4.83202 4.43783 4.834 4.42597 4.8333H4.12357Z" fill="#6765E8"/>
        </svg>`;
    }

    static getMakeLongerIcon() {
        return `<svg width="10" height="9" viewBox="0 0 10 9" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect y="0.818182" width="10" height="1.09091" rx="0.545455" fill="#6765E8"/>
        <rect y="2.90909" width="10" height="1.09091" rx="0.545455" fill="#6765E8"/>
        <rect y="5" width="10" height="1.09091" rx="0.545455" fill="#6765E8"/>
        <rect y="7.09091" width="6" height="1.09091" rx="0.545455" fill="#6765E8"/>
        </svg>`;
    }

    static getMakeShorterIcon() {
        return `<svg width="10" height="5" viewBox="0 0 10 5" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect y="0.909092" width="10" height="1.09091" rx="0.545455" fill="#6765E8"/>
        <rect y="3" width="6" height="1.09091" rx="0.545455" fill="#6765E8"/>
        </svg>`;
    }

    static getTryAgainIcon() {
        return `<svg width="10" height="11" viewBox="0 0 10 11" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M3.69055 0.674716C4.67386 0.407719 5.71526 0.447512 6.67532 0.788767C7.63538 1.13002 8.46828 1.75645 9.06249 2.58417V1.59351C9.06249 1.46918 9.11188 1.34995 9.19978 1.26204C9.28769 1.17412 9.40692 1.12474 9.53124 1.12474C9.65557 1.12474 9.7748 1.17412 9.8627 1.26204C9.95061 1.34995 10 1.46918 10 1.59351V4.24987H7.34372C7.2194 4.24987 7.10017 4.20048 7.01226 4.11257C6.92435 4.02466 6.87496 3.90543 6.87496 3.7811C6.87496 3.65678 6.92435 3.53754 7.01226 3.44963C7.10017 3.36172 7.2194 3.31233 7.34372 3.31233H8.42311C7.93352 2.54635 7.20118 1.96672 6.34325 1.66615C5.48531 1.36559 4.55137 1.36147 3.69081 1.65444C2.83026 1.94742 2.09283 2.52057 1.5965 3.28219C1.10017 4.04382 0.873614 4.94991 0.953083 5.85551C1.03255 6.76112 1.41345 7.6139 2.03484 8.27744C2.65623 8.94097 3.48221 9.37692 4.38064 9.51553C5.27906 9.65415 6.19802 9.48742 6.99049 9.04202C7.78296 8.59662 8.40316 7.89829 8.75186 7.05875C8.7745 7.00054 8.80853 6.94745 8.85197 6.90258C8.89541 6.85771 8.94738 6.82198 9.00482 6.79747C9.06226 6.77297 9.12402 6.76019 9.18647 6.75989C9.24892 6.75959 9.3108 6.77177 9.36848 6.79571C9.42615 6.81966 9.47846 6.85489 9.52234 6.89933C9.56621 6.94378 9.60076 6.99654 9.62396 7.05452C9.64715 7.11251 9.65853 7.17454 9.65742 7.23698C9.65631 7.29942 9.64274 7.36101 9.6175 7.41814C9.29175 8.20226 8.77169 8.8905 8.10636 9.418C7.44102 9.94551 6.65231 10.2949 5.81459 10.4332C4.97686 10.5715 4.11771 10.4942 3.31813 10.2086C2.51854 9.92299 1.80485 9.43845 1.24434 8.80067C0.683831 8.16289 0.29496 7.39287 0.114386 6.5632C-0.0661888 5.73353 -0.032519 4.87154 0.212222 4.05848C0.456962 3.24543 0.904711 2.50809 1.51326 1.91597C2.12181 1.32386 2.87112 0.896467 3.69055 0.674091V0.674716Z" fill="#6765E8"/>
        </svg>`;
    }

    static getSummarizeIcon() {
        return `<svg width="10" height="5" viewBox="0 0 10 5" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M0.114411 2.18441C0.041155 2.26813 0 2.38168 0 2.50008C0 2.61848 0.041155 2.73203 0.114411 2.81575C0.187668 2.89947 0.287025 2.94651 0.390625 2.94651H3.35391L2.45781 3.96972C2.38446 4.05355 2.34326 4.16725 2.34326 4.2858C2.34326 4.40435 2.38446 4.51804 2.45781 4.60187C2.53116 4.68569 2.63064 4.73279 2.73438 4.73279C2.83811 4.73279 2.93759 4.68569 3.01094 4.60187L4.57344 2.81615C4.60982 2.77468 4.63868 2.72542 4.65837 2.67118C4.67806 2.61695 4.6882 2.5588 4.6882 2.50008C4.6882 2.44136 4.67806 2.38322 4.65837 2.32898C4.63868 2.27474 4.60982 2.22548 4.57344 2.18401L3.01094 0.398295C2.93759 0.314468 2.83811 0.267375 2.73438 0.267375C2.63064 0.267375 2.53116 0.314468 2.45781 0.398295C2.38446 0.482122 2.34326 0.595817 2.34326 0.714366C2.34326 0.832916 2.38446 0.946611 2.45781 1.03044L3.35391 2.05365H0.390625C0.287025 2.05365 0.187668 2.10069 0.114411 2.18441Z" fill="#6765E8"/>
        <path d="M7.54219 3.96972L6.64609 2.94651H9.60938C9.71298 2.94651 9.81233 2.89947 9.88559 2.81575C9.95885 2.73203 10 2.61848 10 2.50008C10 2.38168 9.95885 2.26813 9.88559 2.18441C9.81233 2.10069 9.71298 2.05365 9.60938 2.05365H6.64609L7.54219 1.03044C7.61554 0.946611 7.65674 0.832916 7.65674 0.714366C7.65674 0.595817 7.61554 0.482122 7.54219 0.398295C7.46884 0.314468 7.36936 0.267375 7.26562 0.267375C7.16189 0.267375 7.06241 0.314468 6.98906 0.398295L5.42656 2.18401C5.39018 2.22548 5.36132 2.27474 5.34163 2.32898C5.32194 2.38322 5.3118 2.44136 5.3118 2.50008C5.3118 2.5588 5.32194 2.61695 5.34163 2.67118C5.36132 2.72542 5.39018 2.77468 5.42656 2.81615L6.98906 4.60187C7.06241 4.68569 7.16189 4.73279 7.26562 4.73279C7.36936 4.73279 7.46884 4.68569 7.54219 4.60187C7.61554 4.51804 7.65674 4.40435 7.65674 4.2858C7.65674 4.16725 7.61554 4.05355 7.54219 3.96972Z" fill="#6765E8"/>
        </svg>`;
    }

    static getImproveWritingIcon() {
        return `<svg width="12" height="11" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M2.11111 0.5V2.72222V0.5ZM1 1.61111H3.22222H1ZM2.66667 8.27778V10.5V8.27778ZM1.55556 9.38889H3.77778H1.55556ZM6.55556 0.5L7.82556 4.30944L11 5.5L7.82556 6.69056L6.55556 10.5L5.28556 6.69056L2.11111 5.5L5.28556 4.30944L6.55556 0.5Z" stroke="#6765E8" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>`;
    }

}