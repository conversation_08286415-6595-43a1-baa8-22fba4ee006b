import ExperienceBuilderIcons from "../../../../views/experienceBuilderIcons";
import ExperienceBuilderViews from "../../../../views/experienceBuilderViews";
import TemplateIcons from "../../../views/templateIcons";
import Views from "../../wysiwigViews";
import { EMBED_OPTIONS_ARRAY } from "./constants";

export default class Embeds {
    DEFAULT_OPTIONS = {
        onSelect: () => {},
        onBackIconClick: () => {},
    }

    constructor(options) {
        this.options = { ...this.DEFAULT_OPTIONS, ...options };

        this.componentEl = ExperienceBuilderViews.getViewElement("embeds-options-menu", {
            backArrowIcon: ExperienceBuilderIcons.getBackArrowIcon(),
        });

        this.init();
    }

    init = () => {
        const embedOptionContainer = this.componentEl.querySelector(".embeds-options-container");
        EMBED_OPTIONS_ARRAY.forEach(option => {
            const embedOptionEl = ExperienceBuilderViews.getViewElement("embed-option", {
                alt: `${option.label} Logo`,
                lightLogo: TemplateIcons.getEmbedLogo(option.type, false),
                darkLogo: TemplateIcons.getEmbedLogo(option.type, true),
                type: option.type,
                discountLabel: option.discountLabel ? TemplateIcons.getDiscountLabelIcon() : "",
                embedOptionLink: option.discountLabel ? 
                    ExperienceBuilderViews.getView("embed-option-link", { externalLinkIcon: TemplateIcons.getExternalLinkIcon(), link: option.discountLabel.link }) 
                    : "",
            });

            embedOptionEl.addEventListener("click", this.options.onSelect);
            embedOptionContainer.appendChild(embedOptionEl);

            if(option.discountLabel) embedOptionEl.querySelector("a.embed-option-link").addEventListener("click", this.stopPropagation);
        });

        embedOptionContainer.appendChild(Views.getViewElement("suggest-embed-section"));


        this.componentEl.querySelector(".header .arrow-icon").addEventListener("click", this.options.onBackIconClick);
    }

    stopPropagation = (e) => {
        e.stopPropagation();
    }
}