import tippy from "tippy.js";
import Common from "../../../..";
import App from "../../../../components/app";
import SelectMenu from "../../../../generic-components/selectMenu";
import ToggleCheck from "../../../../generic-components/toggleCheck";
import URLInput from "../../../../generic-components/urlInput";
import htmlTreeOperations from "../../../../generic-utils/htmlTreeOperations";
import { Store } from "../../../../store/store";
import ExperienceBuilderViews from "../../../../views/experienceBuilderViews";
import Views from "../../wysiwigViews";

export const ACTION_TYPES = {
    NO_ACTION: {
        label: "No Action",
        value: "no-action",
    },
    NEXT_FLOW: {
        label: "Go to Next Step",
        value: "next-flow",
    },
    GO_TO_URL: {
        label: "Go to URL",
        value: "go-to-url",
    },
    PREV_FLOW: {
        label: "Go to Previous Step",
        value: "prev-flow",
    },
    SKIP_TO_STEP: {
        label: "Go to a Step",
        value: "skip-to-step",
    },
    FLOW: {
        label: "Trigger Flow",
        value: "flow",
    },
    SURVEY: {
        label: "Trigger Survey",
        value: "survey",
    },
    JSCALLBACK: {
        label: "Trigger Javascript Function",
        value: "jscallback",
    },
    DIS_GRP: {
        label: "Skip to Next Group",
        value: "dis-grp",
    },
    CLOSE_TUT_FLOW: {
        label: "Dismiss Flow",
        value: "close-tut",
    },
    CLOSE_TUT_SPOTLIGHT: {
        label: "Dismiss",
        value: "close-tut",
    },
}

export default class Action {
    constructor(actionEl, sectionContainer, container, actionSelectBtn, selectMenuItems, selectMenuOptions) {
        this.actionEl = actionEl;
        this.sectionContainer = sectionContainer;
        this.container = container;
        this.selectMenuItems = selectMenuItems;

        this.selectMenu = new SelectMenu(actionSelectBtn, SelectMenu.generateSelectMenuItems(selectMenuItems), (select) => {
            const value = select.getAttribute("value");
            this.selectMenu.setValue(value);
            this.container.setAttribute("state", this.selectMenu.selectedValue);
            this.changeActionSelection(this.selectMenu.selectedValue, this.selectMenu);
        }, "", "", true, {
            autoRepositionOnResize: true,
            container: this.container
        }, {
            ...selectMenuOptions
        });

        this.init();
    }

    init() {
        this.isImageElement = this.sectionContainer.classList.contains("userpilot-image-section");
        this.selectMenuItems.forEach(option => {
            switch(option.value) {
                case ACTION_TYPES.GO_TO_URL.value:
                    this.initUrlActionEvents(this.container.querySelector(".go-to-url-options"), this.container);
                    break;
                case ACTION_TYPES.SKIP_TO_STEP.value:
                    this.initSkipToStepEvents(this.container.querySelector(".skip-to-step-options"), this.container);
                    break;
                case ACTION_TYPES.FLOW.value:
                    this.initTriggerFlowEvents(this.container.querySelector(".flow-options"), this.container);
                    break;
                case ACTION_TYPES.SURVEY.value:
                    this.initTriggerSurveyEvents(this.container.querySelector(".survey-options"), this.container);
                    break;
                case ACTION_TYPES.JSCALLBACK.value:
                    this.initTriggerJsFunctionEvents(this.container.querySelector(".jsCallback-options"));
                    break;
                case ACTION_TYPES.CLOSE_TUT_FLOW.value:
                    this.initDismissExperienceEvents(this.container.querySelector(".dis-exp-options"));
                    break;
            }
        })
    }

    /* BUTTON ACTIONS */

    changeActionSelection(value, selectMenu) {
        // The cases below are the only select options that immediately change the action behavior. Others need to click on submit button
        // this is why other types of actions are absent
        switch (value) {
            case "no-action":
                this.clearImageActions();
                this.actionEl.setAttribute("userpilot-act", ACTION_TYPES.NO_ACTION.value);
                selectMenu.setValue(ACTION_TYPES.NO_ACTION.value);
                break;
            case "next-flow":
            case "prev-flow":
            case "dis-grp":
                this.clearButtonAttributes();
                this.actionEl.setAttribute("userpilot-act", value);
                selectMenu.setValue(value);
                break;
            case "close-tut":
                this.clearButtonAttributes();
                this.actionEl.setAttribute("userpilot-act", "close-tut");
                selectMenu.setValue(value);
                this.dismissExpProps.selectMenu.setValue(value);
            case "jscallback":
                if(this.actionEl.getAttribute("userpilot-complete-exp") == null) this.actionEl.setAttribute("userpilot-complete-exp", "1");
                break;
            case "flow":
                this.isImageElement ? this.clearImageActions() : this.clearButtonAttributes();
                const flowActionAttribute = this.isImageElement ? "userpilot-action" : "userpilot-btn-action";
                this.actionEl.setAttribute(flowActionAttribute, value);
                selectMenu.setValue(value);
                this.initTriggerFlowData(selectMenu.popupOptions.container);
                break;
            case ACTION_TYPES.SURVEY.value:
                this.isImageElement ? this.clearImageActions() : this.clearButtonAttributes();
                const surveyActionAttribute = this.isImageElement ? "userpilot-action" : "userpilot-btn-action";
                this.actionEl.setAttribute(surveyActionAttribute, value);
                selectMenu.setValue(value);
                this.initTriggerSurveyData(selectMenu.popupOptions.container);
                break;
            case "skip-to-step":
                this.clearButtonAttributes();
                this.actionEl.setAttribute("userpilot-act", value);
                selectMenu.setValue(value);
                this.actionEl.setAttribute("userpilot-skip-to", 1);
                this.skipToStepProps.input.value = 1;
                break;
            case "go-to-url":
                this.initGoToUrlData(selectMenu.popupOptions.container);
                break;
            default:
                break;
        }
    }

    setBtnActionPopupState(selectMenu) {
        // I don't know why the button action in some actions are set with userpilot-act and others with userpilot-btn-action
        // I will not mess with this to not mess with backwards compatibility and SDK functionality
        const btnAction = this.actionEl.getAttribute("userpilot-act");
        const btnAction2 = this.actionEl.getAttribute("userpilot-btn-action") || "";
        const btnAction3 = this.actionEl.getAttribute("userpilot-action") || ""; // used in image-section for flow
        
        const isImageGoToUrlOption = this.isImageElement && !btnAction3 && !btnAction;

        const aggregatedAction = ((/http|localhost/gi.test(btnAction2) || isImageGoToUrlOption) ? "go-to-url" : btnAction || btnAction2 || btnAction3);

        if (aggregatedAction.includes("close-tut") || aggregatedAction.includes("dismiss")) selectMenu.setValue("close-tut")
        else selectMenu.setValue(aggregatedAction);
        this.container.setAttribute("state", aggregatedAction);

        switch (aggregatedAction) {
            case "go-to-url":
                if(this.isImageElement) break;

                this.urlActionProps.urlInstance.setUrl(this.actionEl.getAttribute("userpilot-btn-action"), this.actionEl.getAttribute("target") == "_BLANK");

                if(!this.urlActionProps.dismissExpCheck) break;
                (this.actionEl.getAttribute("up-dismiss-experience") == "1") ? this.urlActionProps.dismissExpCheck.checked = true : this.urlActionProps.dismissExpCheck.checked = false;
                break;

            case "skip-to-step":
                this.skipToStepProps.input.value = this.actionEl.getAttribute("userpilot-skip-to");
                break;

            case "flow":
                this.initTriggerFlowData();
                const flowId = this.actionEl.getAttribute(this.getAttributeNameFlowId());
                const flow = this.triggerFlowProps.flowSelectMenu?.selectElements.find(element => flowId === element.getAttribute("value"));
                
                if(flow) {
                    this.triggerFlowProps.flowSelectMenu?.setValue(flowId);
                } else {
                    const experience = Store.getComponentState(App).experience;
                    const buttonAction = this.isImageElement ? ACTION_TYPES.NO_ACTION.value : (experience.settings.type === "flow") ? "next-flow" : "close-tut";
                    this.container.setAttribute("state", buttonAction);
                    this.changeActionSelection(buttonAction, selectMenu);
                }

                break;

            case ACTION_TYPES.SURVEY.value:
                this.initTriggerSurveyData();
                const surveyId = this.actionEl.getAttribute("survey-id");
                const survey = this.triggerSurveyProps.surveySelectMenu?.selectElements.find(element => surveyId === element.getAttribute("value"));
                
                if(survey) {
                    this.triggerSurveyProps.surveySelectMenu?.setValue(surveyId);
                } else {
                    const buttonAction = this.isImageElement ? ACTION_TYPES.NO_ACTION.value : "next-flow";
                    this.container.setAttribute("state", buttonAction);
                    this.changeActionSelection(buttonAction, selectMenu);
                }

                break;
            case "jscallback":
                this.jsCallbackProps.textArea.textContent = this.actionEl.getAttribute("jscallback");
                break;
            default:
                break;
        }
    }

    urlActionProps = {
        urlInstance: null,
        dismissExpCheck: null,
    }
    initUrlActionEvents(optionsContainer) {
        const onProtocolChange = () => this.urlActionProps.urlInstance.getURL() && this.actionEl.setAttribute("userpilot-btn-action", this.urlActionProps.urlInstance.getFullURL());
        this.urlActionProps.urlInstance = new URLInput(optionsContainer.querySelector(".url-section-v2"), {
            onNewTabChangeCallback: this.initGoToUrlData,
            onUrlChangeCallback: this.initGoToUrlData,
            onProtocolChangeCallback: onProtocolChange,
            propertiesContainer: this.container,
        });
        this.urlActionProps.dismissExpCheck = optionsContainer.querySelector("#mark-complete");

        this.urlActionProps.dismissExpCheck && this.urlActionProps.dismissExpCheck.addEventListener("input", () => {
            if(this.urlActionProps.urlInstance.getURL())
                (this.urlActionProps.dismissExpCheck.checked) ? this.actionEl.setAttribute("up-dismiss-experience", "1") : this.actionEl.removeAttribute("up-dismiss-experience");
        });
    }

    initGoToUrlData = () => {
        if(!this.urlActionProps.urlInstance.getURL()) return;

        if(this.isImageElement) {
            this.clearImageActions();
            this.handleUrlChangeForImageSection(this.urlActionProps.urlInstance);
        } else {
            this.clearButtonAttributes();
            this.handleUrlChange();
            this.handleUrlNewTabChange(this.urlActionProps.urlInstance.isNewTab());
            (this.urlActionProps.dismissExpCheck?.checked) ? this.actionEl.setAttribute("up-dismiss-experience", "1") : this.actionEl.removeAttribute("up-dismiss-experience");
        }
    }

    handleUrlChange() {
        this.actionEl.setAttribute("userpilot-btn-action", this.urlActionProps.urlInstance.getFullURL());
    }

    handleUrlNewTabChange(isChecked) {
        (isChecked) ? this.actionEl.setAttribute("target", "_BLANK") : this.actionEl.removeAttribute("target");
    }

    skipToStepProps = {
        input: null,
    }
    initSkipToStepEvents(optionsContainer) {
        this.skipToStepProps.input = optionsContainer.querySelector("#step-input");
        this.skipToStepProps.input.addEventListener("change", (e) => this.actionEl.setAttribute("userpilot-skip-to", e.target.value));
    }

    triggerFlowProps = {
        navigateUrlToggle: null,
        urlInstance: null,
        flowSelectMenu: null,
    }
    initTriggerFlowEvents(optionsContainer) {
        const btnAction = this.actionEl.getAttribute("userpilot-btn-action") || this.actionEl.getAttribute("userpilot-action");
        const onProtocolChange = () => this.triggerFlowProps.urlInstance.getURL() && this.actionEl.setAttribute("userpilot-btn-url", this.triggerFlowProps.urlInstance.getFullURL());
        const onChange = () => this.isImageElement ? this.handleFlowUrlChangeForImageSection(this.triggerFlowProps.urlInstance) : this.actionEl.setAttribute("userpilot-btn-url", this.triggerFlowProps.urlInstance.getFullURL());
        const urlSection = optionsContainer.querySelector(".url-section-v2");
        this.triggerFlowProps.urlInstance = new URLInput(urlSection, {
            protocol: URLInput.protocols.HTTPS,
            showNewTabOption: false,
            onUrlChangeCallback: onChange,
            onProtocolChangeCallback: onProtocolChange,
            propertiesContainer: this.container,
        });
        !this.isImageElement && btnAction === ACTION_TYPES.FLOW.value && this.triggerFlowProps.urlInstance.setUrl(this.actionEl.getAttribute("userpilot-btn-url"), false);

        const toggleCheckInput = optionsContainer.querySelector("#navigate-to-url");
        this.triggerFlowProps.navigateUrlToggle = this.initNavigateUrlToggle(ACTION_TYPES.FLOW.value, toggleCheckInput, optionsContainer, this.triggerFlowProps.urlInstance);

        const flowSelectBtn = optionsContainer.querySelector("#flows-list");
        const flowsSelectElements = ExperienceBuilderViews.getFlowsSelectOptions(true);
        if (flowsSelectElements.length == 0) flowSelectBtn.textContent = "No Flows Available";
        else {
            const currentSelectedFlow = this.actionEl.getAttribute(this.getAttributeNameFlowId()) || flowsSelectElements[0].getAttribute("value");
            const onFlowSelect = () => this.actionEl.setAttribute(this.getAttributeNameFlowId(), flowSelectBtn.getAttribute("value"));
            this.triggerFlowProps.flowSelectMenu = new SelectMenu(flowSelectBtn, flowsSelectElements, onFlowSelect, currentSelectedFlow,
                "", false, { container: this.container }, { searchable: true });
        }
    }

    initTriggerFlowData() {
        const flowSelectBtn = this.container.querySelector("#flows-list");
        this.actionEl.setAttribute(this.getAttributeNameFlowId(), flowSelectBtn.getAttribute("value"));

        if(this.triggerFlowProps.urlInstance.getURL())
            this.isImageElement ? this.handleFlowUrlChangeForImageSection(this.triggerFlowProps.urlInstance) : this.actionEl.setAttribute("userpilot-btn-url", this.triggerFlowProps.urlInstance.getFullURL());
    }

    triggerSurveyProps = {
        navigateUrlToggle: null,
        urlInstance: null,
        surveySelectMenu: null,
    }
    initTriggerSurveyEvents(optionsContainer) {
        const btnAction = this.actionEl.getAttribute("userpilot-btn-action") || this.actionEl.getAttribute("userpilot-action");
        const onProtocolChange = () => this.triggerSurveyProps.urlInstance.getURL() && this.actionEl.setAttribute("userpilot-btn-url", this.triggerSurveyProps.urlInstance.getFullURL());
        const onChange = () => this.isImageElement ? this.handleFlowUrlChangeForImageSection(this.triggerSurveyProps.urlInstance) : this.actionEl.setAttribute("userpilot-btn-url", this.triggerSurveyProps.urlInstance.getFullURL());
        const urlSection = optionsContainer.querySelector(".url-section-v2");
        this.triggerSurveyProps.urlInstance = new URLInput(urlSection, {
            protocol: URLInput.protocols.HTTPS,
            showNewTabOption: false,
            onUrlChangeCallback: onChange,
            onProtocolChangeCallback: onProtocolChange,
            propertiesContainer: this.container,
        });
        !this.isImageElement && btnAction === ACTION_TYPES.SURVEY.value && this.triggerSurveyProps.urlInstance.setUrl(this.actionEl.getAttribute("userpilot-btn-url"), false);

        const toggleCheckInput = optionsContainer.querySelector("#trigger-survey-navigate-to-url");
        this.triggerSurveyProps.navigateUrlToggle = this.initNavigateUrlToggle(ACTION_TYPES.SURVEY.value, toggleCheckInput, optionsContainer, this.triggerSurveyProps.urlInstance);

        const surveySelectBtn = optionsContainer.querySelector("#surveys-list");
        const surveysSelectElements = ExperienceBuilderViews.getSurveysSelectOptions(true);
        if (surveysSelectElements.length == 0) surveySelectBtn.textContent = "No Surveys Available";
        else {
            const currentSelectedSurvey = this.actionEl.getAttribute("survey-id") || surveysSelectElements[0].getAttribute("value");
            const onSurveySelect = () => this.actionEl.setAttribute("survey-id", surveySelectBtn.getAttribute("value"));
            this.triggerSurveyProps.surveySelectMenu = new SelectMenu(surveySelectBtn, surveysSelectElements, onSurveySelect, currentSelectedSurvey,
                "", false, { container: this.container }, { searchable: true });
        }
    }

    initTriggerSurveyData() {
        const surveySelectBtn = this.container.querySelector("#surveys-list");
        this.actionEl.setAttribute("survey-id", surveySelectBtn.getAttribute("value"));

        if(this.triggerSurveyProps.urlInstance.getURL())
            this.isImageElement ? this.handleFlowUrlChangeForImageSection(this.triggerSurveyProps.urlInstance) : this.actionEl.setAttribute("userpilot-btn-url", this.triggerSurveyProps.urlInstance.getFullURL());
    }

    initNavigateUrlToggle(actionType, toggleInput, optionsContainer, urlInstance) {
        const isFlowSelected = () => {
            const btnAction = this.actionEl.getAttribute("userpilot-btn-action") || this.actionEl.getAttribute("userpilot-action");
            return btnAction == ACTION_TYPES.FLOW.value && btnAction === actionType;
        }
        const isSurveySelected = () => {
            const btnAction = this.actionEl.getAttribute("userpilot-btn-action") || this.actionEl.getAttribute("userpilot-action");
            return btnAction === ACTION_TYPES.SURVEY.value && btnAction === actionType;
        }
        const setInitialState = () => {
            const isImageAnchor = this.isImageElement && this.actionEl.parentElement.tagName === "A";
            return ((isFlowSelected() || isSurveySelected()) && (isImageAnchor || this.actionEl.getAttribute("userpilot-btn-url")));
        }
        const checkedCallback = () => {
            optionsContainer.setAttribute("navigate-url", "on");
            !this.isImageElement && urlInstance.getURL() && this.actionEl.setAttribute("userpilot-btn-url", urlInstance.getFullURL())
        }
        const uncheckedCallback = () => {
            const isFlowOrSurveySelected = isFlowSelected() || isSurveySelected();
            optionsContainer.removeAttribute("navigate-url", "on");
            this.isImageElement ? (isFlowOrSurveySelected && this.clearImageUrlAction()) : (isFlowOrSurveySelected && this.actionEl.removeAttribute("userpilot-btn-url"));
        }
        return new ToggleCheck(toggleInput, setInitialState, checkedCallback, uncheckedCallback);
    }

    jsCallbackProps = {
        textArea: null
    }
    initTriggerJsFunctionEvents(optionsContainer) {
        this.jsCallbackProps.textArea = optionsContainer.querySelector(".jsCallback-options .userpilot-text-area");
        this.jsCallbackProps.textArea.addEventListener("input", (e) => {
            const completeExpToggleValue = this.actionEl.getAttribute("userpilot-complete-exp");
            this.clearButtonAttributes();
            this.actionEl.setAttribute("jscallback", e.target.value);
            this.actionEl.setAttribute("userpilot-act", "jscallback");
            this.actionEl.setAttribute("userpilot-complete-exp", completeExpToggleValue);
        });

        const markCompleteValue = this.actionEl.getAttribute("userpilot-complete-exp");
        const onCheckedCallback = () => this.actionEl.setAttribute("userpilot-complete-exp", "1");
        const onUncheckedCallback = () => this.actionEl.setAttribute("userpilot-complete-exp", "0");

        const toggleCheckInput = optionsContainer.querySelector("#dimiss-complete-flow");
        if(toggleCheckInput) new ToggleCheck(toggleCheckInput, () => (parseInt(markCompleteValue) ?? "1"), onCheckedCallback, onUncheckedCallback);
    }

    dismissExpProps = {
        selectMenu: null,
    }
    initDismissExperienceEvents(optionsContainer) {
        const additionalActionsList = optionsContainer.querySelector("#additional-actions-list");
        const onSelect = (selectedElement) => this.actionEl.setAttribute("userpilot-act", selectedElement.getAttribute("value"));
        this.dismissExpProps.selectMenu = new SelectMenu(additionalActionsList, null, onSelect, this.actionEl.getAttribute("userpilot-act"), "", false, { container: this.container });

        this.dismissExpProps.selectMenu.selectElements.forEach(selectElement => {
            const tooltipText = selectElement.getAttribute("tooltip-content");
            if(!tooltipText) return;

            const tippyContent = Views.getViewElement("tippy-tooltip", { id: "dismiss-additional-actions-tooltip", content: tooltipText });
            tippy(selectElement, { appendTo: Common.shadowRoot, placement: 'right', content: tippyContent });
        })
    }

    clearButtonAttributes() {
        this.actionEl.removeAttribute("id");
        this.actionEl.removeAttribute("userpilot-act");
        this.actionEl.removeAttribute("userpilot-btn-action");
        this.actionEl.removeAttribute("userpilot-complete-exp");
        this.actionEl.removeAttribute("target");
        this.actionEl.removeAttribute("up-dismiss-experience");
        this.actionEl.removeAttribute("userpilot-btn-url");
        this.actionEl.removeAttribute("userpilot-btn-selected");
        this.actionEl.removeAttribute("userpilot-skip-to");
        this.actionEl.removeAttribute("survey-id");
    }

    getAttributeNameFlowId() { return this.isImageElement ? "flow-id" : "id"; }

    handleFlowUrlChangeForImageSection(urlInstance) {
        this.clearImageUrlAction();
        const linkNode = urlInstance.createAnchorNode();
        htmlTreeOperations.wrapNode(this.actionEl, linkNode);
    }

    handleUrlChangeForImageSection(urlInstance) {
        this.clearImageActions();
        const linkNode = urlInstance.createAnchorNode();
        htmlTreeOperations.wrapNode(this.actionEl, linkNode);
    }

    clearImageActions() {
        this.clearImageUrlAction();
        this.actionEl.removeAttribute("target");
        this.actionEl.removeAttribute("userpilot-act");
        this.actionEl.removeAttribute("userpilot-action");
        this.actionEl.removeAttribute("flow-id");
        this.actionEl.removeAttribute("survey-id");
    }

    clearImageUrlAction() {
        if(this.actionEl.parentElement.tagName == "A") htmlTreeOperations.unwrap(this.actionEl.parentElement);
    }
}
