import htmlTreeOperations from "../../../../generic-utils/htmlTreeOperations";
import Popup from "../../../../generic-utils/popup";
import MessageHandler from "../../../../routing/messageHandler";
import RangeOperations from "../../rangeOperations";
import TextSection from "../../textEditor";
import WysiwygOperations from "../../utils/wysiwygOperations";
import Views from "../../wysiwigViews";
import { ASK_AI_ACTIONS } from "./constants";


const AI_PLACEHOLDERS = {
    ACTIVATED: "Ask AI to write anything (be specific with your prompt)",
    NOT_ACTIVATED: "Press “space” to ask AI to write something",
}

export default class AskAI {
    constructor({
        button,
        sectionEditor,
    }) {
        this.askAIButton = button;
        this.popupToolbar = sectionEditor.popupToolbar;
        this.sectionContainer = sectionEditor.sectionContainer;
        this.iframeDocument = sectionEditor.iframeDocument;
        this.wysiwygOperations = sectionEditor.wysiwygOperations;
        this.isInitialEmpty = false;
        this.initialUserValue = '';
        this.isAIActivated = false;

        this.emptyTextPopup = Views.getViewElement("ask-ai-empty-text-popup");
        this.nonEmptyTextPopup = Views.getViewElement("ask-ai-non-empty-text-popup");
        this.suggestedTextPopup = Views.getViewElement("ask-ai-suggested-text-popup");
        this.suggestionBlock = Views.getViewElement("ask-ai-suggestion-block");
        this.loadingAnimationTemplate = Views.getViewElement("ask-ai-loader-container");
        this.askAIFakeDiv = this.iframeDocument.querySelector("#ask-ai-fake-div") || this.getAskAIFakeDiv();
        this.askAIInsertionPoint = this.getAskAIInsertionPoint();

        this.init();
    }

    init = () => {
        if(this.isEmptySection()) this.sectionContainer.setAttribute("ai-placeholder", AI_PLACEHOLDERS.NOT_ACTIVATED);
        else this.sectionContainer.setAttribute("ai-placeholder", "");
        
        this.initPopups();
        this.initTriggerButton();
    }

    initPopups = () => {
        const askAiPopupEl = document.createElement("div");
        askAiPopupEl.classList.add("ask-ai-popup");
        askAiPopupEl.appendChild(this.emptyTextPopup);
        
        this.iframeDocument.querySelector('.userpilot').appendChild(this.askAIFakeDiv);
        this.popup = new Popup(askAiPopupEl, this.askAIFakeDiv, null, null, {
            autoRepositionOnResize: true,
            container: this.popupToolbar.parentElement,
            positionTo: this.sectionContainer,
            defaultDirection: "bottom-left",
        });
        this.popup.callbackOnPopupClose = () => setTimeout(() => {
            this.popup.isClosedByOutsideClick ? this.saveAndCloseAI() : this.resetState();
        }, 450);

        [this.emptyTextPopup, this.nonEmptyTextPopup, this.suggestedTextPopup].forEach(popup => {
            popup.querySelectorAll(".ask-ai-option")
                .forEach(action => action.addEventListener("click", (e) => this.onActionClicked(e.currentTarget.getAttribute("action")))); 
        });
    }

    initTriggerButton = () => {
        this.askAIButton.addEventListener("click", async () => {
            const isEmptySection = this.isEmptySection();
            this.isAIActivated = true;
            this.isInitialEmpty = isEmptySection;
            this.initialSectionContent = this.sectionContainer.cloneNode(true);

            if(isEmptySection) this.sectionContainer.setAttribute("ai-placeholder", AI_PLACEHOLDERS.ACTIVATED);
            else {
                this.popup.popup.innerHTML = "";
                this.popup.popup.appendChild(this.nonEmptyTextPopup);
                this.popup.spawnPopup();
                this.sectionContainer.setAttribute("contenteditable", false);
            }

            this.popupToolbar.classList.add("display-none");
            this.focusSection();

            this.selection = this.wysiwygOperations.selection.cloneRange();
            if(this.selection.startOffset !== this.selection.endOffset) {
                this.isRange = true;
                this.wysiwygOperations.highlightSelection();
            }

            this.initSuggestionBlockStyles();
        });
    }

    handleKeydown = (e) => {
        const keyPressed = e.key;
        const isSectionEmpty = this.isEmptySection();
        const isSpacePressed = keyPressed === " ";
        const isEnterPressed = keyPressed === "Enter";
        const sendButton = this.sectionContainer.querySelector("#ask-ai-send-button");

        if(keyPressed === "Escape" && this.isAIActivated) return this.discardAndCloseAI();
        if((isEnterPressed && this.isAIActivated) || (isSpacePressed && isSectionEmpty)) e.preventDefault();
        if((isEnterPressed && this.isAIActivated) && sendButton) return sendButton.click(); 
        if(isSpacePressed && isSectionEmpty) return this.askAIButton.click();

        setTimeout(() => {
            if(this.isEmptySection(this.sectionContainer)) {
                this.sectionContainer.setAttribute("ai-placeholder", this.isAIActivated ? AI_PLACEHOLDERS.ACTIVATED : AI_PLACEHOLDERS.NOT_ACTIVATED);
                this.removeSendButton();
            }
            else {
                this.sectionContainer.setAttribute("ai-placeholder", "");
                if(this.isInitialEmpty) this.showSendButton();
            }
        });
    }

    onActionClicked = async (action) => {
        const suppressLoadingState = [ASK_AI_ACTIONS.ACCEPT, ASK_AI_ACTIONS.ACCEPT_AND_REPLACE, ASK_AI_ACTIONS.DISCARD].includes(action);
        if(!suppressLoadingState) this.activateLoadingState();

        const emptyTextBuilderBlock = TextSection.getTextBuilderblock();

        switch(action) {
            case ASK_AI_ACTIONS.ACCEPT:
                htmlTreeOperations.unwrap(this.askAIInsertionPoint);
                this.popup.closePopup();
                this.popupToolbar.classList.remove("display-none");
                this.resetSelectionState();
                this.initialSectionContent = this.sectionContainer.cloneNode(true);
                this.wysiwygOperations.collapseCaretOnNode(this.sectionContainer);
                break; 

            case ASK_AI_ACTIONS.ACCEPT_AND_REPLACE:
                htmlTreeOperations.unwrap(this.askAIInsertionPoint);
                const suggestedText = this.suggestionBlock.innerHTML;
                this.suggestionBlock.remove();

                this.sectionContainer.innerHTML = emptyTextBuilderBlock.outerHTML;
                if(this.isRange) this.sectionContainer.innerHTML = suggestedText;
                else {
                    this.wysiwygOperations.collapseCaretOnNode(this.sectionContainer);
                    const spanTextNode = TextSection.getTextnodeSpan(true);
                    spanTextNode.innerHTML = suggestedText;
                    RangeOperations.replaceSelectionWithNode(this.iframeDocument, this.sectionContainer, this.wysiwygOperations.selection, spanTextNode);
                }

                this.nonEmptyTextPopup.remove();
                this.suggestedTextPopup.remove();
                this.popup.popup.appendChild(this.emptyTextPopup);

                this.resetSelectionState();
                Popup.positionPopup(this.popup.getPositioningPopupArguments());
                break;

            case ASK_AI_ACTIONS.WRITE:
                MessageHandler.postAICompletion({ type: action, text: this.initialUserValue });

                this.sectionContainer.innerHTML = emptyTextBuilderBlock.outerHTML;
                this.appendLoader();
                this.wysiwygOperations.collapseCaretOnNode(this.sectionContainer);
                this.askAIInsertionPoint.innerHTML = '';
                RangeOperations.replaceSelectionWithNode(this.iframeDocument, this.sectionContainer, this.wysiwygOperations.selection, this.askAIInsertionPoint);
                break;

            case ASK_AI_ACTIONS.CONTINUE_WRITING:
                MessageHandler.postAICompletion({ type: action, text: this.getSanitizedHTML(this.sectionContainer) });

                if(this.sectionContainer.contains(this.askAIInsertionPoint)) this.wysiwygOperations.collapseCaretOnNode(this.askAIInsertionPoint);
                else {
                    this.wysiwygOperations.collapseCaretOnNode(this.sectionContainer);
                    RangeOperations.replaceSelectionWithNode(this.iframeDocument, this.sectionContainer, this.wysiwygOperations.selection, this.askAIInsertionPoint);
                }
                break;

            case ASK_AI_ACTIONS.MAKE_LONGER:
            case ASK_AI_ACTIONS.MAKE_SHORTER:
                this.handleMakeLongerShorterAction(action);
                break;

            case ASK_AI_ACTIONS.FIX_SPELLING_AND_GRAMMAR:
            case ASK_AI_ACTIONS.SUMMARIZE:
            case ASK_AI_ACTIONS.IMPROVE_WRITING:
                if(this.isRange) this.handleRangeSelectionAction(action);
                else {
                    MessageHandler.postAICompletion({ type: action, text: this.getSanitizedHTML(this.sectionContainer) });
                    this.suggestionBlock.innerHTML = '';
                    this.askAIInsertionPoint.innerHTML = '';
                    this.wysiwygOperations.collapseCaretOnNode(this.suggestionBlock);
                    RangeOperations.replaceSelectionWithNode(this.iframeDocument, this.sectionContainer, this.wysiwygOperations.selection, this.askAIInsertionPoint);   
                }
                
                this.sectionContainer.appendChild(this.suggestionBlock);
                break;

            case ASK_AI_ACTIONS.TRY_AGAIN:
                if(this.initialUserValue) {
                    MessageHandler.postAICompletion({ type: action, text: this.initialUserValue });
    
                    this.sectionContainer.innerHTML = emptyTextBuilderBlock.outerHTML;
                    this.appendLoader();
                    this.askAIInsertionPoint.innerHTML = '';
                    this.wysiwygOperations.collapseCaretOnNode(this.sectionContainer);
                    RangeOperations.replaceSelectionWithNode(this.iframeDocument, this.sectionContainer, this.wysiwygOperations.selection, this.askAIInsertionPoint);
                } else {
                    MessageHandler.postAICompletion({ type: action, text: this.getSanitizedHTML(this.askAIInsertionPoint) });
                    this.askAIInsertionPoint.innerHTML = '';
                    this.wysiwygOperations.collapseCaretOnNode(this.askAIInsertionPoint);
                }
                break;

            case ASK_AI_ACTIONS.DISCARD:
                this.sectionContainer.innerHTML = this.initialSectionContent.innerHTML;
                this.wysiwygOperations.collapseCaretOnNode(this.sectionContainer);
                
                this.sectionContainer.setAttribute("ai-placeholder", this.isEmptySection() ? AI_PLACEHOLDERS.NOT_ACTIVATED : "");
                this.popup.popupActive && this.popup.closePopup();
                break;

        }
    }

    showSendButton = () => {
        if(this.sectionContainer.querySelector(".ask-ai-send-button-container") || !this.isAIActivated) return;
        
        const sendButtonContainer = Views.getViewElement("ask-ai-send-button-container");
        const sendButton = sendButtonContainer.querySelector("#ask-ai-send-button")
        sendButton.addEventListener("click", () => {
            this.sectionContainer.setAttribute("contenteditable", false);
            sendButtonContainer.remove();
            this.initialUserValue = this.sectionContainer.innerText;
            this.onActionClicked(ASK_AI_ACTIONS.WRITE);
        })

        this.sectionContainer.appendChild(sendButtonContainer);
    }

    resetState = () => {
        this.popupToolbar.classList.remove("display-none");
        this.isAIActivated = false;
        this.initialUserValue = '';
        this.sectionContainer.setAttribute("contenteditable", true);
        this.focusSection();
        this.resetSelectionState();
        this.askAIInsertionPoint.innerHTML = '';
    }

    resetSelectionState = () => {
        this.isRange = false;
        this.selection.collapse();
    }
    
    isEmptySection = () => {
        const clonedSection = this.sectionContainer.cloneNode(true);
        clonedSection.querySelector(".ask-ai-send-button-container")?.remove();
        return WysiwygOperations.isEmptyTextContainer(clonedSection);
    }

    activateLoadingState = () => {
        this.popup.options.autoCloseOnOutsideClick = false;
        this.popup.popup.classList.add("ai-is-loading");
        this.sectionContainer.appendChild(this.loadingAnimationTemplate);
        this.suggestionBlock.classList.add("ai-is-loading");
    }

    deactivateLoadingState = () => {
        this.loadingAnimationTemplate.remove();
        this.suggestionBlock.classList.remove("ai-is-loading");
        this.popup.popup.classList.remove("ai-is-loading");
        this.popup.options.autoCloseOnOutsideClick = true;
    }

    discardAndCloseAI = () => {
        this.onActionClicked(ASK_AI_ACTIONS.DISCARD);
        this.resetState();
    }

    saveAndCloseAI = () => {
        if(this.sectionContainer.contains(this.suggestionBlock)) this.onActionClicked(ASK_AI_ACTIONS.ACCEPT_AND_REPLACE);
        else this.onActionClicked(ASK_AI_ACTIONS.ACCEPT);
        this.resetState();
    }

    getAskAIFakeDiv = () => {
        const fakeDiv = document.createElement("div");
        fakeDiv.id = "ask-ai-fake-div";
        fakeDiv.style.display = "none";
        return fakeDiv;
    }

    getAskAIInsertionPoint = () => {
        const span = document.createElement("span");
        span.classList.add("ai-insertion-point");
        return span;
    }

    initSuggestionBlockStyles = () => {
        const computedStyles = getComputedStyle(this.sectionContainer);
        this.suggestionBlock.style.marginLeft = `${-Math.min(parseInt(computedStyles.paddingLeft), 10)}px`;
        this.suggestionBlock.style.marginRight = `${-Math.min(parseInt(computedStyles.paddingRight), 10)}px`;
    }

    handleMakeLongerShorterAction = (action) => {
        const isSectionContainsSuggestionBlock = this.sectionContainer.contains(this.suggestionBlock);
        const isSectionContainsAIInsertionPoint = this.sectionContainer.contains(this.askAIInsertionPoint);

        switch(true) {
            case this.isRange:
                this.handleRangeSelectionAction(action);
                if(!isSectionContainsSuggestionBlock) (this.sectionContainer.appendChild(this.suggestionBlock));
                break;

            case isSectionContainsSuggestionBlock && isSectionContainsAIInsertionPoint:
            case isSectionContainsAIInsertionPoint:
                MessageHandler.postAICompletion({ type: action, text: this.getSanitizedHTML(this.askAIInsertionPoint) });
                this.askAIInsertionPoint.innerHTML = '';    
                break;

            case isSectionContainsSuggestionBlock:
                MessageHandler.postAICompletion({ type: action, text: this.getSanitizedHTML(this.suggestionBlock) });

                this.suggestionBlock.innerHTML = '';
                this.wysiwygOperations.collapseCaretOnNode(this.suggestionBlock);
                RangeOperations.replaceSelectionWithNode(this.iframeDocument, this.sectionContainer, this.wysiwygOperations.selection, this.askAIInsertionPoint);    
                break;
            
            default:
                MessageHandler.postAICompletion({ type: action, text: this.getSanitizedHTML(this.sectionContainer) });

                const emptyTextBuilderBlock = TextSection.getTextBuilderblock();
                this.sectionContainer.innerHTML = emptyTextBuilderBlock.outerHTML;
                this.appendLoader();
                this.askAIInsertionPoint.innerHTML = '';
                this.wysiwygOperations.collapseCaretOnNode(this.sectionContainer);
                RangeOperations.replaceSelectionWithNode(this.iframeDocument, this.sectionContainer, this.wysiwygOperations.selection, this.askAIInsertionPoint);

        }
    }

    handleRangeSelectionAction = (action) => {
        const originalAskAIInsertionPointEl = this.sectionContainer.querySelector(".ai-insertion-point");
        if(!originalAskAIInsertionPointEl) {
            const isCommonParent = (this.selection.startContainer === this.selection.endContainer) && (this.selection.startContainer === this.selection.commonAncestorContainer);

            if (isCommonParent) this.selection.surroundContents(this.askAIInsertionPoint);
            else {
                // If more than one text node is selected, iterate for all text nodes in section
                RangeOperations.surroundSelection(this.iframeDocument, this.sectionContainer, this.selection, this.askAIInsertionPoint);
            }
        }

        if(!this.sectionContainer.contains(this.suggestionBlock)) {
            const clonedSectionContainer = this.sectionContainer.cloneNode(true);
            clonedSectionContainer.querySelector(".ask-ai-loader-container")?.remove();
            
            if(this.sectionContainer.contains(this.askAIInsertionPoint)) this.askAIInsertionPoint.outerHTML = this.askAIInsertionPoint.innerHTML;
            
            this.suggestionBlock.innerHTML = clonedSectionContainer.innerHTML;

            const aiInsertionPoints = Array.from(this.suggestionBlock.querySelectorAll(".ai-insertion-point"));
            const newAskAIInsertionPointEl = aiInsertionPoints.shift();
            this.askAIInsertionPoint = newAskAIInsertionPointEl;

            aiInsertionPoints.forEach(insertionPointEl => insertionPointEl.remove());
        }
        
        MessageHandler.postAICompletion({ type: action, text: this.getSanitizedHTML(this.askAIInsertionPoint) });

        this.askAIInsertionPoint.innerHTML = '';
        this.wysiwygOperations.collapseCaretOnNode(this.askAIInsertionPoint);
    }

    handleStreaming = (detail) => (this.askAIInsertionPoint.innerHTML += detail.chunk.replaceAll('\n', '<br>'));

    handleStreamingEnd = (detail) => {
        switch(detail.type) {
            case ASK_AI_ACTIONS.CONTINUE_WRITING:
                this.nonEmptyTextPopup.remove();
                this.suggestedTextPopup.remove();
                this.popup.popup.appendChild(this.emptyTextPopup);
                break;

            case ASK_AI_ACTIONS.MAKE_LONGER:
            case ASK_AI_ACTIONS.MAKE_SHORTER:
                if(this.sectionContainer.contains(this.suggestionBlock)) {
                    this.nonEmptyTextPopup.remove();
                    this.popup.popup.appendChild(this.suggestedTextPopup);
                } else {
                    this.nonEmptyTextPopup.remove();
                    this.suggestedTextPopup.remove();
                    this.popup.popup.appendChild(this.emptyTextPopup);
                }
                break;

            case ASK_AI_ACTIONS.FIX_SPELLING_AND_GRAMMAR:
            case ASK_AI_ACTIONS.SUMMARIZE:
            case ASK_AI_ACTIONS.IMPROVE_WRITING:
                this.nonEmptyTextPopup.remove();
                this.popup.popup.appendChild(this.suggestedTextPopup);
                break;

            case ASK_AI_ACTIONS.WRITE:
                this.popup.spawnPopup();
                break;

            case ASK_AI_ACTIONS.TRY_AGAIN:
            case ASK_AI_ACTIONS.DISCARD:
                break;
        }

        Popup.positionPopup(this.popup.getPositioningPopupArguments());
        this.deactivateLoadingState();
    }

    handleAIResponse = (detail) => {
        setTimeout(() => {
            if(detail.isFinished) return this.handleStreamingEnd(detail);

            this.handleStreaming(detail);
        }, 1000);
    }

    getSanitizedHTML = (html) => {
        const clonedHtml = html.cloneNode(true);
        clonedHtml.querySelector(".ask-ai-loader-container")?.remove();

        return clonedHtml.innerText;
    }

    handlePasteEvent = () => {
        if(this.isAIActivated && !this.isEmptySection(this.sectionContainer)) {
            this.sectionContainer.setAttribute("ai-placeholder", "");
            if(this.isInitialEmpty) this.showSendButton();
        }
    }

    removeSendButton = () => this.sectionContainer.querySelector(".ask-ai-send-button-container")?.remove();
    focusSection = () => setTimeout(() => this.sectionContainer.focus());
    appendLoader = () => this.sectionContainer.appendChild(this.loadingAnimationTemplate);
}
