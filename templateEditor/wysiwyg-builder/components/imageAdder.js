import Common from '../../../index.js';

export default class ImageAdder {
    constructor(component, imageSrc, additionCallback = () => {}, removalCallback = () => {}) {
        this.imageSrc = imageSrc;
        this.additionCallback = additionCallback;
        this.removalCallback = removalCallback;

        this.imageUploadDropContainer = component.querySelector(".image-drop-upload");
        this.imageUploadInput = this.imageUploadDropContainer.querySelector("input");
        this.deleteImageBtn = this.imageUploadDropContainer.querySelector("#delete-image-icon");
        this.imageUrlInput = component.querySelector("#add-image-url");

        this.init();
    }

    init() {
        this.imageUploadDropContainer.addEventListener("click", () => this.imageUploadInput.click());
        this.imageUploadInput.addEventListener("change", this.uploadImage.bind(this));
        this.imageUploadDropContainer.addEventListener("drop", this.uploadImage.bind(this), false);

        ["dragenter", "dragover", "dragleave", "drop"].forEach(eventName => {
            this.imageUploadDropContainer.addEventListener(eventName, (event) => {
                event.preventDefault();
                event.stopPropagation();
            });
        });

        let inputTimeout = 0;
        this.imageUrlInput.addEventListener("input", (event) => {
            const url = event.currentTarget.value;
            clearTimeout(inputTimeout);
            this.imageUploadDropContainer.classList.add("loading");
            inputTimeout = this.verifyImage(url);
        });

        this.deleteImageBtn.addEventListener("click", (event) => {
            event.stopPropagation();
            this.removeImage();
        });
        
        this.imageSrc 
            ? this.addImageToUploadContainer(this.imageSrc)
            : this.removeImageFromUploadContainer();
    }

    uploadImage(event) {
        this.imageUploadDropContainer.classList.add("loading");
        Common.upload(event, (url) => this.updateImage(url));
    }

    updateImage (url) {
        this.imageSrc = url;
        this.addImageToUploadContainer(url);
        this.additionCallback(url);
        this.imageUrlInput.value = url;
        this.imageUploadDropContainer.classList.remove("loading");
        this.imageUrlInput.parentElement.classList.remove("invalid");
    }

    removeImage() {
        this.imageSrc = "";
        this.removeImageFromUploadContainer();
        this.removalCallback();
        this.imageUrlInput.value = "";
    }
    
    addImageToUploadContainer(url) {
        this.imageUploadDropContainer.style.backgroundImage = `url(${url})`;
        this.imageUploadDropContainer.classList.add("active");
    }

    removeImageFromUploadContainer() {
        this.imageUploadDropContainer.style.backgroundImage = "";
        this.imageUploadDropContainer.classList.remove("active");
    }

    verifyImage(url) {
        const timeout = 3000;
        const img = new Image();

        img.onerror = () => {
            this.imageUrlInput.parentElement.classList.add("invalid");
            this.imageUploadDropContainer.classList.remove("loading");
        };

        img.onload = () => {
            this.updateImage(url);
        };

        img.src = url;
        return setTimeout(() => {}, timeout); 
    }

    resetUrlField() {
        this.imageUrlInput.value = this.imageSrc || "";
        this.imageUrlInput.parentElement.classList.remove("invalid");
    }
}