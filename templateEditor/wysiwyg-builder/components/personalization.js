import Common from "../../..";
import SelectMenu from "../../../generic-components/selectMenu";
import CssOperations from "../../../generic-utils/cssOperations";
import htmlTreeOperations from "../../../generic-utils/htmlTreeOperations";
import ExperienceBuilderViews from "../../../views/experienceBuilderViews";
import Views from "../../views/templateViews";
import RangeOperations from "../rangeOperations";
import UndoRedoManager from "../UndoRedoManager/undoRedoManager";

export default class Personalization {
    static ATTRIBUTE_TYPES = {
        USER: "user",
        COMPANY: "company"
    }

    constructor(component, editor, onAdditionCallback = () => {}, onAttributeElementClickCallback = () => {}) {
        this.component = component;
        this.editor = editor;
        this.onAdditionCallback = onAdditionCallback;
        this.onAttributeElementClickCallback = onAttributeElementClickCallback;

        this.sectionContainer = editor.sectionContainer;
        this.wysiwygOperations = editor.wysiwygOperations;
        this.undoRedoManager = editor.undoRedoManager;
        this.fallbackView = this.component.querySelector("#fallback-value");
        this.fallbackInput = this.fallbackView.querySelector("input");
        this.submitBtn = this.component.querySelector(".submit");

        this.attributeType;
        this.currentSelection;

        this.initPersonalizeEvents();
    }

    initPersonalizeEvents() {
        const selectAttribute = (type, select) => {
            this.attributeType = type;
            this.currentSelection = select.getAttribute("value");
            this.fallbackView.style.display = "";
            this.submitBtn.style.display = "";
            (type == Personalization.ATTRIBUTE_TYPES.COMPANY) ? this.attributeSelectMenu.resetToDefaultView() : this.companyAttributeSelectMenu.resetToDefaultView();
        }

        const attributeSelect = this.component.querySelector(".userpilot-select[type='user']");
        this.userAttributeSelectOptions = ExperienceBuilderViews.getUserAttributeSelectOptions();
        this.attributeSelectMenu = new SelectMenu(attributeSelect, this.userAttributeSelectOptions, 
            selectAttribute.bind(this, Personalization.ATTRIBUTE_TYPES.USER), this.currentSelection, "", false, {}, { searchable: true, autoFallbackValue: false, getFallbackValue: this.getUserAttributeFallbackValue }
        );

        const companyAttributeSelect =  this.component.querySelector(".userpilot-select[type='company']");
        this.companyAttributeSelectOptions = ExperienceBuilderViews.getUserAttributeSelectOptions("company");
        this.companyAttributeSelectMenu = new SelectMenu(companyAttributeSelect, this.companyAttributeSelectOptions,
             selectAttribute.bind(this, Personalization.ATTRIBUTE_TYPES.COMPANY), this.currentSelection, "", false, {}, { searchable: true, autoFallbackValue: false, getFallbackValue: this.getCompanyAttributeFallbackValue }
        );

        this.submitBtn.addEventListener("click", () => {
            const attributeElement = this.personalize(this.currentSelection, this.fallbackInput.value, this.attributeType);
            this.initAttributeElementEvent(attributeElement);
            this.onAdditionCallback?.();
        });

        this.initAttributeElementsEvents();
    }

    getUserAttributeFallbackValue (currentValue) {
        currentValue = currentValue.slice(1, currentValue.length - 1);
        return Common.userData.userPropertiesList.find(userProperty => userProperty.legacy_key == currentValue).display_name;
    }

    getCompanyAttributeFallbackValue (currentValue) {
        currentValue = currentValue.slice(1, currentValue.length - 1);
        return Common.userData.companyPropertiesList.find(companyProperty => companyProperty.legacy_key == currentValue).display_name;
    }

    initAttributeElementsEvents() {
        const attributeElements = Array.from(this.sectionContainer.querySelectorAll("up-container"));
        attributeElements.forEach(element => this.initAttributeElementEvent(element));
    }

    initAttributeElementEvent(attributeElement) {
        attributeElement.addEventListener("click", () => {
            setTimeout(() => {
                this.wysiwygOperations.selection.selectNode(attributeElement);
                this.wysiwygOperations.highlightSelection();
                this.onAttributeElementClickCallback?.(attributeElement);
            }, 50);
        });
    }

    personalize(attribute, fallback, attributeType) {
        const attributeElement = Views.getViewElement("attribute-element", {
            type: attributeType,
            fallback: fallback,
            attribute: attribute
        });

        this.editor.undoRedoManager.saveForUndo(UndoRedoManager.MUTATION_TYPES.CUSTOM);

        if(RangeOperations.isMultipleBlocks(this.wysiwygOperations.selection)) {
            const firstSelectedBlock = this.elementsToEdit[0];
            const startContainer = this.wysiwygOperations.selection.startContainer.nodeName === "#text" ? this.wysiwygOperations.selection.startContainer.parentElement : this.wysiwygOperations.selection.startContainer;
            const { parent, lastChild } = htmlTreeOperations.getDirectParentsTreeCopy(startContainer, firstSelectedBlock);

            lastChild.appendChild(attributeElement);
            RangeOperations.replaceSelectionWithNode(this.iframeDocument, this.sectionContainer, this.wysiwygOperations.selection, parent);
            this.wysiwygOperations.removeEmptyBlocks();
        } else {
            RangeOperations.replaceSelectionWithNode(this.iframeDocument, this.sectionContainer, this.wysiwygOperations.selection, attributeElement);
        }
        attributeElement.innerText = attributeElement.innerText.trim();
        htmlTreeOperations.removeEmptyNodesOfSelector(this.sectionContainer, "up-container");

        this.wysiwygOperations.removeHighlightBackgrounds();
        RangeOperations.setTextCaretAfterNode(this.wysiwygOperations.selection, attributeElement);
        this.wysiwygOperations.resetSectionRange();

        CssOperations.highlightElementForDuration(attributeElement, "#DD5584", 1500);
        return attributeElement;
    }

    initAttributeElement(attributeElement) {
        if (attributeElement) {
            const attributeValue = "{" + attributeElement.textContent.replace(/[{}]/g, "").trim() + "}";
            const fallbackValue = attributeElement.getAttribute("up-fallback");
            const type = attributeElement.getAttribute("type");
            this.attributeType = type;
            this.currentSelection = attributeValue;

            (type == Personalization.ATTRIBUTE_TYPES.COMPANY) ? this.companyAttributeSelectMenu.setValue(attributeValue) : this.attributeSelectMenu.setValue(attributeValue);
            this.fallbackInput.value = fallbackValue;
            this.fallbackView.style.display = "";
            this.submitBtn.style.display = "";
        }

        this.wysiwygOperations.highlightSelection();

    }

    resetPersonlization() {
        this.fallbackView.style.display = "none";
        this.submitBtn.style.display = "none";
        this.fallbackInput.value = "";
        this.attributeSelectMenu.resetToDefaultView();
        this.companyAttributeSelectMenu.resetToDefaultView();
        this.wysiwygOperations.unhighlightSelection();
        this.wysiwygOperations.refocusText();
    }

    static runPersonalizationNodeChecks = (sectionContainer) => {
        Array.from(sectionContainer.querySelectorAll("up-container"))
        .filter(node => node.querySelector("span"))
        .forEach(node => {
            const personalizationSpan = node.querySelector("span.userpilot-personalization-node");
            const value = personalizationSpan?.textContent?.trim() || node?.textContent?.trim();
            const type = personalizationSpan?.getAttribute("type") || "user";
            const fallback = personalizationSpan?.getAttribute("up-fallback") || "";
            const active = personalizationSpan?.getAttribute("active") || "1";
    
            node.innerHTML = value;
            node.setAttribute("type", type);
            node.setAttribute("up-fallback", fallback);
            node.setAttribute("active", active);
            node.classList.add("userpilot-personalization-node");
            
        });
    
        Array.from(sectionContainer.querySelectorAll("up-container[contenteditable='false']"))
        .filter(node => !node?.nextSibling?.wholeText?.trim()?.length)
        .forEach(node => htmlTreeOperations.insertAfter(node, document.createTextNode("\u200B")));
    }

}