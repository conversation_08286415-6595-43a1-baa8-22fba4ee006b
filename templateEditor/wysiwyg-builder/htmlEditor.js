import BuilderSection from './commonEditor.js';
import Views from './wysiwigViews.js';
import htmlTreeOperations from "../../generic-utils/htmlTreeOperations.js";
import InputSelectionRange from "../../generic-utils/inputSelectionRange.js";

export default class HTMLSection extends BuilderSection {
    constructor(el, popupToolbar) {
        super(el, popupToolbar);
        this.htmlEditorContainer = Views.getViewElement("html-section-editor");
        this.htmlEditor = this.htmlEditorContainer.querySelector("textarea");
        this.editorSelectionInstance = new InputSelectionRange(this.htmlEditor);
        this.initHtmlEditorListeners();
    }

    init() {
        super.init();
        const currentHtml = this.sectionContainer.innerHTML.trim().replace("<empty></empty>", "");
        this.sectionContainer.innerHTML = "";
        this.htmlEditor.value = currentHtml;
        this.sectionContainer.appendChild(this.htmlEditorContainer);
        this.editorSelectionInstance.collapseSelection();
        this.sectionContainer.setAttribute("html", "")
        // if(this.htmlEditor.value){
        //     htmlTreeOperations.verifyHTML(this.htmlEditor.value) ? this.sectionContainer.setAttribute("html", "valid") : this.sectionContainer.setAttribute("html", "invalid");
        // }
    }

    // To override and disable the default behavior of the superclass
    preventSectionDeletion() {

    }

    save(_event) {
        super.save();
        if (this.htmlEditor.value) {
            this.sectionContainer.textContent = "";
            this.sectionContainer.innerHTML = this.htmlEditor.value;
        } else {
            this.sectionContainer.innerHTML = "<empty></empty>"
        }
        this.htmlEditorContainer.remove();
    }

    initHtmlEditorListeners() {
        this.htmlEditor.addEventListener('paste', this.pasteHandler.bind(this));
        // this.htmlEditor.addEventListener("input", () => {
        //     if(!this.htmlEditor.value){
        //         this.sectionContainer.setAttribute("html", "");
        //         return;
        //     }
        //     htmlTreeOperations.verifyHTML(this.htmlEditor.value) ? this.sectionContainer.setAttribute("html", "valid") : this.sectionContainer.setAttribute("html", "invalid");
        // });
    }

    pasteHandler(event) {
        event.preventDefault();
        const paste = event.clipboardData.getData('text');
        this.editorSelectionInstance.insertIntoInput(paste);
    }

    initPopupEvents(popupName, popupContainer, popupObj) {
        switch(popupName) {
            case "section-padding":
                this.initSectionPaddingEvents(popupContainer);
                break;
            case "section-background":
                this.initSectionBgEvents(popupContainer, popupObj);
                break;
            case "section-visibility":
                this.initVisibilityEvents(popupContainer, popupObj);
                break;
            default:
                break;
        }
    }
}