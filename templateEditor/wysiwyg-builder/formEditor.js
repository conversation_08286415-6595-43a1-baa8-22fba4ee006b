import BuilderSection from './commonEditor.js';
import InputSlider from "../../generic-components/inputSlider.js";
import SolidColorPicker from "../../generic-components/colorPickers/solidColorPicker.js";
import Views from './wysiwigViews.js';
import SelectMenu from "../../generic-components/selectMenu.js";
import WysiwygOperations from './utils/wysiwygOperations.js';
import ExperienceBuilderIcons from '../../views/experienceBuilderIcons.js';
import { LIKERT_SCALE_TYPES } from '../../store/constants/experiences/constants.js';
import TemplateIcons from '../views/templateIcons.js';
import { Store } from '../../store/store.js';
import App from '../../components/app.js';

class InputSection extends BuilderSection {
    static userpilotTextStyles = ["color"];

    constructor(el, popupToolbar, editorTemplate, finalTemplate) {
        super(el, popupToolbar);
        this.editorTemplateName = editorTemplate;
        this.finalTemplate = finalTemplate;
        this.inputOptions = {
            type: 'text',
            required: false,
            maxLength: ''
        }
        this.labelOptions = {
            color: '',
            textAlign: ''
        }
        this.readInputOptions();
        this.initLabelOptions();
    }

    init() {
        super.init();
        this.sectionContainer.contentEditable = false;
        this.switchToEditorView();

        this.wysiwygOperations = new WysiwygOperations(this, {preventDefaultKeydownBehavior: true});
        this.initFontColorEventListeners();
        this.inputEditor.querySelector('.userpilot-form-label').addEventListener("keydown", this.preventEnterKey, true);
        this.sectionContainer.addEventListener("keyup", this.handleCaretMovement, true);
        this.sectionContainer.addEventListener("mouseup", this.handleCaretMovement, true);
    }

    save(_event) {
        super.save();
        this.applyEditorChanges(_event);

        this.inputEditor.querySelector('.userpilot-form-label').removeEventListener("keydown", this.preventEnterKey, true);
        this.sectionContainer.removeEventListener("keyup", this.handleCaretMovement, true);
        this.sectionContainer.removeEventListener("mouseup", this.handleCaretMovement, true);
    }

    readInputOptions() {
        const labelRef = this.sectionContainer.querySelector('.userpilot-form-label');
        const createPropertyAttrValue = this.sectionContainer.getAttribute('create-property');

        this.inputOptions.required = this.sectionContainer.getAttribute('required');
        this.inputOptions.isCreatePropertyChecked = createPropertyAttrValue || true;
        this.inputOptions.propertyName = this.sectionContainer.getAttribute('property-name') || labelRef.innerText;
        this.inputOptions.type = this.sectionContainer.querySelector('input, textarea')?.getAttribute('t_type');
        if(this.inputOptions.type === SingleInputSection.SINGLE_INPUT_TYPES.URL) this.inputOptions.type = SingleInputSection.SINGLE_INPUT_TYPES.GENERAL;
        this.inputOptions.maxLength = this.sectionContainer.querySelector('input, textarea')?.getAttribute('maxlength');
    }

    initLabelOptions() {
        const labelColor = this.sectionContainer.querySelector('.userpilot-form-label');
        this.labelOptions.textAlign = labelColor.style.textAlign;
    }

    switchToEditorView() {
        const labelRef = this.sectionContainer.querySelector('.userpilot-form-label');
        const inputRef = this.sectionContainer.querySelector('input, textarea');

        this.inputEditor = Views.getViewElement(this.editorTemplateName, {
            label: labelRef.innerHTML,
            placeholder: inputRef.getAttribute('placeholder'),
        });
        
        this.inputEditor.querySelector('.userpilot-form-label').style.color = labelRef.style.color;
        this.sectionContainer.innerHTML = "";
        this.sectionContainer.appendChild(this.inputEditor);
        this.initFormEventListeners();
    }

    applyEditorChanges(_event) {

        const label = this.inputEditor.querySelector('.userpilot-form-label').innerHTML;

        const placeholder = this.sectionContainer.querySelector('.placeholder-input').value || this.sectionContainer.querySelector('.placeholder-input').innerHTML;

        const originalTemplateContent = Views.getViewElement(this.finalTemplate).querySelector('#userpilot-form');

        this.sectionContainer.setAttribute('required', this.inputOptions.required);

        if(_event !== "discard") {
            this.sectionContainer.setAttribute('create-property', this.inputOptions.isCreatePropertyChecked);
            [true, "true"].includes(this.inputOptions.isCreatePropertyChecked) ? this.sectionContainer.setAttribute('property-name', this.inputOptions.propertyName) : this.sectionContainer.removeAttribute('property-name');
        }

        originalTemplateContent.querySelector('.userpilot-form-label').innerHTML = label;
        originalTemplateContent.querySelector('.userpilot-form-label').style.textAlign = this.labelOptions.textAlign;

        originalTemplateContent.querySelector('.form-input').setAttribute('placeholder', placeholder);

        this.sectionContainer.appendChild(originalTemplateContent);
        this.inputEditor.remove();
    }

    initPopupEvents(popupName, popupContainer, popupObj) {
        switch (popupName) {
            case "section-background":
                this.initSectionBgEvents(popupContainer, popupObj);
                break;
            case "image-resize":
                this.initBackgroundOptionsEvents(popupContainer, popupObj);
                break;
            case "section-padding":
                this.initSectionPaddingEvents(popupContainer);
                break;
            case "input-settings":
                this.initSettingsEvents(popupContainer);
                break;
            case "section-visibility":
                this.initVisibilityEvents(popupContainer, popupObj);
                break;
            default:
                break;
        }
    }

    initSettingsEvents(popupContainer) {
        this.initRequiredCheckbox(popupContainer);
        this.initCreateProperty(popupContainer);
    }
    
    initRequiredCheckbox(popupContainer) {
        const checkbox = popupContainer.querySelector(".required-checkbox");
        const isRequired = (this.inputOptions.required === true || this.inputOptions.required === "true");
        (this.inputOptions.required && isRequired) ? checkbox.setAttribute('checked', "true") : checkbox.removeAttribute('checked');
        checkbox.addEventListener('change', (e) => this.inputOptions.required = checkbox.checked);
    }

    initCreateProperty(popupContainer) {
        const checkbox = popupContainer.querySelector(".create-property-checkbox");
        const input = popupContainer.querySelector("#property-name-field");
        const inputContainer = popupContainer.querySelector(".property-name-field-container");
        const isChecked = [true, "true"].includes(this.inputOptions.isCreatePropertyChecked);

        const showHideInput = (isChecked) => {
            if(isChecked) inputContainer.classList.remove("display-none");
            else inputContainer.classList.add("display-none");
        }

        (isChecked) ? checkbox.setAttribute('checked', "true") : checkbox.removeAttribute('checked');
        checkbox.addEventListener('change', () => {
            const labelRef = this.sectionContainer.querySelector('.userpilot-form-label');
            this.inputOptions.isCreatePropertyChecked = checkbox.checked;
            if(checkbox.checked) {
                input.value = labelRef.innerText;
                this.inputOptions.propertyName = labelRef.innerText;
            }
            showHideInput(checkbox.checked);
        });

        input.value = this.inputOptions.propertyName;
        input.addEventListener('change', () => {
            const value = input.value.trim() || this.inputOptions.propertyName;
            input.value = value;
            this.inputOptions.propertyName = value;
        });

        showHideInput(isChecked);
    }

    initFontColorEventListeners() {
        const fontColorIcon = this.popupToolbar.querySelector("#font-color");
        const formLabel = this.inputEditor.querySelector('.userpilot-form-label');

        new SolidColorPicker(fontColorIcon, this.getSelectedBlockColor.bind(this), {
            onInitCallback: () => {
                if(!formLabel.contains(this.wysiwygOperations.selection.startContainer)) {
                    this.wysiwygOperations.collapseCaretOnNode(formLabel);
                    this.findActiveStyles();
                }
                this.wysiwygOperations.highlightSelection();
            },
            onChangeCallback: (color) =>  this.changeFontProperty(color, "color"),
            onSaveCallback: this.wysiwygOperations.unhighlightSelection,
            onHideCallback: this.wysiwygOperations.unhighlightSelection,
        });

    }

    getSelectedBlockColor() {
        const labelColor = this.sectionContainer.querySelector('.userpilot-form-label');
        return labelColor.style.color || getComputedStyle(labelColor).color;
    }

    initBackgroundOptionsEvents(popupContainer, popupObj) {
        const sectionStyles = getComputedStyle(this.sectionContainer);
        const imageZoom = parseFloat(sectionStyles.backgroundSize);
        const imageHeight = parseFloat(this.sectionContainer.offsetHeight);

        const zoomSlider = popupContainer.querySelector("#zoom-slider");
        new InputSlider(zoomSlider, this.setImageZoom.bind(this), imageZoom);

        const heightSlider = popupContainer.querySelector("#height-slider");
        new InputSlider(heightSlider, this.setImageHeight.bind(this), imageHeight);

        popupObj.callbackOnPopupSpawn = () => {
            if (this.sectionContainer.style.background.includes("url")) popupContainer.classList.remove("no-image");
            else popupContainer.classList.add("no-image");
        }
    }

    setImageHeight(value) {
        this.sectionContainer.style.minHeight = value + "px";
        this.sectionContainer.style.maxHeight = value + "px";
    }

    setImageZoom(value) {
        this.sectionContainer.style.backgroundSize = value + "%";
    }

    validateChanges() {
        const label = this.sectionContainer.querySelector('.userpilot-form-label');
        const input = this.sectionContainer.querySelector('#content-editor .userpilot-input');
        if (!label.innerText.trim()) {
            this.showLabelErrorMessage();
            return false;
        }
        if (!input.value || (!input.value && !input.innerHTML)) {
            this.showEditorErrorMessage();
            return false;
        }
        return true;
    }

    showLabelErrorMessage() {
        const messageElement = this.sectionContainer.querySelector('#input-label-editor .error-message');
        const inputElement = this.sectionContainer.querySelector('#input-label-editor .userpilot-input');
        messageElement.style.display = 'block';
        inputElement.classList.add('invalid');

    }

    showEditorErrorMessage() {
        const messageElement = this.sectionContainer.querySelector('#content-editor .error-message');
        const inputElement = this.sectionContainer.querySelector('#content-editor .userpilot-input');
        messageElement.style.display = 'block';
        inputElement.classList.add('invalid');
    }

    initFormEventListeners() {
        const labelInput = this.sectionContainer.querySelector('#input-label-editor .userpilot-input');
        const labelError = this.sectionContainer.querySelector('#input-label-editor .error-message');
        const contentInput = this.sectionContainer.querySelector('#content-editor .userpilot-input');
        const contentError = this.sectionContainer.querySelector('#content-editor .error-message');

        const restErrors = () => {
            labelError.style.display = 'none';
            if(contentError) contentError.style.display = 'none';
            labelInput.classList.remove('invalid');
            contentInput?.classList.remove('invalid');
        }

        labelInput.addEventListener('input', restErrors.bind(this));
        contentInput?.addEventListener('input', restErrors.bind(this));

    }

    handleCaretMovement = () => {
        setTimeout(this.findActiveStyles.bind(this), 100);
    }

    findActiveStyles() {
        const setUserpilotTextStyles = () => {
            const currentElement = (this.wysiwygOperations.selection.startContainer.nodeName == "#text") ? this.wysiwygOperations.selection.startContainer.parentElement : this.wysiwygOperations.selection.startContainer;
            const style = getComputedStyle(currentElement);
            const fontColorButton = this.popupToolbar.querySelector("#font-color");
            fontColorButton.querySelector("svg").style.fill = style.color;
        }
        setUserpilotTextStyles();
    }

    preventEnterKey = (e) => {
        if(e.key === "Enter") e.preventDefault();
    }

    normalizeText(offsets) {
        const formLabel = this.inputEditor.querySelector(".userpilot-form-label");
        super.normalizeText(offsets, formLabel, InputSection.userpilotTextStyles, "span")
    }

    stopEventsPropagation() {
        const contentEditorEl = this.inputEditor.querySelector("#content-editor");
        
        const stopPropagation = (e) => e.stopPropagation();

        contentEditorEl.addEventListener("mouseup", stopPropagation);
        contentEditorEl.addEventListener("keyup", stopPropagation);
        contentEditorEl.addEventListener('keydown', stopPropagation);
    }

}

export class SingleInputSection extends InputSection {

    static SINGLE_INPUT_TYPES = {
        GENERAL: "general",
        TEXT: "text",
        NUMBER: "number",
        EMAIL: "email",
        DATE: "date",
        PHONE: "tel",
        URL: "url",
    }

    constructor(el, popupToolbar) {
        super(el, popupToolbar, "input-section-editor", "input-section");
    }

    getInputType() {
        switch(this.inputOptions.type) {
            case SingleInputSection.SINGLE_INPUT_TYPES.GENERAL:
                return "text";

            default:
                return this.inputOptions.type;

        }
    }

    applyEditorChanges(_event) {
        const label = this.inputEditor.querySelector('.userpilot-form-label').innerHTML;
        const placeholder = this.sectionContainer.querySelector('.placeholder-input').value || this.sectionContainer.querySelector('.placeholder-input').innerHTML;

        const originalTemplateContent = Views.getViewElement(this.finalTemplate, {
            inputContainer: Views.getView(this.inputOptions.type === SingleInputSection.SINGLE_INPUT_TYPES.PHONE ? "phone-input-container" : "text-input-container"),
        }).querySelector('#userpilot-form');

        this.sectionContainer.setAttribute('required', this.inputOptions.required);

        if(_event !== "discard") {
            this.sectionContainer.setAttribute('create-property', this.inputOptions.isCreatePropertyChecked);
            [true, "true"].includes(this.inputOptions.isCreatePropertyChecked) ? this.sectionContainer.setAttribute('property-name', this.inputOptions.propertyName) : this.sectionContainer.removeAttribute('property-name');
        }

        originalTemplateContent.querySelector('input').setAttribute('t_type', this.inputOptions.type);
        originalTemplateContent.querySelector('input').setAttribute('maxlength', this.inputOptions.maxLength);
        originalTemplateContent.querySelector('input').setAttribute('type', this.getInputType());

        originalTemplateContent.querySelector('.userpilot-form-label').innerHTML = label;
        originalTemplateContent.querySelector('.userpilot-form-label').style.textAlign = this.labelOptions.textAlign;
        originalTemplateContent.querySelector('.form-input').setAttribute('placeholder', placeholder);

        this.sectionContainer.appendChild(originalTemplateContent);
        this.inputEditor.remove();
    }

    initSettingsEvents(popupContainer) {
        popupContainer.classList.remove('hidden-options');

        const maxLengthInput = popupContainer.querySelector('#input-length');
        maxLengthInput.value = this.inputOptions.maxLength || '';
        maxLengthInput.addEventListener('input', () => this.inputOptions.maxLength = maxLengthInput.value);

        this.initRequiredCheckbox(popupContainer);
        this.initCreateProperty(popupContainer);
    }

    initFormEventListeners() {
        super.initFormEventListeners();

        const typesSelectMenu = this.inputEditor.querySelector('#input-type');
        new SelectMenu(
            typesSelectMenu,
            null,
            (item) => this.inputOptions.type = item.getAttribute('value'),
            this.inputOptions.type,
            "input-type-select",
            false,
            { 
                container: this.iframeDocument.querySelector("#content-container"),
                positionTo: typesSelectMenu,
                isSameWidthAsSelectButton: true,
            }
        );
    }
}

export class LargeInputSection extends InputSection {

    constructor(el, popupToolbar) {
        super(el, popupToolbar, "large-input-section-editor", "large-input-section");
    }
}

export class RadioInputSection extends InputSection {

    constructor(el, popupToolbar) {
        super(el, popupToolbar, "radio-input-section-editor", "radio-input-section");
    }

    generateChoiceContainerEl(value = "", classes = "") {
        const choiceContainer = Views.getViewElement("multiple-choice-item-editor", {
            choice: value,
            trashIcon: ExperienceBuilderIcons.getDeleteIconV2(20, 20),
            classes: classes,
        });

        const inputField = choiceContainer.querySelector("input");
        inputField.addEventListener("input", this.resetMultipleChoiceErrors);

        const deleteBtn = choiceContainer.querySelector(".delete-btn");
        deleteBtn.addEventListener("click", e => {
            const choiceContainer = e.currentTarget.parentElement;
            const addOtherBtn = this.inputEditor.querySelector(".add-other-btn");
            const isOtherChoiceRemoved = choiceContainer.classList.contains("other-container");

            e.currentTarget.parentElement.remove()
            if(isOtherChoiceRemoved) addOtherBtn.classList.remove("display-none");
        });

        return choiceContainer;
    }

    initActions() {
        const choicesContainerEl = this.inputEditor.querySelector(".choices-container");
        const addChoiceBtn = this.inputEditor.querySelector(".add-choice-btn");
        const addOtherBtn = this.inputEditor.querySelector(".add-other-btn");
        const otherChoiceContainerEl = choicesContainerEl.querySelector(".other-container");

        const hideAddOtherBtn = () => addOtherBtn.classList.add("display-none");
        if(otherChoiceContainerEl) hideAddOtherBtn();

        addChoiceBtn.addEventListener("click", () => {
            const newChoiceEl = this.generateChoiceContainerEl();
            const otherChoiceContainerEl = choicesContainerEl.querySelector(".other-container");
            const inputField = newChoiceEl.querySelector("input");

            otherChoiceContainerEl ? choicesContainerEl.insertBefore(newChoiceEl, otherChoiceContainerEl) : choicesContainerEl.appendChild(newChoiceEl);
            inputField.addEventListener("input", this.resetMultipleChoiceErrors);
            inputField.focus();
        });

        addOtherBtn.addEventListener("click", () => {
            const newChoiceEl = this.generateChoiceContainerEl("Other", "other-container");
            const inputField = newChoiceEl.querySelector("input");
            choicesContainerEl.appendChild(newChoiceEl);
            inputField.addEventListener("input", this.resetMultipleChoiceErrors);
            inputField.focus();
            inputField.selectionStart = inputField.value.length;
            hideAddOtherBtn();
        });
    }

    resetMultipleChoiceErrors = () => {
        const contentError = this.inputEditor.querySelector('#content-editor .error-message');
        if(contentError) contentError.style.display = 'none';
    }

    initFormEventListeners() {
        super.initFormEventListeners();

    }

    switchToEditorView() {
        this.labelRef = this.sectionContainer.querySelector('.userpilot-form-label');
        this.multipleChoiceInputs = this.sectionContainer.querySelectorAll('.form-radio-container');
        const firstMultipleChoiceInput = this.multipleChoiceInputs[0].querySelector("input");
        this.multipleChoiceName = firstMultipleChoiceInput.name;

        const appState = Store.getComponentState(App);
        const isDefaultLocale = appState.current_localization === "default";

        this.inputEditor = Views.getViewElement(this.editorTemplateName, {
            label: this.labelRef.innerHTML,
            multipleSelectToggle: Views.getView("settings-toggle-on-right", {
                id: "multiple-select-checkbox-container",
                title: "Multiple select",
                settingToggle: Views.getView("settings-toggle", {
                    id: "multiple-select-checkbox",
                    action: "",
                    jsonSetting: "",
                    title: "",
                    expandingSection: ""
                }),
            })
        });

        const multipleChoiceCheckbox = this.inputEditor.querySelector("#multiple-select-checkbox");
        multipleChoiceCheckbox.checked = firstMultipleChoiceInput?.getAttribute("type") === "checkbox";

        const choicesContainerEl = this.inputEditor.querySelector(".choices-container");
        const choices = Array.from(this.multipleChoiceInputs).map(choiceEl => {
            const additionalClasses = choiceEl.classList.contains("other-container") ? "other-container" : "" ;

            if(isDefaultLocale) return this.generateChoiceContainerEl(choiceEl.querySelector("input").value, additionalClasses);
            else return this.generateChoiceContainerEl(choiceEl.querySelector("label").textContent, additionalClasses);
        });
        choicesContainerEl.append(...choices);

        this.sectionContainer.innerHTML = "";
        this.sectionContainer.appendChild(this.inputEditor);
        this.initFormEventListeners();
        this.stopEventsPropagation();
        this.initActions();
    }

    applyEditorChanges(_event) {
        const labelEl = this.inputEditor.querySelector('.userpilot-form-label');
        const labelValue = labelEl.innerHTML;
        const labelAlignment = this.labelOptions.textAlign ? `text-align: ${this.labelOptions.textAlign};` : "";
        const isMultiple = this.inputEditor.querySelector("#multiple-select-checkbox").checked;
        const templateName = isMultiple ? "checkbox-input-item" : "radio-input-item";

        const appState = Store.getComponentState(App);
        const isDefaultLocale = appState.current_localization === "default";

        const multipleChoiceValues = Array.from(this.inputEditor.querySelectorAll(".choices-container input")).map((input, index) => {
            const additionalClasses = input.parentElement.classList.contains("other-container") ? "other-container" : "" ;

            return Views.getViewElement(templateName, {
                value: !isDefaultLocale ? this.multipleChoiceInputs[index].querySelector("input").value : input.value.trim(),
                name: this.multipleChoiceName,
                label: input.value.trim(),
                classes: additionalClasses,
            })
        });

        this.sectionContainer.setAttribute('required', this.inputOptions.required);

        if(_event !== "discard") {
            this.sectionContainer.setAttribute('create-property', this.inputOptions.isCreatePropertyChecked);
            [true, "true"].includes(this.inputOptions.isCreatePropertyChecked) ? this.sectionContainer.setAttribute('property-name', this.inputOptions.propertyName) : this.sectionContainer.removeAttribute('property-name');
        }

        const originalTemplate = Views.getViewElement(this.finalTemplate);
        const originalTemplateContent = originalTemplate.querySelector('#userpilot-form');

        originalTemplateContent.innerHTML = `<div class="userpilot-form-label" style="${labelAlignment}">${labelValue}</div>`;
        multipleChoiceValues.forEach(item => {
            originalTemplateContent.appendChild(item);
        });
        this.sectionContainer.innerHTML = "";
        this.sectionContainer.appendChild(originalTemplateContent);
    }

    showEditorErrorMessage() {
        const messageElement = this.sectionContainer.querySelector('#content-editor .error-message');
        messageElement.style.display = 'block';
    }

    validateChanges() {
        const label = this.sectionContainer.querySelector('.userpilot-form-label');
        const inputText = Array.from(this.inputEditor.querySelectorAll('.choices-container input')).find(input => !input.value.trim());

        if (!label.innerText.trim()) {
            this.showLabelErrorMessage();
            return false;
        }
        if (inputText) {
            this.showEditorErrorMessage();
            return false;
        }
        return true;
    }
}

const LIKERT_SCALE_EMOJIS_RANGE_3 = [
    TemplateIcons.getLikertScaleEmojis(3),
    TemplateIcons.getLikertScaleEmojis(5),
    TemplateIcons.getLikertScaleEmojis(9),
];

const LIKERT_SCALE_EMOJIS_RANGE_5 = [
    TemplateIcons.getLikertScaleEmojis(3),
    TemplateIcons.getLikertScaleEmojis(5),
    TemplateIcons.getLikertScaleEmojis(6),
    TemplateIcons.getLikertScaleEmojis(9),
    TemplateIcons.getLikertScaleEmojis(10),
];

const LIKERT_SCALE_EMOJIS_RANGE_7 = [
    TemplateIcons.getLikertScaleEmojis(1),
    TemplateIcons.getLikertScaleEmojis(3),
    TemplateIcons.getLikertScaleEmojis(5),
    TemplateIcons.getLikertScaleEmojis(6),
    TemplateIcons.getLikertScaleEmojis(7),
    TemplateIcons.getLikertScaleEmojis(9),
    TemplateIcons.getLikertScaleEmojis(10),
];

const LIKERT_SCALE_EMOJIS_RANGE_10 = [
    TemplateIcons.getLikertScaleEmojis(1),
    TemplateIcons.getLikertScaleEmojis(2),
    TemplateIcons.getLikertScaleEmojis(3),
    TemplateIcons.getLikertScaleEmojis(4),
    TemplateIcons.getLikertScaleEmojis(5),
    TemplateIcons.getLikertScaleEmojis(6),
    TemplateIcons.getLikertScaleEmojis(7),
    TemplateIcons.getLikertScaleEmojis(8),
    TemplateIcons.getLikertScaleEmojis(9),
    TemplateIcons.getLikertScaleEmojis(10),
];

const LIKERT_SCALE_EMOJI_ICONS_RANGES = {
    3: LIKERT_SCALE_EMOJIS_RANGE_3,
    5: LIKERT_SCALE_EMOJIS_RANGE_5,
    7: LIKERT_SCALE_EMOJIS_RANGE_7,
    10: LIKERT_SCALE_EMOJIS_RANGE_10,
  };
  

export class LikertScaleSection extends InputSection {

    constructor(el, popupToolbar) {
        super(el, popupToolbar, "likert-scale-section-editor", "likert-scale-section");
    }

    readInputOptions() {
        super.readInputOptions();

        this.inputOptions.likertScaleRange = this.sectionContainer.querySelector(".likert-scale-steps").childElementCount
        this.inputOptions.likertScaleType = this.sectionContainer.getAttribute("type");
        this.inputOptions.likertScaleLowScoreLabel = this.sectionContainer.querySelector(".low-score-label").textContent;
        this.inputOptions.likertScaleHighScoreLabel = this.sectionContainer.querySelector(".high-score-label").textContent;
    }

    handleScoreMouseEnter = e => {
        let target = e.currentTarget;
        while (target) {
            target.classList.add('active');
            target = target.previousElementSibling;
        }
    };
    
    handleScoreMouseLeave = e => {
        let target = e.currentTarget;
        while (target) {
            target.classList.remove('active');
            target = target.previousElementSibling;
        }
    };

    initFormEventListeners() {
        const labelInput = this.sectionContainer.querySelector('#input-label-editor .userpilot-input');
        const lowScoreLabelInput = this.inputEditor.querySelector('.low-score-label-input');
        const highScoreLabelInput = this.inputEditor.querySelector('.high-score-label-input');
        const labelError = this.sectionContainer.querySelector('#input-label-editor .error-message');
        const lowScoreLabelMessageEl = this.sectionContainer.querySelector('.low-score-label-error-message');
        const highScoreLabelMessageEl = this.sectionContainer.querySelector('.high-score-label-error-message');


        const restErrors = () => {
            labelError.style.display = 'none';
            lowScoreLabelMessageEl.style.display = 'none';
            highScoreLabelMessageEl.style.display = 'none';
            labelInput.classList.remove('invalid');
            lowScoreLabelInput.classList.remove('invalid');
            highScoreLabelInput.classList.remove('invalid');
        }

        labelInput.addEventListener('input', restErrors);
        lowScoreLabelInput.addEventListener('input', restErrors);
        highScoreLabelInput.addEventListener('input', restErrors);

    }

    initLikertScaleStepListeners(step) {
        step.addEventListener("mouseenter", this.handleScoreMouseEnter);
        step.addEventListener("mouseleave", this.handleScoreMouseLeave);
    }

    initScoreLabelFieldsListeners() {
        const lowScoreLabelInput = this.inputEditor.querySelector(".low-score-label-input");
        const highScoreLabelInput = this.inputEditor.querySelector(".high-score-label-input");
        const lowScoreLabel = this.inputEditor.querySelector(".low-score-label");
        const highScoreLabel = this.inputEditor.querySelector(".high-score-label");

        lowScoreLabelInput.addEventListener("input", (e) => {
            e.stopPropagation();
            lowScoreLabel.textContent = lowScoreLabelInput.value;
        });
        highScoreLabelInput.addEventListener("input", (e) => {
            e.stopPropagation();
            highScoreLabel.textContent = highScoreLabelInput.value;
        });

    }

    switchToEditorView() {
        const labelRef = this.sectionContainer.querySelector('.userpilot-form-label');

        this.inputEditor = Views.getViewElement(this.editorTemplateName, {
            label: labelRef.innerHTML,
            lowScoreLabel: this.inputOptions.likertScaleLowScoreLabel,
            highScoreLabel: this.inputOptions.likertScaleHighScoreLabel,
            likertScalePreview: this.sectionContainer.querySelector(".likert-scale-preview").outerHTML,
            numbersIcon: TemplateIcons.getLikertScaleTypeIcon(LIKERT_SCALE_TYPES.NUMBERS),
            starsIcon: TemplateIcons.getLikertScaleTypeIcon(LIKERT_SCALE_TYPES.STARS),
            heartsIcon: TemplateIcons.getLikertScaleTypeIcon(LIKERT_SCALE_TYPES.HEARTS),
            smileysIcon: TemplateIcons.getLikertScaleTypeIcon(LIKERT_SCALE_TYPES.SMILEYS),
        });

        Array.from(this.inputEditor.querySelector(".likert-scale-steps").children).forEach(step => this.initLikertScaleStepListeners(step));

        this.inputEditor.querySelector('.userpilot-form-label').style.color = labelRef.style.color;
        this.sectionContainer.innerHTML = "";
        this.sectionContainer.appendChild(this.inputEditor);
        this.initFormEventListeners();
        this.initScoreLabelFieldsListeners();
        this.stopEventsPropagation();

        this.stopEventsPropagation();

        const rangeSelectMenu = this.inputEditor.querySelector('#range-select-menu');
        new SelectMenu(
            rangeSelectMenu,
            null,
            (item) => {
                this.inputOptions.likertScaleRange = item.getAttribute('value');
                this.renderLikertScale();
            },
            this.inputOptions.likertScaleRange,
            "likert-scale-range-select",
            false,
        );

        const typeSelectMenu = this.inputEditor.querySelector('#likert-scale-type-select-menu');
        new SelectMenu(
            typeSelectMenu,
            null,
            (item) => {
                this.inputOptions.likertScaleType = item.getAttribute('value');
                this.renderLikertScale();
            },
            this.inputOptions.likertScaleType,
            "likert-scale-type-select",
            false,
        );

    }

    generateNumbersScale = length => {
        return Array.from({ length }).map((_, index) => {
            const value = index + 1;
            return { label: value, value: value };
        });
      };
      
    generateStarsScale = (length, label) => {
        return Array.from({ length }).map((_, index) => {
            return { label: label, value: index + 1 };
        });
      };
      
    generateHeartsScale = (length, label) => {
        return Array.from({ length }).map((_, index) => {
            return { label: label, value: index + 1 };
        });
      };
      
    generateSmileysScale = (length, smileys) => {
        return Array.from({ length }).map((_, index) => {
            return { label: smileys[index], value: index + 1 };
        });
    };

    renderLikertScale() {
        const nodeEl = document.createElement("div");
        nodeEl.classList.add("likert-scale-steps");

        let scale = [];
        switch(this.inputOptions.likertScaleType) {
            case LIKERT_SCALE_TYPES.NUMBERS:
                scale = this.generateNumbersScale(this.inputOptions.likertScaleRange);
                break;

            case LIKERT_SCALE_TYPES.STARS:
                scale = this.generateStarsScale(this.inputOptions.likertScaleRange, TemplateIcons.getLikertScaleTypeIcon(LIKERT_SCALE_TYPES.STARS));
                break;

            case LIKERT_SCALE_TYPES.HEARTS:
                scale = this.generateHeartsScale(this.inputOptions.likertScaleRange, TemplateIcons.getLikertScaleTypeIcon(LIKERT_SCALE_TYPES.HEARTS));
                break;

            case LIKERT_SCALE_TYPES.SMILEYS:
                scale = this.generateSmileysScale(this.inputOptions.likertScaleRange, LIKERT_SCALE_EMOJI_ICONS_RANGES[this.inputOptions.likertScaleRange]);
                break;
        }
        scale.forEach(({label, value}) => {
            const step = document.createElement("div");
            step.innerHTML = label;
            step.setAttribute("data-index", value);
            this.initLikertScaleStepListeners(step);
            nodeEl.appendChild(step);

        });

        this.inputEditor.querySelector(".likert-scale-steps").replaceWith(nodeEl);
        
    }

    validateChanges() {
        const label = this.sectionContainer.querySelector('.userpilot-form-label');
        const lowScoreLabelInput = this.inputEditor.querySelector('.low-score-label-input');
        const highScoreLabelInput = this.inputEditor.querySelector('.high-score-label-input');

        if (!label.innerText.trim()) {
            this.showLabelErrorMessage();
            return false;
        }
        if (!lowScoreLabelInput.value.trim()) {
            const lowScoreLabelMessageEl = this.sectionContainer.querySelector('.low-score-label-error-message');
            lowScoreLabelMessageEl.style.display = 'block';
            lowScoreLabelInput.classList.add('invalid');
            return false;
        }
        if(!highScoreLabelInput.value.trim()) {
            const highScoreLabelMessageEl = this.sectionContainer.querySelector('.high-score-label-error-message');
            highScoreLabelMessageEl.style.display = 'block';
            highScoreLabelInput.classList.add('invalid');
            return false;
        }

        return true;
    }

    applyEditorChanges(_event) {
        const label = this.inputEditor.querySelector('.userpilot-form-label').innerHTML;
        const likertScalePreview = this.inputEditor.querySelector(".likert-scale-preview");

        const originalTemplateContent = Views.getViewElement(this.finalTemplate, {
            likertScalePreview: likertScalePreview.outerHTML,
        }).querySelector('#userpilot-form');

        this.sectionContainer.setAttribute('required', this.inputOptions.required);

        if(_event !== "discard") {
            this.sectionContainer.setAttribute('create-property', this.inputOptions.isCreatePropertyChecked);
            [true, "true"].includes(this.inputOptions.isCreatePropertyChecked) ? this.sectionContainer.setAttribute('property-name', this.inputOptions.propertyName) : this.sectionContainer.removeAttribute('property-name');
            
            this.sectionContainer.setAttribute("type", this.inputOptions.likertScaleType);
        }

        originalTemplateContent.querySelector('.userpilot-form-label').innerHTML = label;
        originalTemplateContent.querySelector('.userpilot-form-label').style.textAlign = this.labelOptions.textAlign;

        this.sectionContainer.appendChild(originalTemplateContent);
        this.inputEditor.remove();
    }
}