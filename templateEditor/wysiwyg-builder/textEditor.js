import BuilderSection from './commonEditor.js';
import htmlTreeOperations from '../../generic-utils/htmlTreeOperations.js';
import RangeOperations from './rangeOperations.js';
import FormValidator from "../../generic-utils/formValidator.js";
import CssOperations from "../../generic-utils/cssOperations.js";
import SelectMenu from '../../generic-components/selectMenu.js';
import InputSlider from "../../generic-components/inputSlider.js";
import URLInput from '../../generic-components/urlInput.js';
import SolidColorPicker from "../../generic-components/colorPickers/solidColorPicker.js";
import EmojiPicker from '../../generic-components/emojiPicker/emojiPicker.js';
import TextUndoRedoManager from "./UndoRedoManager/text.js";
import { runTextSectionIntegrity } from "./integrityChecks/textSectionIntegrity.js";
import GraphemeSplitter from "grapheme-splitter";
import WysiwygOperations from './utils/wysiwygOperations.js';
import TextEditorOperations from './utils/textEditorOperations.js';
import AskAI from './components/askAI/askAI.js';
import { addOpenSansFonts } from '../../app-initializer/initializeCssFiles.js';
import { aiFlow } from '../../components/engagement/content.js';
import Common from '../../index.js';
import Element from '../element/element.js';
import Views from '../views/templateViews.js';
import PositioningUserpilotContent from '../element/positioning-userpilot-content.js';
import MessageHandler from '../../routing/messageHandler.js';

export default class TextSection extends BuilderSection {
    static userpilotTextStyles = ["fontSize", "textTransform", "lineHeight", "letterSpacing", "color", "fontWeight", "fontStyle", "textDecoration"];

    static fontSizes = {
        normal: {
            size: "14",
            lineHeight: "25",
        },
        h1: {
            size: "35",
            lineHeight: "40",
        },
        h2: {
            size: "25",
            lineHeight: "30",
        },
        h3: {
            size: "20",
            lineHeight: "26",
        }
    }

    static getTextnodeSpan(empty = false) {
        const textSpan = document.createElement("span");
        if(!empty) textSpan.textContent = "\u200B";
        textSpan.classList.add("userpilot-text-node");

        return textSpan;
    }

    createTextNodeSpan = (empty = false) => {
        return TextSection.getTextnodeSpan(empty);
    }

    static getTextBuilderblock(withSpan = true) {
        const builderBlock = document.createElement("div");
        builderBlock.classList.add("userpilot-builder-block");
        if(withSpan) builderBlock.appendChild(this.getTextnodeSpan());

        return builderBlock;
    }

    static splitter = new GraphemeSplitter();

    constructor(el, popupToolbar) {
        super(el, popupToolbar);

        this.keyboardHandlerRef = this.keyboardHandler.bind(this);
        this.handleCaretMovementRef = this.handleCaretMovement.bind(this);
        this.wysiwygOperations = new TextEditorOperations(this);
    }

    currentFontValues = {
        fontSize: "",
        fontFamily: "",
        textTransform: "",
        lineHeight: "",
        letterSpacing: "",
        fontWeight: "",
        fontStyle: "",
        textDecoration: ""
    }

    /* Inititalizers */

    init() {
        runTextSectionIntegrity(this.sectionContainer, this.iframeDocument);
        super.init();
        this.undoRedoManager = new TextUndoRedoManager(this);
        this.sectionContainer.contentEditable = true;

        this.normalizeText();

        this.wysiwygOperations.collapseCaretOnNode(this.sectionContainer);
        this.findActiveStyles();

        this.initStylingEvents();
        this.initFontColorEventListeners();
        this.initEmojiEventListeners();
        this.initAskAI();
        setTimeout(this.showAITooltip, 300); // to wait for the WYSIWYG toolbar
        this.sectionContainer.addEventListener("keydown", this.keyboardHandlerRef, true);
        this.sectionContainer.addEventListener("keyup", this.handleCaretMovementRef, true);
        this.sectionContainer.addEventListener("mouseup", this.handleCaretMovementRef, true);

        this.sectionContainer.addEventListener("ai-is-calling", this.handleAICall, true);
        this.sectionContainer.addEventListener("keydown", this.askAI.handleKeydown, true);
        this.sectionContainer.addEventListener("paste", this.askAI.handlePasteEvent);
    }

    save(_event) {
        super.save();
        this.undoRedoManager.destroy();

        this.sectionContainer.style.caretColor = "";
        if (WysiwygOperations.isEmptyTextContainer(this.sectionContainer)) {
            this.sectionContainer.innerHTML = '<div class="userpilot-builder-block"><span class="userpilot-text-node"></span></div>';
        } else {
            runTextSectionIntegrity(this.sectionContainer, this.iframeDocument);
            this.wysiwygOperations.removeZeroWidthSpace();
            this.normalizeText();
        }

        this.sectionContainer.removeEventListener("keydown", this.keyboardHandlerRef, true);
        this.sectionContainer.removeEventListener("keyup", this.handleCaretMovementRef, true);
        this.sectionContainer.removeEventListener("mouseup", this.handleCaretMovementRef, true);

        this.sectionContainer.removeEventListener("ai-is-calling", this.handleAICall, true);
        this.sectionContainer.removeEventListener("keydown", this.askAI.handleKeydown, true);
        this.sectionContainer.removeEventListener("paste", this.askAI.handlePasteEvent);
    }

    handleAICall = (e) => this.askAI.handleAIResponse(e.detail);

    initPopupEvents(popupName, popupContainer, popupObj) {
        switch (popupName) {
            case "text-header-size":
                this.initTextTypeEvents(popupContainer, popupObj);
                break;
            case "text-typography":
                this.initTextTypographyEvents(popupContainer, popupObj);
                break;
            case "font-size":
                this.initFontSizeEvents(popupContainer, popupObj);
                break;
            case "justify-content":
                this.initAlignTextEvents(popupContainer, popupObj);
                break;
            case "text-list":
                this.initTextListEvents(popupContainer, popupObj);
                break;
            case "text-link":
                this.initTextLinkEvents(popupContainer, popupObj);
                break;
            case "text-personalize":
                this.initPersonalizeEvents(popupContainer, popupObj);
                break;
            case "section-padding":
                this.initSectionPaddingEvents(popupContainer);
                break;
            case "section-border":
                this.initSectionBorderEvents(popupContainer);
                break;
            case "section-background":
                this.initSectionBgEvents(popupContainer, popupObj);
                break;
            case "section-visibility":
                this.initVisibilityEvents(popupContainer, popupObj);
                break;
            default:
                break;
        }
    }

    updateFontStyleIcons() {
        const hasStyle = (values, styleText) => styleText.split(" ").some(value => values.includes(value));

        const boldBtn = this.popupToolbar.querySelector("#text-bold");
        const italicBtn = this.popupToolbar.querySelector("#text-italic");
        const underlineBtn = this.popupToolbar.querySelector("#text-underline");

        hasStyle(["bold", "700"], this.currentFontValues.fontWeight) ? boldBtn.classList.add("active"): boldBtn.classList.remove("active");
        hasStyle(["italic"], this.currentFontValues.fontStyle) ? italicBtn.classList.add("active"): italicBtn.classList.remove("active");
        hasStyle(["underline"], this.currentFontValues.textDecoration) ? underlineBtn.classList.add("active"): underlineBtn.classList.remove("active");
    }

    initStylingEvents() {
        const changeTextStyle = (prop, value) => {
            const textSpan = this.wysiwygOperations.selection.startContainer.nodeName === "#text" ? this.wysiwygOperations.selection.startContainer.parentElement : this.wysiwygOperations.selection.startContainer;

            const currentStyle = getComputedStyle(textSpan)[prop];
            (currentStyle.includes(value))
                ? this.changeFontProperty(prop == "textDecoration" ? "none" : "normal", prop)
                : this.changeFontProperty(value, prop);

            const offsets = RangeOperations.getRangeOffsetsBasedOnContainer(this.iframeDocument, this.wysiwygOperations.selection, this.sectionContainer);
            setTimeout(() => {
                this.normalizeText(offsets);
                this.sectionContainer.focus();
                this.findActiveStyles();
            });
        }

        const boldBtn = this.popupToolbar.querySelector("#text-bold");
        boldBtn.addEventListener("click", () => changeTextStyle("fontWeight", "700"));

        const italicBtn = this.popupToolbar.querySelector("#text-italic");
        italicBtn.addEventListener("click", () => changeTextStyle("fontStyle", "italic"));

        const underlineBtn = this.popupToolbar.querySelector("#text-underline");
        underlineBtn.addEventListener("click", () => changeTextStyle("textDecoration", "underline"));
    }

    initAskAI = () => {
        this.askAI = new AskAI({
            button: this.popupToolbar.querySelector("#ask-ai"),
            sectionEditor: this,
        });
    }

    handleCaretMovement() {
        setTimeout(this.findActiveStyles.bind(this), 100);
    }

    findActiveStyles() {
        const setUserpilotTextStyles = () => {
            const currentElement = (this.wysiwygOperations.selection.startContainer.nodeName == "#text") ? this.wysiwygOperations.selection.startContainer.parentElement : this.wysiwygOperations.selection.startContainer;
            if (currentElement === this.iframeDocument) return;
            const style = getComputedStyle(currentElement);
            TextSection.userpilotTextStyles.forEach(property => {
                this.currentFontValues[property] = style[property];
            });
            if (this.currentFontValues.letterSpacing == "normal") this.currentFontValues.letterSpacing = "0";
            if (this.currentFontValues.lineHeight == "normal") this.currentFontValues.lineHeight = "19px"
            this.updateTextTypeDisplay();
            this.updateFontSizeInput();
            this.updateFontColorIcon(style.color);
            this.updateFontStyleIcons();
        }
        setUserpilotTextStyles();
    }

    updateTextTypeDisplay() {
        const textTypeDisplay = this.popupToolbar.querySelector("#text-header-size");
        const lineHeight = parseFloat(this.currentFontValues.lineHeight);
        if (lineHeight >= parseInt(TextSection.fontSizes.h1.lineHeight)) {
            textTypeDisplay.textContent = "Header 1";
        } else if (lineHeight >= parseInt(TextSection.fontSizes.h2.lineHeight)) {
            textTypeDisplay.textContent = "Header 2";
        } else if (lineHeight >= parseInt(TextSection.fontSizes.h3.lineHeight)) {
            textTypeDisplay.textContent = "Header 3";
        } else {
            textTypeDisplay.textContent = "Normal";
        }
    }

    /* END */

    /* Font Type (Normal, h1..etc) */

    initTextTypeEvents(popupContainer, popupObj) {
        const textTypeDisplay = this.popupToolbar.querySelector("#text-header-size");
        const fontTypeOptions = popupContainer.querySelectorAll(".raw-option");
        fontTypeOptions.forEach(option => {
            option.addEventListener("click", () => {
                textTypeDisplay.textContent = option.textContent;
                this.changeCurrentFontType(option);
                popupObj.closePopup();
                this.findActiveStyles();
            });
        });

        popupObj.callbackOnPopupSpawn = () => {
            this.wysiwygOperations.highlightSelection(false);

            fontTypeOptions.forEach(option => {
                option.classList.remove("selected");
                if (textTypeDisplay.textContent.trim() == option.textContent.trim()) option.classList.add("selected");
            });
        }
        popupObj.callbackOnPopupClose = this.wysiwygOperations.unhighlightSelection;
    }

    changeCurrentFontType(option) {
        const fontType = option.getAttribute("value");
        this.changeFontProperty(TextSection.fontSizes[fontType].size + "px", "fontSize");
        this.changeFontProperty(TextSection.fontSizes[fontType].lineHeight + "px", "lineHeight");
    }

    /* END */


    /* Font Typography */

    typographyTimeout;
    initTextTypographyEvents(popupContainer, popupObj) {
        const changeTypography = (value, attribute) => {
            if (this.typographyTimeout) clearTimeout(this.typographyTimeout);
            this.typographyTimeout = setTimeout(() => this.changeFontProperty(value, attribute), 200);
        }

        /* DEPRECATED for now at least */
        // const fontFamilySelectBtn = popupContainer.querySelector("#font-family");
        // const fontFamilySelectOptions = ExperienceBuilderViews.getFontsSelectOptions();
        // const fontFamilySelect = new SelectMenu(fontFamilySelectBtn, fontFamilySelectOptions, (select) => {
        //     changeTypography(select.getAttribute("value"), "fontFamily");
        // });

        const letterCaseSelectBtn = popupContainer.querySelector("#font-case");
        const letterCaseSelect = new SelectMenu(letterCaseSelectBtn, null, (select) => {
            changeTypography(select.getAttribute("value"), "textTransform");
        });

        const lineHeightSliderView = popupContainer.querySelector("#line-height-slider");
        const lineHeightSlider = new InputSlider(lineHeightSliderView, (value) => {
            changeTypography(value + "px", "lineHeight");
        });

        const letterSpacingSliderView = popupContainer.querySelector("#letter-space-slider");
        const letterSpacingSlider = new InputSlider(letterSpacingSliderView, (value) => {
            changeTypography(value + "px", "letterSpacing");
        });

        popupObj.callbackOnPopupSpawn = () => {
            // fontFamilySelect.setValue(this.currentFontValues.fontFamily.replace(/^"(.*)"$/, '$1'));
            letterCaseSelect.setValue(this.currentFontValues.textTransform);
            lineHeightSlider.changeSliderValue(parseFloat(this.currentFontValues.lineHeight));
            letterSpacingSlider.changeSliderValue(parseFloat(this.currentFontValues.letterSpacing));
            this.wysiwygOperations.highlightSelection();
        }
        popupObj.callbackOnPopupClose = this.wysiwygOperations.unhighlightSelection;
    }
    /* END */

    /* Font Styles */

    initFontColorEventListeners() {
        const fontColorIcon = this.popupToolbar.querySelector("#font-color");
        new SolidColorPicker(fontColorIcon, this.getFontColor.bind(this), {
            onInitCallback: this.wysiwygOperations.highlightSelection,
            onChangeCallback: this.changeFontColor.bind(this),
            onSaveCallback: this.wysiwygOperations.unhighlightSelection,
            onHideCallback: this.wysiwygOperations.unhighlightSelection,
        });
    }

    getFontColor() {
        return getComputedStyle(this.wysiwygOperations.selection.startContainer.parentElement).color;
    }

    updateFontColorIcon(color) {
        const fontColorIcon = this.popupToolbar.querySelector("#font-color");
        fontColorIcon.querySelector("svg").style.fill = color;
    }

    inputTimeout;
    changeFontColor(color) {
        clearTimeout(this.inputTimeout);
        this.inputTimeout = setTimeout(() => {
            this.changeFontProperty(color, "color");
        }, 300);
    }

    /* END */

    /* Font Size */

    updateFontSizeInput() {
        const input = this.popupToolbar.querySelector("#font-size input");
        input.value = parseFloat(this.currentFontValues.fontSize);
    }

    initFontSizeEvents(popupContainer, popupObj) {
        const toolbarFontSizeContainer = this.popupToolbar.querySelector("#font-size");
        const input = toolbarFontSizeContainer.querySelector("#font-size input");
        const fontOptions = Array.from(popupContainer.querySelectorAll(".popup-container > div"));
        let initialFontSize;

        const changeFontSize = (value, refocusText = true) => {
            this.changeFontProperty(parseFloat(value) + "px", "fontSize", refocusText);
        }

        let inputTimeout;
        input.addEventListener("input", () => {
            if (!input.value) return;
            const value = parseFloat(input.value);
            clearTimeout(inputTimeout);
            inputTimeout = setTimeout(() => {
                changeFontSize(value, false);
                input.focus();
            }, 500);
        });

        input.addEventListener("keydown", (event) => {
            if (!input.value) return;
            if (!["ArrowUp", "ArrowDown", "Enter", "Escape"].includes(event.code)) return;
            event.preventDefault();

            let value = input.value;
            let refocusInput = false;
            switch(event.code) {
                case "ArrowUp":
                    input.value = ++value;
                    refocusInput = true;
                    break;
                case "ArrowDown":
                    input.value = --value;
                    refocusInput = true;
                    break;
                case "Enter":
                case "Escape":
                    popupObj.closePopup();
                    break;
            }

            if (event.code == "Escape") {
                input.value = initialFontSize;
                changeFontSize(initialFontSize);
            }
            else changeFontSize(value, false);

            if(refocusInput) input.focus();
        });

        fontOptions.forEach(option => option.addEventListener("click", () => {
            changeFontSize(option.textContent);
            popupObj.closePopup();
        }));

        popupObj.callbackOnPopupSpawn = () => {
            initialFontSize = parseFloat(this.currentFontValues.fontSize);
            toolbarFontSizeContainer.classList.add("active");
            this.wysiwygOperations.highlightSelection();
            input.setSelectionRange(input.value.length, input.value.length);
            input.focus();
        }

        popupObj.callbackOnPopupClose = () => {
            toolbarFontSizeContainer.classList.remove("active");
            this.wysiwygOperations.unhighlightSelection();
            this.wysiwygOperations.refocusText();
            this.findActiveStyles();
        }
    }

    /* END */


    /* Paragraph Styles */

    initTextListEvents(popupContainer, popupObj) {
        const orderBtns = Array.from(popupContainer.querySelectorAll(".order-li"));
        orderBtns.forEach(btn => {
            btn.addEventListener("click", () => {
                this.execListCommand(btn.getAttribute("value"));
                popupObj.closePopup();
            });
        });

        popupObj.callbackOnPopupSpawn = () => {
            this.highlightActiveOrderBtn(orderBtns);
            this.highlightSelectedBlocks();
        }

        popupObj.callbackOnPopupClose = () => {
            this.unhighlightSelectedBlocks();
        }
    }

    execListCommand(type) {
        this.undoRedoManager.saveForUndo(TextUndoRedoManager.MUTATION_TYPES.CUSTOM);

        const isUndoList = this.elementsToEdit.every(selectedBlock => (
            WysiwygOperations.isBlockUnderList(selectedBlock) && selectedBlock.parentElement.tagName === type
        ));

        isUndoList ? this.unlist() : this.list(type);

        this.wysiwygOperations.collapseCaretOnNode(this.elementsToEdit[this.elementsToEdit.length - 1]);
    }

    list(type) {
        const insertAfterBlock = WysiwygOperations.isBlockUnderList(this.elementsToEdit[0]) ? this.elementsToEdit[0].parentElement : this.elementsToEdit[0];
        const listContainer = document.createElement(type);

        htmlTreeOperations.insertAfter(insertAfterBlock, listContainer);
        this.elementsToEdit.forEach(block => {
            listContainer.appendChild(block);
        });
        if (!RangeOperations.isRangeSingleCaret(this.wysiwygOperations.selection)) this.removeEmptyLists();
    }

    unlist() {
        const selectedLists = new Set(this.elementsToEdit.map(block => block.parentElement));
        selectedLists.forEach(list => {
            const selectedChildrenBlocks = this.elementsToEdit.filter(block => block.parentElement === list);
            const firstSelectedBlock = selectedChildrenBlocks[0];
            const lastSelectedBlock = selectedChildrenBlocks[selectedChildrenBlocks.length -1];

            const direction = list.firstElementChild === firstSelectedBlock ? "left"
                : list.lastElementChild === lastSelectedBlock ? "right"
                : "middle";

            switch(direction) {
                case "left":
                    selectedChildrenBlocks.forEach(block => list.parentElement.insertBefore(block, list));
                    break;
                case "right":
                    selectedChildrenBlocks.reverse().forEach(block => htmlTreeOperations.insertAfter(list, block));
                    break;
                case "middle":
                    const listClone = list.cloneNode(false);
                    while(lastSelectedBlock.nextElementSibling) listClone.appendChild(lastSelectedBlock.nextElementSibling);
                    selectedChildrenBlocks.reverse().forEach(block => htmlTreeOperations.insertAfter(list, block));
                    if (listClone.children.length) htmlTreeOperations.insertAfter(lastSelectedBlock, listClone);
            }
        });
        this.removeEmptyLists();
    }

    highlightActiveOrderBtn(orderBtns) {
        const isUndoList = this.elementsToEdit.every(selectedBlock => WysiwygOperations.isBlockUnderList(selectedBlock));
        const type = this.elementsToEdit[0].parentElement.tagName;

        orderBtns.forEach(btn => btn.classList.remove("selected"));
        if (isUndoList && type) orderBtns.find(btn => btn.getAttribute("value") === type).classList.add("selected");
    }

    removeEmptyLists() {
        Array.from(this.sectionContainer.querySelectorAll("ul, ol"))
        .filter(list => !list.textContent.replaceAll("\u200B", "").trim().length)
        .forEach(list => list.remove());
    }

    /* END */

    /* Text Link */

    textLinkPopup;
    initTextLinkEvents(popupContainer, popupObj) {
        const textInput = popupContainer.querySelector("#link-text-input");
        const submitButton = popupContainer.querySelector(".submit");
        const unlinkButton = popupContainer.querySelector("#unlink-text");
        const urlInstance = new URLInput(popupContainer.querySelector(".url-section-v2"), { propertiesContainer: popupContainer });

        this.textLinkPopup = popupObj;

        popupObj.callbackOnPopupSpawn = () => {
            const linkElement = RangeOperations.getFirstNodeTypeInRange(this.iframeDocument, this.wysiwygOperations.selection, this.sectionContainer, "A");
            if (linkElement) {
                urlInstance.setUrlBasedOnAnchor(linkElement);
                unlinkButton.style.display = "";
            } else {
                urlInstance.resetValidation();
                urlInstance.urlInput.parentElement.setAttribute("data-replicated-value", "")
                urlInstance.setUrl("", URLInput.protocols.HTTPS, true);
                unlinkButton.style.display = "none";
            }
            textInput.value = this.wysiwygOperations.selection.toString();
            this.wysiwygOperations.highlightSelection();
        }
        popupObj.callbackOnPopupClose = this.wysiwygOperations.unhighlightSelection;

        popupObj.validator = new FormValidator(popupContainer, submitButton, null, () => {
            this.link(textInput.value, urlInstance, popupObj);
            popupObj.closePopup();
        }, { validateUrlsOnFirstRun: false });

        unlinkButton.addEventListener("click", this.unlink.bind(this, popupObj));

        this.initAnchorElementsEvents();
    }

    initAnchorElementsEvents() {
        Array.from(this.sectionContainer.querySelectorAll("a"))
        .forEach(node => this.initLinkElementEvents(node));
    }

    initLinkElementEvents(linkNode) {
        linkNode.addEventListener("click", () => {
            setTimeout(() => {
                this.wysiwygOperations.selection.selectNodeContents(linkNode);
                this.wysiwygOperations.highlightSelection();
                this.textLinkPopup.spawnPopup();
            }, 50);
        });
    }

    link(text, urlInstance, popupObj) {
        const linkNode = urlInstance.createAnchorNode(text);

        this.undoRedoManager.saveForUndo(TextUndoRedoManager.MUTATION_TYPES.CUSTOM);

        if (RangeOperations.isRangeSingleCaret(this.wysiwygOperations.selection)) {
            if (this.wysiwygOperations.selection?.startContainer?.parentElement?.tagName == "A") this.wysiwygOperations.selection.setStartAfter(this.wysiwygOperations.selection.startContainer.parentElement);
            this.wysiwygOperations.selection.insertNode(linkNode);
            RangeOperations.setTextCaretAfterNode(this.wysiwygOperations.selection, linkNode);
            this.initLinkElementEvents(linkNode, popupObj);
        } else {
            if(RangeOperations.isMultipleBlocks(this.wysiwygOperations.selection)) {
                const firstSelectedBlock = this.elementsToEdit[0];
                const startContainer = this.wysiwygOperations.selection.startContainer.nodeName === "#text" ? this.wysiwygOperations.selection.startContainer.parentElement : this.wysiwygOperations.selection.startContainer;
                const { parent, lastChild } = htmlTreeOperations.getDirectParentsTreeCopy(startContainer, firstSelectedBlock);

                lastChild.appendChild(linkNode);

                RangeOperations.replaceSelectionWithNode(this.iframeDocument, this.sectionContainer, this.wysiwygOperations.selection, parent, ["a"]);
                this.initLinkElementEvents(linkNode, popupObj);
                this.wysiwygOperations.selection.selectNodeContents(linkNode.firstChild);
                this.wysiwygOperations.removeEmptyBlocks();
            } else {
                RangeOperations.replaceSelectionWithNode(this.iframeDocument, this.sectionContainer, this.wysiwygOperations.selection, linkNode, ["a"]);
                this.initLinkElementEvents(linkNode, popupObj);
                this.wysiwygOperations.selection.selectNodeContents(linkNode.firstChild);
            }
        }
        CssOperations.highlightElementForDuration(linkNode, "#DD5584", 1500);
    }

    unlink(popupObj) {
        RangeOperations.unwrapAllNodesOfTypeInRange(this.iframeDocument, this.sectionContainer, this.wysiwygOperations.selection, ["a"]);
        this.wysiwygOperations.collapseCaretOnNode(this.wysiwygOperations.selection.startContainer);
        this.findActiveStyles();
        popupObj.closePopup();
    }

    /* END */

    /* EMOJI PICKER */

    initEmojiEventListeners = () => {
        const emojiIcon = this.popupToolbar.querySelector("#emojis");
        const emojiOptions = {
            rootElement: this.iframeDocument.body
        }

        const onEmojiCallback = selection => {
            const emoji = document.createTextNode(selection.emoji);

            this.undoRedoManager.saveForUndo(TextUndoRedoManager.MUTATION_TYPES.CUSTOM);

            if(RangeOperations.isMultipleBlocks(this.wysiwygOperations.selection)) {
                const firstSelectedBlock = this.elementsToEdit[0];
                const startContainer = this.wysiwygOperations.selection.startContainer.nodeName === "#text" ? this.wysiwygOperations.selection.startContainer.parentElement : this.wysiwygOperations.selection.startContainer;
                const { parent, lastChild } = htmlTreeOperations.getDirectParentsTreeCopy(startContainer, firstSelectedBlock);

                lastChild.appendChild(emoji);
                RangeOperations.replaceSelectionWithNode(this.iframeDocument, this.sectionContainer, this.wysiwygOperations.selection, parent);
                this.wysiwygOperations.removeEmptyBlocks();
            } else {
                RangeOperations.replaceSelectionWithNode(this.iframeDocument, this.sectionContainer, this.wysiwygOperations.selection, emoji);
            }

            this.wysiwygOperations.removeHighlightBackgrounds();
            this.wysiwygOperations.selection.setStart(emoji, emoji.length);
            this.wysiwygOperations.selection.setEnd(emoji, emoji.length);
        }

        const onShowCallback = () => {
            this.wysiwygOperations.highlightSelection();
            emojiIcon.classList.add("active");
        }

        const onHideCallback = () => {
            this.wysiwygOperations.unhighlightSelection();
            this.wysiwygOperations.refocusText();
            emojiIcon.classList.remove("active");
        }

        new EmojiPicker(emojiIcon, emojiOptions, this.iframeDocument, onEmojiCallback, onShowCallback, onHideCallback);
    }

    /* END */

    /* Keyboard Handler */

    keyboardHandler(event) {
        switch(event.code) {
            case "Enter":
                return this.handleEnterKey(event);
            case "Backspace":
                return this.wysiwygOperations.handleBackspaceKey(event);
            default:
                return true;
        }
    }

    handleEnterKey(event) {
        const isShiftKeyHeld = event.shiftKey;
        const firstSelectedBlock = this.elementsToEdit[0];
        const isEmptyListItem = WysiwygOperations.isBlockUnderList(firstSelectedBlock) && !firstSelectedBlock.textContent.replaceAll("\u200B", "").trim().length;

        if(isEmptyListItem && !isShiftKeyHeld) {
            return this.handleEnterOnEmptyListItem(event, firstSelectedBlock, firstSelectedBlock.parentElement);
        } else {
            const textNode = this.wysiwygOperations.selection.endContainer;
            const textNodeLength = textNode.wholeText.length;
            const endOffset = this.wysiwygOperations.selection.endOffset;
            if (textNodeLength === endOffset) htmlTreeOperations.insertAfter(textNode, document.createTextNode("\u200B"));
        }

        return true;
    }

    handleEnterOnEmptyListItem(event, currListItem, list) {
        event.preventDefault();
        this.undoRedoManager.saveForUndo(TextUndoRedoManager.MUTATION_TYPES.CUSTOM);

        const listItems = Array.from(list.children);
        const currListItemIndex = listItems.indexOf(currListItem);
        const nextListItems = Array.from(list.children).filter((child, i) => i > currListItemIndex);

        if (nextListItems.length) {
            const listClone = list.cloneNode(false);
            nextListItems.forEach(item => listClone.appendChild(item));
            htmlTreeOperations.insertAfter(list, listClone);
        }

        htmlTreeOperations.insertAfter(list, currListItem);
        this.removeEmptyLists();
        this.wysiwygOperations.collapseCaretOnNode(currListItem);
        return false;
    }

    /* END */


    /* Helpers */

    normalizeText(offsets) {
        super.normalizeText(offsets, this.sectionContainer, TextSection.userpilotTextStyles, "div.userpilot-builder-block > span")
    }

    fixUnderlineInheritance() {
        Array.from(this.sectionContainer.querySelectorAll("span[style*='text-decoration: underline'"))
        .forEach(span => {
            const isMainSpan = span.parentElement.classList.contains("userpilot-builder-block");
            if (isMainSpan) htmlTreeOperations.wrapNode(span, TextSection.getTextnodeSpan(true));

            htmlTreeOperations.flattenNode(span, this.iframeDocument, ["A", "UP-CONTAINER"], true);
        });
    }

    /* END */

    showAITooltip = async () => {
        this.iframeDocument.querySelector("#ai-tooltip")?.remove();
        if(Common.userData.engagementData[aiFlow.id]?.isCompleted) return;

        const tooltipStep = document.createElement("div");
        tooltipStep.style.cssText = `display: flex; align-items: center; justify-content: center; box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.25); position: relative; border-radius: 4px;`;

        const tooltipSlide = document.createElement("div");
        tooltipSlide.style.cssText = `background-color: white; position: relative; z-index: 1; border-radius: 4px;`;

        tooltipSlide.innerHTML = aiFlow.steps[0].template;
        tooltipSlide.querySelector(".userpilot-btn").addEventListener("click", this.dismissAITooltip);
        tooltipStep.appendChild(tooltipSlide);
        this.renderBeacon(tooltipStep);

        const iframe = await this.prepareAIIframe(tooltipStep);
        const targetElement = this.popupToolbar.querySelector("#ask-ai");
        const bodyZoom = htmlTreeOperations.getBodyZoom();
        const direction = 'top-center';

        const positionTooltip = () => {
            const targetElementCoords = Element.getBoundingClientRect(targetElement, null, null, bodyZoom);
            PositioningUserpilotContent.position({
                tooltip: iframe,
                target: targetElement,
                targetCoords: targetElementCoords,
                direction: direction,
                bodyZoom: bodyZoom,
                beaconParent: tooltipStep,
            });
        }
        positionTooltip();
        this.aiTooltipInterval = setInterval(positionTooltip, 500);

        this.initDismissTooltipListeners();
        this.updateEngagementData(aiFlow.id);
    }

    prepareAIIframe = async (tooltipStep) => {
        const iframe = document.createElement("iframe");
        iframe.id = "ai-tooltip";
        this.iframeDocument.body.appendChild(iframe);
        const iframeContentType = iframe.contentDocument || iframe.contentWindow.document;

        await new Promise(resolve => {
            const iframeOnload = async () => {
                const iframeHead = iframe.contentDocument.head;
                const iframeBody = iframe.contentDocument.body;
                const cssSources = [
                    Common.baseURL + "/templateEditor/views/css/sections.css",
                    Common.baseURL + "/commonCSS/theme.css",
                    Common.baseURL + "/commonCSS/animations.css",
                    Common.baseURL + "/commonCSS/userpilotTemplates.css",
                    Common.baseURL + "/commonCSS/tooltips.css",
                    Common.baseURL + "/templateEditor/views/css/iframe.css",
                    Common.baseURL + "/commonCSS/userpilotGenerics.css",
                    Common.baseURL + "/views/css/engagementOverrides.css",
                ];

                await Promise.all(cssSources.map(source => {
                    let cssLink = document.createElement("link");
                    cssLink.href = source;
                    cssLink.rel = "stylesheet";
                    iframeHead.appendChild(cssLink);
                    return new Promise((resolve, reject) => {
                        cssLink.addEventListener("load", resolve);
                        setTimeout(reject, 1000);
                    });
                })).catch(reject => console.warn("Userpilot: Some stylesheets failed to load", reject));

                addOpenSansFonts(iframeHead);
                iframeBody.appendChild(tooltipStep);
                iframeBody.style.cssText = 'padding: 12px;';
                resolve();
            }

            //run the iframeOnload if the iframe.onload event is already fired
            if (iframeContentType && iframeContentType.readyState === 'complete') iframeOnload();
            else iframe.contentWindow.addEventListener('load', iframeOnload);
        });

        return iframe;
    }

    renderBeacon = (container) => {
        if(!container.querySelector(".userpilot-beacon")) {
            const normalBeacon = Views.getViewElement("userpilot-beacon", { beaconID: "beacon-arrow", type: "arrow", classes: "" });
            normalBeacon.style.backgroundColor = "white";
            normalBeacon.style.boxShadow = '2px 2px 4px rgba(0, 0, 0, 0.25)';
            container.appendChild(normalBeacon);
        }
    }

    updateEngagementData = (contentId) => {
        Common.userData.engagementData[contentId] = {
            ...Common.userData.engagementData[contentId],
            isCompleted: true,
        };
        MessageHandler.postEngagementData(contentId, { isCompleted: true });
    }

    dismissAITooltip = () => {
        clearInterval(this.aiTooltipInterval);
        this.iframeDocument.querySelector("#ai-tooltip")?.remove();
    }

    initDismissTooltipListeners = () => {
        const aiButton = this.popupToolbar.querySelector("#ask-ai");
        const saveSectionBtn = this.popupToolbar.querySelector("#save-section");
        const discardChangesBtn = this.popupToolbar.querySelector("#discard-section");
        const removeSectionBtn = this.popupToolbar.querySelector("#remove-section");
        const cloneSectionBtn = this.popupToolbar.querySelector("#clone-section");

        [
            aiButton,
            saveSectionBtn,
            discardChangesBtn,
            removeSectionBtn,
            cloneSectionBtn
        ].forEach(element => {
            element.addEventListener("click", this.dismissAITooltip);
        })
    }
}