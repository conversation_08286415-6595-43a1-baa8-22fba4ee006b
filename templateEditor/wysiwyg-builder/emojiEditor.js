import BuilderSection from './commonEditor.js';
import InputSlider from '../../generic-components/inputSlider.js';
import EmojiPicker from '../../generic-components/emojiPicker/emojiPicker.js';

export default class EmojiSection extends BuilderSection {

    constructor(el, popupToolbar) {
        super(el, popupToolbar);
    }

    /* Initializers */

    init() { 
        super.init();
        this.initEmojiEventListeners();
    }

    initPopupEvents(popupName, popupContainer, popupObj) {
        switch(popupName) {
            case "emoji-size": 
                this.initEmojiSizeEvents(popupContainer, popupObj);
                break;
            case "justify-content":
                this.initJustifySectionEvents(popupContainer);
                break;
            case "section-padding":
                this.initSectionPaddingEvents(popupContainer);
                break;
            case "section-background":
                this.initSectionBgEvents(popupContainer, popupObj);
                break;
            case "section-visibility":
                this.initVisibilityEvents(popupContainer, popupObj);
                break;
            default:
                break;
        }
    }

    /* END */

    /* Image Styles */

    initEmojiSizeEvents(popupContainer, popupObj) {
        const emojiSizeSliderView = popupContainer.querySelector("#emoji-size-slider");
        const emojiSizeSlider = new InputSlider(emojiSizeSliderView, this.changeEmojiSize);

        popupObj.callbackOnPopupSpawn = () => {
            const fontSize = parseFloat(getComputedStyle(this.sectionContainer).fontSize);
            emojiSizeSlider.changeSliderValue(fontSize);
        }
    }

    changeEmojiSize = (value) => {
        this.sectionContainer.style.fontSize = value + "px";
    }

    /* END */

    /* Emoji picker */

    initEmojiEventListeners () {
        const emojiIcon = this.popupToolbar.querySelector("#emojis");
        const emojiOptions = {
            rootElement: this.iframeDocument.body
        }

        const onEmojiCallback = selection => this.sectionContainer.innerHTML = selection.emoji;

        const onShowCallback = () => emojiIcon.classList.add("active");

        const onHideCallback = () => emojiIcon.classList.remove("active");

        new EmojiPicker(emojiIcon, emojiOptions, this.iframeDocument, onEmojiCallback, onShowCallback, onHideCallback);

        this.sectionContainer.addEventListener("open-emoji-picker", () => emojiIcon.dispatchEvent(new Event("mouseup")));
    }

    /* END */

}