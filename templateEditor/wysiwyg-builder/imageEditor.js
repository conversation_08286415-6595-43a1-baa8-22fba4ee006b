import FormValidator from '../../generic-utils/formValidator.js';
import BuilderSection from './commonEditor.js';
import htmlTreeOperations from '../../generic-utils/htmlTreeOperations.js';
import ExperienceBuilderViews from "../../views/experienceBuilderViews.js";
import SelectMenu from "../../generic-components/selectMenu.js";
import InputSlider from '../../generic-components/inputSlider.js';
import TabSwitcher from "./tabSwitcher.js";
import URLInput from '../../generic-components/urlInput.js';
import ImageAdder from './components/imageAdder.js';
import Action, { ACTION_TYPES } from './components/Action/action.js';

export default class ImageSection extends BuilderSection {

    constructor(el, popupToolbar) {
        super(el, popupToolbar);
        this.image;
        this.linkingType = 'url';
        this.initImageActionsEventsFn;
    }

    /* Initializers */

    init() {
        this.image = this.sectionContainer.querySelector('img');
        if(!this.image) this.popupToolbar.classList.add("no-image");
        if(this.image) this.addDefaultImageAction();
        super.init();
    }

    save(_event) {
        super.save();
        this.popupToolbar.classList.remove("no-image");
        if (!this.image) this.sectionContainer.innerHTML = "<empty></empty>";
    }

    initPopupEvents(popupName, popupContainer, popupObj) {
        switch(popupName) {
            case "image-upload":
                this.initImageEvents(popupContainer, popupObj);
                break;
            case "image-dimensions":
                this.initImageWidthEvents(popupContainer, popupObj);
                break;
            case "justify-content":
                this.initJustifySectionEvents(popupContainer);
                break;
            case "image-opacity":
                this.initImageOpacityEvents(popupContainer, popupObj);
                break;
            case "accessibility-actions":
                this.initAccessibilityActions(popupContainer, popupObj);
                break;
            case "image-actions":
                this.initImageActionsEventsFn = this.initImageActionsEvents(popupContainer, popupObj);
                this.initImageActionsEventsFn();
                break;
            case "section-padding":
                this.initSectionPaddingEvents(popupContainer);
                break;
            case "section-background":
                this.initSectionBgEvents(popupContainer, popupObj);
                break;
            case "section-visibility":
                this.initVisibilityEvents(popupContainer, popupObj);
                break;
            default:
                break;
        }
    }

    /* END */

    /* Upload */

    initImageEvents(popupContainer, popupObj) {
        const imageAdderComponent = popupContainer.querySelector(".image-upload-container");
        const imageAdder = new ImageAdder(imageAdderComponent, this.image?.src, this.addImageToSection, this.removeImageFromSection.bind(this));

        if(!this.image) popupObj.spawnPopup();
        popupObj.callbackOnPopupSpawn = () => imageAdder.resetUrlField();
    }

    addImageToSection = (url) => {
        if(this.image) {
            this.image.src = url;
        }
        else {
            this.image = document.createElement('img');
            this.image.classList.add("userpilot-img");
            this.sectionContainer.innerHTML = "";
            this.sectionContainer.appendChild(this.image);
            this.image.src = url;
            this.image.setAttribute("userpilot-act", ACTION_TYPES.NO_ACTION.value);
            this.initImageActionsEventsFn();
        }
        this.popupToolbar.classList.remove("no-image");
    }

    removeImageFromSection() {
        this.sectionContainer.innerHTML = "<empty></empty>";
        this.image = null;
        this.popupToolbar.classList.add("no-image");
    }

    /* END */

    /* Image Styles */

    initImageWidthEvents(popupContainer, popupObj) {
        const widthSliderView = popupContainer.querySelector("#image-width-slider");
        const widthSlider = new InputSlider(widthSliderView, this.changeImageWidth.bind(this));

        popupObj.callbackOnPopupSpawn = () => {
            const width = parseFloat(getComputedStyle(this.image).width);
            const sectionStyle = getComputedStyle(this.sectionContainer);
            const calculatedMax = parseFloat(sectionStyle.width) - (parseFloat(sectionStyle.paddingLeft) + parseFloat(sectionStyle.paddingRight));

            widthSlider.changeMax(calculatedMax);
            widthSlider.changeSliderValue(width);
        }
    }

    changeImageWidth(value) {
        this.image.style.width = value + "px";
    }

    changeImageHeight(value) {
        this.image.style.height = value + "px";
    }

    initImageOpacityEvents(popupContainer, popupObj) {
        const opacitySliderView = popupContainer.querySelector("#image-opacity-slider");
        const opacitySlider = new InputSlider(opacitySliderView, this.changeOpacity.bind(this));
        popupObj.callbackOnPopupSpawn = () => opacitySlider.changeSliderValue(getComputedStyle(this.image).opacity * 100);
    }

    changeOpacity(value) {
        this.image.style.opacity = value / 100;
    }

    initAccessibilityActions(popupContainer, popupObj) {
        const altTextInput = popupContainer.querySelector("#alt-text-input");
        altTextInput.addEventListener("input", () => this.image.setAttribute("alt", altTextInput.value));
        popupObj.callbackOnPopupSpawn = () => altTextInput.value = this.image.getAttribute("alt");
    }

    /* END */

    /* Image Action */

    initImageActionsEvents = (popupContainer, popupObj) => {
        const popupContainerCloned = popupContainer.cloneNode(true);

        return () => {
            if(!this.image) return;
            popupContainer.innerHTML = popupContainerCloned.innerHTML;

            const actionSelectBtn = popupContainer.querySelector("#button-action-select");
            const action = new Action(this.image, this.sectionContainer, popupContainer, actionSelectBtn, [ACTION_TYPES.NO_ACTION, ACTION_TYPES.GO_TO_URL, ACTION_TYPES.FLOW, ACTION_TYPES.SURVEY]);

            popupObj.callbackOnPopupSpawn = () => {
                action.setBtnActionPopupState(action.selectMenu);

                const imgHasAnchor = this.image.parentElement.tagName == "A";
                const imageUserpilotAttr = this.image.getAttribute("userpilot-action")

                switch(imageUserpilotAttr) {
                    case ACTION_TYPES.FLOW.value:
                        this.handleNavigateCheckState({
                            imgHasAnchor,
                            urlInstance: action.triggerFlowProps.urlInstance,
                            navigateCheck: popupContainer.querySelector(".flow-options #navigate-to-url")
                        });
                        break;
                    
                    case ACTION_TYPES.SURVEY.value:
                        this.handleNavigateCheckState({
                            imgHasAnchor,
                            urlInstance: action.triggerSurveyProps.urlInstance,
                            navigateCheck: popupContainer.querySelector(".survey-options #trigger-survey-navigate-to-url")
                        });
                        break;

                    default:
                        if(imgHasAnchor) action.urlActionProps.urlInstance.setUrlBasedOnAnchor(this.image.parentElement);
                }
            }
        }
    }

    addDefaultImageAction = () => {
        if(!this.image.getAttribute("userpilot-action") && this.image.parentElement.tagName !== "A") this.image.setAttribute("userpilot-action", ACTION_TYPES.NO_ACTION.value);
    }

    handleNavigateCheckState = ({imgHasAnchor, urlInstance, navigateCheck}) => {
        if (imgHasAnchor) {
            const anchorNode = this.image.parentElement;
            urlInstance.setUrlBasedOnAnchor(anchorNode);
            navigateCheck.checked = true;
            navigateCheck.dispatchEvent(new Event("click"));
        } else {
            navigateCheck.checked = false;
            navigateCheck.dispatchEvent(new Event("click"));
        }
    }

    /* END */
}