import WysiwygOperations from "./wysiwygOperations";

export default class HeaderEditorOperations extends WysiwygOperations {
    constructor(editor) {
        super(editor);
        this.initPasteListener();
    }

    removeZeroWidthSpace = () => {
        this.editor.sectionContainer.querySelectorAll(".userpilot-builder-block").forEach(block => block.innerHTML = block.innerHTML.replaceAll("\u200B", "").trim());
    }
}