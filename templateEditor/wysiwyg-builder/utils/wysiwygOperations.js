import htmlTreeOperations from "../../../generic-utils/htmlTreeOperations";
import { cleanText } from "../../../generic-utils/textTransformation";
import Views from "../../views/templateViews";
import RangeOperations from "../rangeOperations";
import TextUndoRedoManager from "../UndoRedoManager/text";
import UndoRedoManager from "../UndoRedoManager/undoRedoManager";
import WysiwigBuilder from "../wysiwigBuilder";
import TextSection from "../textEditor";
import Common from "../../../index";

export default class WysiwygOperations {
    static isEmptyTextContainer = (sectionContainer) => {
        return !sectionContainer.textContent.replaceAll("\u200B", "").trim().length
    }

    static isBlockUnderList = (block) => {
        return ["UL", "OL"].includes(block.parentElement?.tagName)
    }

    static defaultOptions = {
        preventDefaultKeydownBehavior: false,
    }

    constructor(editor, options = {}) {
        this.editor = editor;
        this.sectionContainer = editor.sectionContainer;
        this.iframeDocument = editor.iframeDocument;

        this.htmlBeforeUnhighlight;
        this.textBeforeUnhighlight;
        this.selection = this.iframeDocument.createRange();
        this.fakeCaret = Views.getViewElement("fake-caret");
        this.preventNewSelection = false;
        this.options = {...WysiwygOperations.defaultOptions, ...options};

        this.init();
    }

    init = () => {
        this.initEventListeners();
    }

    initEventListeners = () => {
        this.sectionContainer.addEventListener("mouseup", this.sectionMouseEvents);
        this.sectionContainer.addEventListener("mouseup", () => {
            if (this.preventNewSelection) this.resetSectionRange();
            this.preventNewSelection = false;
        });

        this.sectionContainer.addEventListener("keyup", this.sectionMouseEvents);
        this.sectionContainer.addEventListener('keydown', this.preventSectionDeletion);
    }

    sectionMouseEvents = () => {
        this.getElementSelection();
        Common.startActivityWatcher();
    }

    getElementSelection = async () => {
        if (this.preventNewSelection) return;

        // Wait one cycle tick for document selection to update after event
        await new Promise(resolve => setTimeout(resolve));
        const documentSelection = this.iframeDocument.getSelection();
        const isRangeInDoc = documentSelection.rangeCount > 0;

        if (isRangeInDoc) {
            this.selection = this.iframeDocument.getSelection().getRangeAt(0).cloneRange();
        }

        const endContainer = this.selection.endContainer
        const isInvalidEndContainer = endContainer.tagName === "DIV" && !RangeOperations.isRangeSingleCaret(this.selection);
        const isFirstBlockInList = ["UL", "OL"].includes(endContainer.parentElement.tagName) && !endContainer.previousElementSibling;
        if (isInvalidEndContainer) {
            const validContainer = isFirstBlockInList ? endContainer?.parentElement.previousElementSibling : endContainer?.previousElementSibling;
            const lastTextNode = RangeOperations.getLastTextNode(this.iframeDocument, validContainer);
            this.selection.setEnd(lastTextNode, lastTextNode.textContent.length);
            this.resetSectionRange();
        }

        this.editor.elementsToEdit = htmlTreeOperations.getSelectedBuilderBlock(this.selection, this.sectionContainer);
    }

    resetSectionRange = () => {
        const currentSelection = this.iframeDocument.getSelection();
        currentSelection.removeAllRanges();
        currentSelection.addRange(this.selection);
    }

    preventSectionDeletion = (event) => {
        if(this.options.preventDefaultKeydownBehavior) return;

        if ((event.code == "Backspace" || event.code == "Enter") && this.sectionContainer.innerText.split('\n').join('') == "") {
            event.preventDefault();
        }
    }

    highlightSelection = (preventNewSelection = true) => {
        if (!this.selection) return;
        this.htmlBeforeUnhighlight = this.sectionContainer.innerHTML;
        this.textBeforeUnhighlight = this.sectionContainer.textContent;
        this.preventNewSelection = preventNewSelection;
        if (RangeOperations.isRangeSingleCaret(this.selection)) return this.highlightCaret();

        this.resetSectionRange();
        this.iframeDocument.execCommand("hiliteColor", false, "#ACCEF7");
        this.selection = this.iframeDocument.getSelection().getRangeAt(0).cloneRange();
    }


    unhighlightSelection = () => {
        if (!this.selection) return;
        if (RangeOperations.isRangeSingleCaret(this.selection)) return this.unhighlightCaret();

        const offsets = RangeOperations.getRangeOffsetsBasedOnContainer(this.iframeDocument, this.selection, this.sectionContainer);
        this.removeHighlightBackgrounds();


        this.normalizeSection(offsets);
    }

    highlightCaret = () => {
        this.selection.insertNode(this.fakeCaret);
        this.fakeCaret.parentElement.insertBefore(document.createTextNode("\u200B"), this.fakeCaret);
        this.collapseCaretOnNode(this.fakeCaret.previousSibling);
        this.sectionContainer.style.caretColor = "transparent";
    }

    unhighlightCaret = () => {
        const offsets = RangeOperations.getRangeOffsetsBasedOnContainer(this.iframeDocument, this.selection, this.sectionContainer);

        this.removeHighlightBackgrounds();
        this.sectionContainer.style.caretColor = "";
        if(!this.fakeCaret) this.fakeCaret = this.sectionContainer.querySelector("#fake-caret");
        this.fakeCaret.remove();

        if(this.htmlBeforeUnhighlight !== this.sectionContainer.innerHTML) {
            this.normalizeSection(offsets);
        }
    }

    collapseCaretOnNode = (node, toStart = false) => {
        const isNodeEmpty = node.nodeName !== "#text" && !node.textContent;
        if (isNodeEmpty) {
            const lastTextContainer = RangeOperations.getLastNodeOfType(this.iframeDocument, node, "SPAN") || node;
            lastTextContainer.textContent = "\u200B";
        }

        const targetTextnode = node.nodeName === "#text" ? node
        : toStart ? RangeOperations.getFirstTextNode(this.iframeDocument, node)
        : RangeOperations.getLastTextNode(this.iframeDocument, node);

        this.selection.selectNodeContents(targetTextnode);
        this.selection.collapse(toStart);
        this.resetSectionRange();
    }

    removeEmptyBlocks = () => {
        Array.from(this.sectionContainer.querySelectorAll(".userpilot-builder-block"))
        .filter(block => block.textContent.trim().length === 0 && block !== this.sectionContainer.firstElementChild)
        .forEach(block => block.remove());

        if (!this.sectionContainer.innerHTML) {
            this.sectionContainer.appendChild(TextSection.getTextBuilderblock());
            this.collapseCaretOnNode(this.sectionContainer);
        }
    }

    removeHighlightBackgrounds = () => {
        Array.from(this.sectionContainer.querySelectorAll("span[style*='background-color']")).forEach(span => {
            span.style.backgroundColor = "";
        });

        Array.from(this.sectionContainer.querySelectorAll("a[style*='background-color']")).forEach(link => {
            link.style.backgroundColor = ""
        });

        Array.from(this.sectionContainer.querySelectorAll("u[style*='background-color'], b[style*='background-color'], i[style*='background-color']")).forEach(style => {
            style.style.backgroundColor = ""
        });
    }

    refocusText = () => {
        this.preventNewSelection = false;
        this.resetSectionRange();
        this.sectionContainer.focus();
    }

    normalizeSection = (offsets) => {
        const sectionType = WysiwigBuilder.getSectionType(this.sectionContainer);
        switch(sectionType) {
            case "userpilot-text-section":
            case "userpilot-input-text":
            case "userpilot-input-text-large":
            case "userpilot-input-radio":
            case "userpilot-input-likert-scale":
                this.editor.normalizeText(offsets);
                break;
        }
    }

    handleBackspaceKey = (event) => {
        return RangeOperations.isRangeSingleCaret(this.selection)
            ? this.handleSingleCharDeletion(event) : this.handleRangeDeletion(event);
    }

    handleSingleCharDeletion = (event) => {
        const startContainer = this.selection.startContainer;
        const startOffset = this.selection.startOffset;
        const textNodeParent = startContainer.parentElement;
        const currentBuilderBlock = this.editor.elementsToEdit[0]
        const prevBlock = currentBuilderBlock?.previousElementSibling;

        const isLastCharToDelete =  TextSection.splitter.splitGraphemes(currentBuilderBlock.textContent.replaceAll("\u200B", "")).length === 1;
        if (isLastCharToDelete && startOffset !== 0) {
            event.preventDefault();
            textNodeParent.textContent = "\u200B";
            this.collapseCaretOnNode(textNodeParent);
            return false;
        }


        const isEmptyBlock = WysiwygOperations.isEmptyTextContainer(currentBuilderBlock);
        if (isEmptyBlock) {
            event.preventDefault();
            this.editor.undoRedoManager.saveForUndo(UndoRedoManager.MUTATION_TYPES.CUSTOM);
            if (prevBlock) {
                currentBuilderBlock.remove();
                this.collapseCaretOnNode(prevBlock);
            } else if (WysiwygOperations.isBlockUnderList(currentBuilderBlock)) {
                htmlTreeOperations.unwrap(currentBuilderBlock.parentElement);
                this.collapseCaretOnNode(currentBuilderBlock);
            }

            return false;
        }

        const currBlockFirstTextnode = RangeOperations.getFirstTextNode(this.iframeDocument, currentBuilderBlock);
        const isAtStartOfCurrBlock = (startContainer === currBlockFirstTextnode || startContainer.previousSibling === prevBlock) && this.selection.startOffset === 0;
        if(prevBlock && isAtStartOfCurrBlock) {
            event.preventDefault();
            this.editor.undoRedoManager.saveForUndo(UndoRedoManager.MUTATION_TYPES.CUSTOM);

            if (WysiwygOperations.isBlockUnderList(currentBuilderBlock) && !WysiwygOperations.isBlockUnderList(prevBlock)) {
                htmlTreeOperations.unwrap(currentBuilderBlock.parentElement);
                this.collapseCaretOnNode(currBlockFirstTextnode, true);
            } else {
                prevBlock.appendChild(currentBuilderBlock);
                htmlTreeOperations.unwrap(currentBuilderBlock);
                this.sectionContainer.normalize();
                this.collapseCaretOnNode(currBlockFirstTextnode, true);
            }

            return false;
        }

        const isPrevNonEditableNode = startContainer?.previousSibling instanceof HTMLElement && startContainer?.previousSibling?.getAttribute("contenteditable") === "false";
        if (isPrevNonEditableNode) {
            const shouldDelete = !startContainer.textContent.replaceAll("\u200B", "").length;
            const shouldAddZeroChar = TextSection.splitter.splitGraphemes(startContainer.textContent.replaceAll("\u200B", "")).length === 1;
            if (shouldAddZeroChar) {
                event.preventDefault();
                startContainer.textContent = "\u200B";
                this.collapseCaretOnNode(startContainer);
                return false;
            } else if (shouldDelete) {
                event.preventDefault();
                this.editor.undoRedoManager.saveForUndo(UndoRedoManager.MUTATION_TYPES.CUSTOM);
                startContainer.previousSibling.remove();
                return false;
            }
        }

        return true;
    }

    handleRangeDeletion = (event) => {
        const startContainer = this.selection.startContainer;
        const textNodeParent = startContainer.parentElement;

        event.preventDefault();
        this.editor.undoRedoManager.saveForUndo(UndoRedoManager.MUTATION_TYPES.CUSTOM);
        this.selection.deleteContents();
        if(startContainer.textContent.length === 0) textNodeParent.textContent = "\u200B";
        this.removeEmptyBlocks();
        if(!textNodeParent.innerText.trim() || textNodeParent.innerText === "\u200B") this.collapseCaretOnNode(textNodeParent);
        return false;
    }

    /* Helpers */

    initPasteListener = () => {
        this.sectionContainer.addEventListener('paste', (e) => {
            e.preventDefault();
            const textData = (e.clipboardData || window.clipboardData).getData('text');
            const htmlData = (e.clipboardData || window.clipboardData).getData('text/html');
            let userpilotContent = '';

            // only use html content when copied from text-section
            if(htmlData) {
                const tmpNode = document.createElement("span");
                tmpNode.innerHTML = htmlData;
                const builderBlock = tmpNode.querySelector('.userpilot-copy-section');
                if(builderBlock) {
                    userpilotContent = builderBlock.innerHTML;
                }
            }

            const dataPast = userpilotContent || textData;
            const newTextNode = document.createElement("span");
            newTextNode.innerHTML = userpilotContent ? dataPast : cleanText(dataPast);

            this.editor.undoRedoManager.saveForUndo(TextUndoRedoManager.MUTATION_TYPES.CUSTOM);
            RangeOperations.replaceSelectionWithNode(this.iframeDocument, this.sectionContainer, this.selection, newTextNode);
            RangeOperations.placeCaretAfterNode(this.iframeDocument, newTextNode);
        });

        // override copy behaviour on text-section to add a flag for userpilot content
        // this.sectionContainer.addEventListener('copy', function (e) {
        //     const copySection = this.iframeWindow.getSelection().getRangeAt(0).commonAncestorContainer.innerHTML;

        //     if(copySection) {
        //         const userpilotCopySection = document.createElement("span");
        //         userpilotCopySection.classList.add('userpilot-copy-section');
        //         userpilotCopySection.innerHTML = copySection;

        //         e.clipboardData.setData('text/html', userpilotCopySection.outerHTML);
        //         e.clipboardData.setData('text', this.iframeWindow.getSelection().toString());
        //         e.preventDefault();
        //     }
        // }.bind(this));
    }

    /* END */

}
