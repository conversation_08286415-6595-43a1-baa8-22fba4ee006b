import WysiwygOperations from "./wysiwygOperations.js";

export default class TextEditorOperations extends WysiwygOperations {
    constructor(editor) {
        super(editor);
        this.initPasteListener();
    }

    removeZeroWidthSpace = () => {
        this.editor.sectionContainer.querySelectorAll(".userpilot-builder-block").forEach(block => block.innerHTML = block.innerHTML.replaceAll("\u200B", "").trim());
    }
}