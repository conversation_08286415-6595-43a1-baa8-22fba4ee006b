// Note that this is a static class, static variables are defined below the class definition
import Common from "../../index.js";
import { experienceTypes, INFO_ICON_TEXT } from "../../store/constants/experiences/constants.js";
import { spotlightTypes } from "../../store/constants/experiences/spotlightTypes.js";
import ExperienceBuilderIcons from "../../views/experienceBuilderIcons.js";
import ExperienceBuilderViews from "../../views/experienceBuilderViews.js";
import TemplateIcons from "../views/templateIcons.js";
import WysiwygIcons from "./wysiwygIcons.js";

export default class Views {
    static templateType;

    static worker;
    static sectionComputedStyle;
    static setWorker(worker) {
        this.worker = worker;
        this.sectionComputedStyle = getComputedStyle(this.worker.sectionContainer);
    }

    static getView(view, vars = {}) {
        var template = Common.shadowRoot.getElementById(view + "Temp");
        if (!template) console.error("DevError: Cannot find component " + view + " in templates");
        var element = template.content.firstElementChild.cloneNode(true);
        return element.outerHTML.formatUnicorn(Object.assign({}, this.getViewVars(view), vars));
    }

    static getViewElement(view, vars = {}) {
        let el = document.createElement("template");
        el.innerHTML = this.getView(view, vars);
        return el.content.firstChild.cloneNode(true);
    }

    static getViewVars(view) {
        switch (view) {
            case 'wysiwyg-toolbar':
                return this.getWysiwygToolbarIcons();
            case 'header-section-toolbar':
                return this.getHeaderToolbarIcons();
            case 'textEditor':
                return this.getTextEditorVars();
            case 'nodeButton':
            case 'buttonEditor':
                return this.getButtonEditorVars();
            case 'imageEditor':
                return this.getImageEditorVars();
            case 'htmlEditor':
                return this.getHtmlEditorVars();
            case 'emojiEditor':
                return this.getEmojiEditorVars();
            case 'embedEditor':
                return this.getEmbedEditorVars();
            case 'embed-size':
                return this.getEmbedDimensionsVars();
            case 'image-upload-container':
                return this.getImageUploadContainerVars();
            case 'section-background':
                return this.getBackgroundPopupVars();
            case 'image-resize':
                return this.getImageResizeVars();
            case 'image-dimensions':
                return this.getImageDimensionsVars();
            case 'text-typography':
                return this.getTextTypographyVars();
            case 'justify-content':
                return this.getJutifyContentVars();
            case 'text-list':
                return this.getListVars();
            case 'text-link':
                return {
                    urlInput: this.getView("url-input-v2", {
                        userPropertiesIcon: TemplateIcons.getUserPropertiesIcon(),
                        jsonSetting: "",
                        action: "",
                        urlTextarea: this.getView("multi-line-textarea", { required: "required", placeholder: "e.g. www.yourapp.com/dashboard" }),
                        newTabToggle: this.getView("settings-toggle", {
                            id: "text-link-new-tab",
                            title: "Open In New Tab",
                            toggleClass: "userpilot-toggle-secondary userpilot-checkbox",
                            expandingSection: "",
                            action: "",
                            jsonSetting: "",
                        }),
                    })
                };
            case 'section-padding':
                return this.getPaddingVars();
            case 'button-typography':
                return this.getButtonTypographyVars();
            case 'button-opacity':
                return this.getButtonOpacityVars();
            case 'button-config':
            case 'button-configv2':
                return this.getBtnConfigVars();
            case 'button-actions':
                return this.getButtonActionsVars();
            case 'button-padding':
                return this.getBtnPaddingVars();
            case 'button-colors':
                return this.getBorderInputVars();
            case 'image-upload':
                return this.getImageUploadVars();
            case 'image-actions':
                return this.getImageActionsVars();
            case 'image-opacity':
                return this.getImgOpacityVars();
            case 'singleInputSettings':
                return this.getInputSettingsVars();
            case "input-settings":
                return this.getInputSettingsOptionsVars();
            case 'url-input':
                return {
                    newTabToggle: this.getView("settings-toggle", {
                        id: "url-new-tab",
                        title: "Open In New Tab"
                    })
                }
            case 'select-set-user-property':
                return { deleteIcon: ExperienceBuilderIcons.getDeleteIconV2(12, 12, "#A3B0B8") }
            case 'emoji-size':
                return this.getEmojiSizeVars();
            case 'inputEditor':
            case 'largeInputEditor':
            case 'radioInputEditor':
                return this.getInputEditorVars();
            case 'ask-ai-empty-text-popup':
                return this.getAskAIEmptyTextPopupVars();
            case "ask-ai-non-empty-text-popup":
                return this.getAskAINonEmptyTextPopupVars();
            case "ask-ai-suggested-text-popup":
                return this.getAskAISuggestedTextPopupVars();
            case "ask-ai-loader-container":
                return this.getAskAILoaderContainerVars();
            case "unsplash-container":
                return this.getUnsplashContainerVars();
            default:
                return;
        }
    }

    static getBorderInputVars() {

        if (this.templateType == spotlightTypes.BUTTON) {
            return {borderInput: ""}
        }

        const borderWidthIcon = WysiwygIcons.getbuttonConfigIcon()
        const borderInput = this.getView("border-input", {
            borderWidthIcon: borderWidthIcon,
        });

        return {borderInput}
    }

    static getWysiwygToolbarIcons() {
        return {
            applyIcon: WysiwygIcons.getApplyIcon(),
            discardIcon: WysiwygIcons.getDiscardIcon(),
            removeIcon: ExperienceBuilderIcons.getDeleteIconV2(10, 12, "#A3B0B8"),
            cloneIcon: WysiwygIcons.getCloneIcon()
        }
    }

    static getHeaderToolbarIcons() {
        return {
            backgroundIcon: TemplateIcons.getImageIcon(),
            eyeIcon: ExperienceBuilderIcons.getEyeIcon(16, 16, "#A8A8AC"),
            imageResizeIcon: TemplateIcons.getImageIcon(),
            headerSizeIcon: WysiwygIcons.getHeaderSizeIcon(),
            fontColorIcon: WysiwygIcons.getFontColorIcon(),
            justifyContentIcon: WysiwygIcons.getAlignmentIcon(),
            paddingIcon: WysiwygIcons.getSectionPaddingIcon(),
            personalizationIcon: WysiwygIcons.getPersonalizationIcon(),
        };
    }

    static getTextEditorVars() {
        return {
            headerSizeIcon: WysiwygIcons.getHeaderSizeIcon(),
            typographyIcon: WysiwygIcons.getTypograpghyIcon(),
            fontColorIcon: WysiwygIcons.getFontColorIcon(),
            boldIcon: WysiwygIcons.getBoldIcon(),
            italicIcon: WysiwygIcons.getItalicIcon(),
            underlineIcon: WysiwygIcons.getUnderlineIcon(),
            justifyContentIcon: WysiwygIcons.getAlignmentIcon(),
            listIcon: WysiwygIcons.getListIcon(),
            emojiIcon: WysiwygIcons.getEmojiIcon(),
            personalizationIcon: WysiwygIcons.getPersonalizationIcon(),
            linkIcon: WysiwygIcons.getUrlLinkIcon(),
            paddingIcon: WysiwygIcons.getSectionPaddingIcon(),
            backgroundIcon: TemplateIcons.getImageIcon(),
            eyeIcon: ExperienceBuilderIcons.getEyeIcon(16, 16, "#A8A8AC"),
            askAIIcon: WysiwygIcons.getAskAIIcon(),
        };
    }

    static getButtonEditorVars() {
        return {
            btnTypographyIcon: WysiwygIcons.getTypograpghyIcon(),
            fontColorIcon: WysiwygIcons.getFontColorIcon(),
            buttonColorsIcon: WysiwygIcons.getButtonColorIcon(),
            buttonOpacityIcon: TemplateIcons.getOpacityIcon16(),
            justifyContentIcon: WysiwygIcons.getAlignmentIcon(),
            buttonConfigIcon: WysiwygIcons.getSectionPaddingIcon(),
            buttonActionsIcon: WysiwygIcons.getActionIcon(),
            paddingIcon: WysiwygIcons.getSectionPaddingIcon(),
            backgroundIcon: TemplateIcons.getImageIcon(),
            eyeIcon: ExperienceBuilderIcons.getEyeIcon(16, 16, "#A8A8AC"),
        };
    }

    static getImageEditorVars() {
        return {
            uploadIcon: TemplateIcons.getImageIcon(),
            imageResizeIcon: WysiwygIcons.getDimensionIcon(),
            justifyContentIcon: WysiwygIcons.getAlignmentIcon(),
            opacityIcon: TemplateIcons.getOpacityIcon16(),
            accessibilityIcon: WysiwygIcons.getAccessibilityIcon(),
            actionsIcon: WysiwygIcons.getActionIcon(),
            paddingIcon: WysiwygIcons.getSectionPaddingIcon(),
            backgroundIcon: TemplateIcons.getImageIcon(),
            eyeIcon: ExperienceBuilderIcons.getEyeIcon(16, 16, "#A8A8AC"),
        };
    }

    static getHtmlEditorVars() {
        return {
            paddingIcon: WysiwygIcons.getSectionPaddingIcon(),
            backgroundIcon: TemplateIcons.getImageIcon(),
            eyeIcon: ExperienceBuilderIcons.getEyeIcon(16, 16, "#A8A8AC"),
        }
    }

    static getEmojiEditorVars() {
        return {
            emojiIcon: WysiwygIcons.getEmojiIcon(),
            expandIcon: ExperienceBuilderIcons.getHeightIcon16(),
            justifyContentIcon: WysiwygIcons.getAlignmentIcon(),
            paddingIcon: WysiwygIcons.getSectionPaddingIcon(),
            backgroundIcon: TemplateIcons.getImageIcon(),
            eyeIcon: ExperienceBuilderIcons.getEyeIcon(16, 16, "#A8A8AC"),
        };
    }

    static getEmbedEditorVars() {
        return {
            embedLinkIcon: WysiwygIcons.getUrlLinkIcon(),
            embedResizeIcon: ExperienceBuilderIcons.getHeightIcon16(),
            paddingIcon: WysiwygIcons.getSectionPaddingIcon(),
            backgroundIcon: TemplateIcons.getImageIcon(),
            eyeIcon: ExperienceBuilderIcons.getEyeIcon(16, 16, "#A8A8AC"),
        }
    }

    static getEmbedDimensionsVars() {
        const embedWidthSlider = this.getView("settings-slider", {
            id: "embed-width-slider",
            elements: "",
            jsonSetting: "",
            property: "",
            title: "Width",
            max: "",
            min: "1",
            sliderIcon: ExperienceBuilderIcons.getWidthIcon(),
            inputUnit: ""
        });

        const embedHeightSlider = this.getView("settings-slider", {
            id: "embed-height-slider",
            elements: "",
            jsonSetting: "",
            property: "",
            title: "Height",
            max: "1000",
            min: "1",
            sliderIcon: ExperienceBuilderIcons.getHeightIcon(),
            inputUnit: ""
        });

        return {
            widthSlider: embedWidthSlider,
            heightSlider: embedHeightSlider,
        }
    }

    static getImageResizeVars() {
        const zoomSlider = this.getView("settings-slider", {
            id: "zoom-slider",
            elements: "",
            jsonSetting: "",
            property: "",
            title: "Zoom",
            max: "200",
            min: "100",
            sliderIcon: "",
            inputUnit: "%"
        });

        const heightSlider = this.getView("settings-slider", {
            id: "height-slider",
            elements: "",
            jsonSetting: "",
            property: "",
            title: "Height",
            max: "500",
            min: "50",
            sliderIcon: ExperienceBuilderIcons.getHeightIcon(),
            inputUnit: "px"
        });

        return {
            zoomSlider: zoomSlider,
            heightSlider: heightSlider
        }
    }

    static getImageDimensionsVars() {
        const imageWidthSlider = this.getView("settings-slider", {
            id: "image-width-slider",
            elements: "",
            jsonSetting: "",
            property: "",
            title: "Width",
            max: "",
            min: "1",
            sliderIcon: ExperienceBuilderIcons.getWidthIcon(),
            inputUnit: "px"
        });

        return {
            widthSlider: imageWidthSlider,
        }
    }

    static getEmojiSizeVars() {
        const emojiSizeSlider = this.getView("settings-slider", {
            id: "emoji-size-slider",
            elements: "",
            jsonSetting: "",
            property: "",
            title: "Size",
            max: "100",
            min: "10",
            sliderIcon: "",
            inputUnit: "px"
        });

        return { emojiSizeSlider }
    }

    static getTextTypographyVars() {
        const lineHeightSlider = this.getView("settings-slider", {
            id: "line-height-slider",
            elements: "",
            jsonSetting: "",
            property: "",
            title: "Line Height",
            max: "50",
            min: "1",
            sliderIcon: WysiwygIcons.getLineHeightIcon(),
            inputUnit: ""
        });

        const letterSpaceSlider = this.getView("settings-slider", {
            id: "letter-space-slider",
            elements: "",
            jsonSetting: "",
            property: "",
            title: "Letter Space",
            max: "10",
            min: "0",
            sliderIcon: WysiwygIcons.getLetterSpaceIcon(),
            inputUnit: ""
        });

        return {
            lineHeightSlider: lineHeightSlider,
            letterSpaceSlider: letterSpaceSlider
        }
    }

    static getButtonTypographyVars() {
        const fontweightSlider = this.getView("settings-slider", {
            id: "font-weight-slider",
            elements: "",
            jsonSetting: "",
            property: "",
            title: "Font Weight",
            max: "900",
            min: "100",
            step: "100",
            sliderIcon: WysiwygIcons.getLineHeightIcon(),
            inputUnit: ""
        });
        return {
            fontweightSlider: fontweightSlider
        };
    }

    static getButtonOpacityVars() {
        const opacitySlider = this.getView("settings-slider", {
            id: "button-opacity-slider",
            elements: "",
            jsonSetting: "",
            property: "",
            title: "Button Opacity",
            max: "100",
            min: "1",
            sliderIcon: TemplateIcons.getOpacityIcon(),
            inputUnit: ""
        });
        return {
            opacitySlider: opacitySlider
        };
    }

    static getJutifyContentVars() {
        return {
            leftAlignIcon: WysiwygIcons.getPositionAlignmentIcon("left"),
            centerAlignIcon: WysiwygIcons.getPositionAlignmentIcon("center"),
            rightAlignIcon: WysiwygIcons.getPositionAlignmentIcon("right"),
            justifyAlignIcon: WysiwygIcons.getPositionAlignmentIcon("justify")
        };
    }

    static getListVars() {
        return {
            dottedListIcon: WysiwygIcons.getListIconV2(),
            numberedListIcon: WysiwygIcons.getNumberedListIcon()
        };
    }

    static getPaddingVars() {
        const paddingInputs = this.getView("direction-inputs", {
            id: "section-padding",
            min: "0",
            max: "100",
            activeLinkIcon: WysiwygIcons.getUnifyInputIcon(true),
            inactiveLinkIcon: WysiwygIcons.getUnifyInputIcon(false),
            unifyState: "false"
        });

        return {
            paddingInputs: paddingInputs
        }
    }

    static getBtnConfigVars() {
        const paddingInput = this.getView("direction-inputs", {
            id: "button-padding",
            min: "0",
            max: "100",
            activeLinkIcon: WysiwygIcons.getUnifyInputIcon(true),
            inactiveLinkIcon: WysiwygIcons.getUnifyInputIcon(false),
            unifyState: "false"
        });

        const borderWidthSlider = this.getView("settings-slider", {
            id: "border-width-slider",
            elements: "",
            jsonSetting: "",
            property: "",
            title: "Border Width",
            max: "25",
            min: "0",
            sliderIcon: ExperienceBuilderIcons.getWidthIcon(),
            inputUnit: ""
        });

        const shadowWidthSlider = this.getView("settings-slider", {
            id: "shadow-width-slider",
            elements: "",
            jsonSetting: "",
            property: "",
            title: "Shadow",
            max: "10",
            min: "0",
            sliderIcon: TemplateIcons.getShadowIntensityIcon(),
            inputUnit: ""
        });

        return {
            paddingInput: paddingInput,
            borderWidth: borderWidthSlider,
            buttonShadow: shadowWidthSlider
        }
    }

    static getButtonActionsVars() {
        const triggerFlowOptions = this.getView("trigger-flow", {
            urlInput: this.getView("url-input-v2", {
                userPropertiesIcon: TemplateIcons.getUserPropertiesIcon(),
                urlTextarea: this.getView("multi-line-textarea", { required: "required", placeholder: "e.g. www.yourapp.com/dashboard" }),
                newTabToggle: this.getView("settings-toggle", {
                    id: "flow-new-tab",
                    title: "Open In New Tab",
                    toggleClass: "userpilot-toggle-secondary userpilot-checkbox",
                    expandingSection: "",
                    action: "",
                    jsonSetting: "",
                }),
            })
        });

        const triggerSurveyOptions = this.getView("trigger-survey", {
            urlInput: this.getView("url-input-v2", {
                userPropertiesIcon: TemplateIcons.getUserPropertiesIcon(),
                urlTextarea: this.getView("multi-line-textarea", { required: "required", placeholder: "e.g. www.yourapp.com/dashboard" }),
                newTabToggle: this.getView("settings-toggle", {
                    id: "survey-new-tab",
                    title: "Open In New Tab",
                    toggleClass: "userpilot-toggle-secondary userpilot-checkbox",
                    expandingSection: "",
                    action: "",
                    jsonSetting: "",
                }),
            })
        });

        const updateUserPropertiesToggle = this.getView("settings-toggle", {
            id: "update-property",
            title: "Update Properties"
        });

        const trackEventToggle = this.getView("settings-toggle", {
            id: "track-event",
            title: "Track event"
        });

        const markCompleteToggle = this.getView("settings-toggle", {
            id: "mark-complete",
            title: ([spotlightTypes.BANNER, experienceTypes.BANNER_v2].includes(this.templateType)) ? "Dismiss Banner" : "Dismiss flow and mark it as complete",
            toggleClass: "userpilot-checkbox",
        });

        const dismissFlowAndCompleteToggle = this.getView("settings-toggle", {
            id: "dimiss-complete-flow",
            title: ([spotlightTypes.BANNER, experienceTypes.BANNER_v2].includes(this.templateType)) ? "Dismiss Banner" : "Dismiss flow and mark it as complete",
            toggleClass: "userpilot-checkbox",
        });

        const userPropertiesList = this.getView("user-properties", {
            updateUserPropertiesToggle: updateUserPropertiesToggle,
        });

        const urlInput = this.getView("url-input-v2", {
            userPropertiesIcon: TemplateIcons.getUserPropertiesIcon(),
            jsonSetting: "",
            action: "",
            urlTextarea: this.getView("multi-line-textarea", { required: "required", placeholder: "e.g. www.yourapp.com/dashboard" }),
            newTabToggle: this.getView("settings-toggle", {
                id: "url-new-tab",
                title: "Open In New Tab",
                toggleClass: "userpilot-toggle-secondary userpilot-checkbox",
                expandingSection: "",
                action: "",
                jsonSetting: "",
            }),
        })

        const actionsSection = this.getView("wysiwyg-actions-section", {
            urlInput: urlInput,
            markCompleteToggle: (this.templateType === spotlightTypes.NATIVE) ? "" : markCompleteToggle,
            triggerFlowOptions: triggerFlowOptions,
            triggerSurveyOptions: triggerSurveyOptions,
            dismissFlowAndCompleteToggle: (this.templateType === spotlightTypes.NATIVE) ? "" : dismissFlowAndCompleteToggle,
            selectMenuLabel: "On Button Click",
        });

        return {
            actionsSection: actionsSection,
            trackEventToggle: trackEventToggle,
            userPropertiesList: userPropertiesList,
        }
    }

    static getImageActionsVars() {
        const urlInput = this.getView("url-input-v2", {
            userPropertiesIcon: TemplateIcons.getUserPropertiesIcon(),
            jsonSetting: "",
            action: "",
            urlTextarea: this.getView("multi-line-textarea", { required: "required", placeholder: "e.g. www.yourapp.com/dashboard" }),
            newTabToggle: this.getView("settings-toggle", {
                id: "go-to-url-new-tab",
                title: "Open In New Tab",
                toggleClass: "userpilot-toggle-secondary userpilot-checkbox",
                expandingSection: "",
                action: "",
                jsonSetting: "",
            }),
        });

        const triggerFlowOptions = this.getView("trigger-flow", {
            urlInput: this.getView("url-input-v2", {
                userPropertiesIcon: TemplateIcons.getUserPropertiesIcon(),
                jsonSetting: "",
                action: "",
                urlTextarea: this.getView("multi-line-textarea", { required: "required", placeholder: "e.g. www.yourapp.com/dashboard" }),
                newTabToggle: this.getView("settings-toggle", {
                    id: "flow-link-new-tab",
                    title: "Open In New Tab",
                    toggleClass: "userpilot-toggle-secondary userpilot-checkbox",
                    expandingSection: "",
                    action: "",
                    jsonSetting: "",
                }),
            })
        });

        const triggerSurveyOptions = this.getView("trigger-survey", {
            urlInput: this.getView("url-input-v2", {
                userPropertiesIcon: TemplateIcons.getUserPropertiesIcon(),
                jsonSetting: "",
                action: "",
                urlTextarea: this.getView("multi-line-textarea", { required: "required", placeholder: "e.g. www.yourapp.com/dashboard" }),
                newTabToggle: this.getView("settings-toggle", {
                    id: "survey-link-new-tab",
                    title: "Open In New Tab",
                    toggleClass: "userpilot-toggle-secondary userpilot-checkbox",
                    expandingSection: "",
                    action: "",
                    jsonSetting: "",
                }),
            })
        });

        return {
            actionsSection: this.getView("wysiwyg-actions-section", {
                urlInput: urlInput,
                triggerFlowOptions: triggerFlowOptions,
                triggerSurveyOptions: triggerSurveyOptions,
                markCompleteToggle: "",
                dismissFlowAndCompleteToggle: "",
                selectMenuLabel: "On Image Click",
            }),
        }
    }

    static getBtnPaddingVars() {
        var style = getComputedStyle(this.worker.button);
        var top = parseInt(style.paddingTop);
        var bottom = parseInt(style.paddingBottom);
        var right = parseInt(style.paddingRight);
        var left = parseInt(style.paddingLeft);

        return {
            top: top,
            left: left,
            right: right,
            bottom: bottom
        };
    }

    static getImgOpacityVars() {
        const opacitySlider = this.getView("settings-slider", {
            id: "image-opacity-slider",
            elements: "",
            jsonSetting: "",
            property: "",
            title: "Image Opacity",
            max: "100",
            min: "1",
            sliderIcon: TemplateIcons.getOpacityIcon16(),
            inputUnit: ""
        });

        return {
            opacitySlider: opacitySlider
        }
    }

    static getInputSettingsVars() {
        var maxLength = this.worker.input.getAttribute("maxlength");
        if (!maxLength) {
            maxLength = "";
        }
        return {
            maxLength: maxLength
        };
    }

    static getInputSettingsOptionsVars() {
        const infoIcon = this.getView("info-icon", {
            infoIcon: ExperienceBuilderIcons.getInfoIconV2(),
            text: INFO_ICON_TEXT.WYSIWYG_FORM_SECTIONS_CREATE_PROPERTY,
        });

        return {
            infoIcon: infoIcon,
        }
    }

    static getBackgroundPopupVars() {
        const solidColorsHTML = Common.userData.app_data.colors.solid.reduce(
            (accumalator, color) => accumalator + this.getView("background-color", {
                color: color
            }), "");

        const gradientColorsHTML = Common.userData.app_data.colors.gradient.reduce(
            (accumalator, color) => accumalator + this.getView("background-color", {
                color: color
            }), "");

        const unsplashContainer = this.getView("unsplash-container", {
            containerClass: "",
        });

        return {
            colors: solidColorsHTML,
            gradientColors: gradientColorsHTML,
            addIcon: WysiwygIcons.getAddIcon(),
            imageUploadContainer: this.getView("image-upload-container"),
            searchIcon: ExperienceBuilderIcons.getSearchIcon(),
            unsplashLogo: WysiwygIcons.getUnsplashLogo(),
            unsplashContainer: unsplashContainer,
        }
    }

    static getUnsplashContainerVars() {
        return {
            unsplashLogo: WysiwygIcons.getUnsplashLogo(),
            searchIcon: ExperienceBuilderIcons.getSearchIcon(),
        }
    }

    static getImageUploadVars() {
        const imageUploadContainer = this.getView("image-upload-container", {
            unsplashLogo: WysiwygIcons.getUnsplashLogo(),
        });

        return {
            imageUploadContainer: imageUploadContainer,
        }
    }

    static getImageUploadContainerVars() {

        const unsplashContainer = this.getView("unsplash-container", {
            containerClass: "",
            backArrowIcon: ExperienceBuilderIcons.getBackIconV2(),
        });

        return {
            genericImageIcon: TemplateIcons.getImageIcon(),
            deleteImageIcon: ExperienceBuilderIcons.getDeleteIconV2(),
            unsplashContainer: unsplashContainer,
        };
    }

    static getInputEditorVars() {
        return {
            fontColorIcon: WysiwygIcons.getFontColorIcon(),
            paddingIcon: WysiwygIcons.getSectionPaddingIcon(),
            backgroundIcon: TemplateIcons.getImageIcon(),
            eyeIcon: ExperienceBuilderIcons.getEyeIcon(16, 16, "#A8A8AC"),
            settingsIcon: ExperienceBuilderIcons.getFilterIcon(),
        }
    }

    static getAskAIEmptyTextPopupVars() {
        return {
            acceptIcon: WysiwygIcons.getCheckIcon(),
            continueWritingIcon: WysiwygIcons.getContinueWritingIcon(),
            makeLongerIcon: WysiwygIcons.getMakeLongerIcon(),
            makeShorterIcon: WysiwygIcons.getMakeShorterIcon(),
            tryAgainIcon: WysiwygIcons.getTryAgainIcon(),
            discardIcon: ExperienceBuilderIcons.getDeleteIconV2(10, 12, "#6765E8"),
        }
    }

    static getAskAINonEmptyTextPopupVars() {
        return {
            acceptIcon: WysiwygIcons.getCheckIcon(),
            continueWritingIcon: WysiwygIcons.getContinueWritingIcon(),
            fixSpellingAndGrammarIcon: WysiwygIcons.getCheckIcon(),
            summarizeIcon: WysiwygIcons.getSummarizeIcon(),
            improveWritingIcon: WysiwygIcons.getImproveWritingIcon(),
            makeLongerIcon: WysiwygIcons.getMakeLongerIcon(),
            makeShorterIcon: WysiwygIcons.getMakeShorterIcon(),
        }
    }

    static getAskAISuggestedTextPopupVars() {
        return {
            acceptIcon: WysiwygIcons.getCheckIcon(),
            makeLongerIcon: WysiwygIcons.getMakeLongerIcon(),
            makeShorterIcon: WysiwygIcons.getMakeShorterIcon(),
            tryAgainIcon: WysiwygIcons.getTryAgainIcon(),
            discardIcon: ExperienceBuilderIcons.getDeleteIconV2(10, 12, "#6765E8"),
        }
    }

    static getAskAILoaderContainerVars() {
        return {
            loader: ExperienceBuilderViews.getView("builder-loading-animation"),
        }
    }
}

// Static variables
String.prototype.formatUnicorn = String.prototype.formatUnicorn ||
    function () {
        "use strict";
        var str = this.toString();
        if (arguments.length) {
            var t = typeof arguments[0];
            var key;
            var args = ("string" === t || "number" === t) ?
                Array.prototype.slice.call(arguments) :
                arguments[0];

            for (key in args) {
                str = str.replace(new RegExp("\\{" + key + "\\}", "gi"), args[key]);
            }
        }
        return str;
    };