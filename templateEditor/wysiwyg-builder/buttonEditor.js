import Common from '../../index.js';
import BuilderSection from './commonEditor.js';

// Components
import InputSlider from "../../generic-components/inputSlider.js";
import SolidColorPicker from "../../generic-components/colorPickers/solidColorPicker.js";
import DirectionInput from "../../generic-components/directionInput.js";
import AdditionSelectMenu from '../../generic-components/additionSelectMenu.js';
import UserCompnayProperties from '../../generic-components/userProperties/userCompnayProperties.js';
import RangeOperations from './rangeOperations.js';
import WysiwygOperations from './utils/wysiwygOperations.js';

// Utils
import ButtonUndoRedoManager from "./UndoRedoManager/button.js";
import { cleanText } from '../../generic-utils/textTransformation.js';

// Store
import { Store } from '../../store/store.js';
import App from '../../components/app.js';
import Action, { ACTION_TYPES } from './components/Action/action.js';
import { experienceTypes } from '../../store/constants/experiences/constants.js';
import { PROPERTIES_TYPES } from '../../generic-components/userProperties/constants.js';
import { Alert } from '../../generic-components/alert.js';
import MessageHandler from '../../routing/messageHandler.js';
import { patternTypes } from '../../store/constants/experiences/uiPatternTypes.js';

const flowActionTypes = [
    ACTION_TYPES.NEXT_FLOW,
    ACTION_TYPES.GO_TO_URL,
    ACTION_TYPES.PREV_FLOW,
    ACTION_TYPES.SKIP_TO_STEP,
    ACTION_TYPES.FLOW,
    ACTION_TYPES.SURVEY,
    ACTION_TYPES.JSCALLBACK,
    ACTION_TYPES.DIS_GRP,
    ACTION_TYPES.CLOSE_TUT_FLOW,
]

const spotlightActionTypes = [
    ACTION_TYPES.GO_TO_URL,
    ACTION_TYPES.FLOW,
    ACTION_TYPES.SURVEY,
    ACTION_TYPES.JSCALLBACK,
    ACTION_TYPES.CLOSE_TUT_SPOTLIGHT,
]

export default class ButtonSection extends BuilderSection {

    constructor(el, popupToolbar, templateEditor) {
        super(el, popupToolbar);
        this.initPasteListener();
        this.actionSelected = '';
        this.button;
        this.wysiwygOperations = new WysiwygOperations(this);
        this.templateEditor = templateEditor;
    }

    /* Inititalizers */

    init() {
        this.button = this.sectionContainer.querySelector('.userpilot-btn');
        this.buttonStyles = getComputedStyle(this.button);
        this.button.contentEditable = true;

        super.init();
        this.undoRedoManager = new ButtonUndoRedoManager(this);

        const targetSelectionElement = this.getSectionSelectionTarget();
        this.wysiwygOperations.selection.selectNodeContents(targetSelectionElement);
        this.wysiwygOperations.selection.collapse(false);
        this.wysiwygOperations.resetSectionRange();
        this.initFontColorEvents();
    }

    save(_event) {
        super.save();
        this.button.contentEditable = false;
    }

    getSectionSelectionTarget() {
        const section = this.button;
        if (section.firstChild) return section.firstChild;
        else return section;
    }

    initPopupEvents(popupName, popupContainer, popupObj) {
        switch (popupName) {
            case "button-typography":
                this.initButtonTypographyEvents(popupContainer, popupObj);
                break;
            case "font-size":
                this.initButtonFontSizeEvents(popupContainer, popupObj);
                break;
            case "button-colors":
                this.initButtonColorsEvents(popupContainer, popupObj);
                break;
            case "button-opacity":
                this.initButtonOpacityEvents(popupContainer, popupObj);
                break;
            case "justify-content":
                this.initJustifyButtonEvents(popupContainer, popupObj);
                break;
            case "button-config":
                this.initButtonConfig(popupContainer, popupObj);
                break;
            case "button-actions":
                this.initButtonActionEvents(popupContainer, popupObj);
                break;
            case "section-padding":
                this.initSectionPaddingEvents(popupContainer);
                break;
            case "section-background":
                this.initSectionBgEvents(popupContainer, popupObj);
                break;
            case "section-visibility":
                this.initVisibilityEvents(popupContainer, popupObj);
                break;
            default:
                break;
        }
    }

    /* END */

    changeButtonStyle(property, value) {
        this.undoRedoManager.saveForUndo(ButtonUndoRedoManager.MUTATION_TYPES.BUTTON_STYLE);
        this.button.style[property] = value;
    }

    changeButtonHoverStyle(property, value) {
        this.undoRedoManager.saveForUndo(ButtonUndoRedoManager.MUTATION_TYPES.BUTTON_STYLE);
        this.button.setAttribute(property, value);
    }

    /* Button Styles */

    initButtonTypographyEvents(popupContainer, popupObj) {
        const fontWeightSliderView = popupContainer.querySelector("#font-weight-slider");
        const fontWeightSlider = new InputSlider(fontWeightSliderView, (value) => this.changeButtonStyle("fontWeight", value), parseInt(this.buttonStyles.fontWeight));

        popupObj.callbackOnPopupSpawn = () => {
            const fontWeight = parseInt( getComputedStyle(this.button).fontWeight );
            fontWeightSlider.changeSliderValue(parseInt(fontWeight));
        }
    }

    initButtonFontSizeEvents(popupContainer, popupObj) {
        const toolbarFontSizeContainer = this.popupToolbar.querySelector("#font-size");
        const input = toolbarFontSizeContainer.querySelector("#font-size input");
        const fontOptions = Array.from(popupContainer.querySelectorAll(".popup-container > div"));

        const initialSize = parseFloat(this.buttonStyles.fontSize);
        input.value = initialSize;

        let inputTimeout;
        input.addEventListener("input", () => {
            if (!input.value) return;
            const value = parseFloat(input.value);
            clearTimeout(inputTimeout);
            inputTimeout = setTimeout(() => {
                this.changeButtonStyle("fontSize", parseFloat(value) + "px");
            }, 500);
        });

        input.addEventListener("keydown", (event) => {
            if (!input.value) return;
            if (!["ArrowUp", "ArrowDown", "Enter", "Escape"].includes(event.code)) return;
            event.preventDefault();

            let value = input.value;
            switch (event.code) {
                case "ArrowUp":
                    input.value = ++value;
                    this.changeButtonStyle("fontSize", parseFloat(value) + "px");
                    break;

                case "ArrowDown":
                    input.value = --value;
                    this.changeButtonStyle("fontSize", parseFloat(value) + "px");
                    break;

                case "Enter":
                    popupObj.closePopup();
                    break;

                case "Escape":
                    input.value = initialSize;
                    this.changeButtonStyle("fontSize", parseFloat(initialSize) + "px");
                    popupObj.closePopup();
                    break;
            }
        });

        fontOptions.forEach(option => option.addEventListener("click", () => {
            this.changeButtonStyle("fontSize", parseFloat(option.textContent) + "px");
            input.value = option.textContent;
        }));

        popupObj.callbackOnPopupSpawn = () => {
            toolbarFontSizeContainer.classList.add("active");
            input.setSelectionRange(input.value.length, input.value.length);
            input.focus();
        }

        popupObj.callbackOnPopupClose = () => {
            toolbarFontSizeContainer.classList.remove("active");
            this.button.focus();
        }
    }

    /* END */

    /* Button Styles */

    initFontColorEvents() {
        const fontColorBtn = this.popupToolbar.querySelector("#font-color");
        const fontColorIcon = fontColorBtn.querySelector("svg");
        fontColorIcon.style.fill = this.getButtonFontColor();

        new SolidColorPicker(fontColorBtn, this.getButtonFontColor.bind(this), {
            onChangeCallback: (color) => {
                this.changeButtonStyle("color", color);
                fontColorIcon.style.fill = color;
            }
        });
    }

    getButtonFontColor() {
        return this.button.style.color || this.buttonStyles.color;
    }

    initButtonColorsEvents(popupContainer, popupObj) {
        const buttonBgColorPicker = popupContainer.querySelector("#button-bg-color");
        new SolidColorPicker(buttonBgColorPicker, this.getButtonBackgroundColor.bind(this), {
            onChangeCallback: (color) => {
                this.changeButtonStyle("background", color);
                buttonBgColorPicker.style.background = color;
            }
        });

        const buttonBorderColorPicker = popupContainer.querySelector("#button-border-color");
        new SolidColorPicker(buttonBorderColorPicker, this.getButtonBorderColor.bind(this), {
            onChangeCallback: (color) => {
                this.changeButtonStyle("borderColor", color);
                buttonBorderColorPicker.style.background = color;
            }
        });

        const borderWidthEl = popupContainer.querySelector(".button-border-width");
        borderWidthEl.addEventListener("click", () => {
            popupObj.closePopup();
            this.iframeDocument.querySelector("#button-config").click();
        })

        const buttonBgHoverColorPicker = popupContainer.querySelector("#button-bg-hover-color");
        new SolidColorPicker(buttonBgHoverColorPicker, this.getButtonBackgroundHoverColor, {
            onChangeCallback: (color) => {
                this.changeButtonHoverStyle("bg-hover-color", color);
                buttonBgHoverColorPicker.style.background = color;
            }
        });
        this.initButtonBackgroundHoverColor();
        
        popupObj.callbackOnPopupSpawn = () => {
            buttonBgColorPicker.style.background = this.getButtonBackgroundColor();
            buttonBorderColorPicker.style.background = this.getButtonBorderColor();
            buttonBgHoverColorPicker.style.background = this.getButtonBackgroundHoverColor();
            popupContainer.querySelector(".border-width-value").innerHTML = this.buttonStyles.borderWidth.replace("px", "");
        }
    }

    getButtonBackgroundColor() {
        return this.button.style.background || this.buttonStyles.backgroundColor;
    }

    getButtonBorderColor() {
        return this.button.style.borderColor || this.buttonStyles.borderColor;
    }

    getButtonBackgroundHoverColor = () => {
        return  this.button.getAttribute("bg-hover-color") || this.getThemeButtonBackgroundHoverColor();
    }

    getThemeButtonBackgroundHoverColor = () => {
        const stepType = this.templateEditor.templateSettings.type;
        const buttonUppa = this.button.getAttribute("uppa");
        let themeValuePath = ([experienceTypes.BANNER_v2].includes(stepType)) ? `${stepType}-primary-color_on_hover` : `general-control-primary-color_on_hover`;
        const buttonsThemePath = ([patternTypes.MODAL, patternTypes.SLIDEOUT].includes(stepType)) ? stepType + "-buttons" : stepType;

        switch(buttonUppa) {
            case "next":
                themeValuePath = (`${buttonsThemePath}-next-color_on_hover`);
                break;
            case "back":
                themeValuePath = (`${buttonsThemePath}-back-color_on_hover`);
                break;
        }

        return this.templateEditor.templateSettings.themeManager.getCurrentSettingValue(themeValuePath.split("-"));
    }

    initButtonBackgroundHoverColor = () => {
        this.button.addEventListener('mouseenter', () => {
            const backgroundHoverColor = this.button.getAttribute("bg-hover-color");
            if(!backgroundHoverColor) return;

            this.button.classList.add("hovered-on");
            this.button.style.setProperty("--button-bg-hover-color", backgroundHoverColor);
        })

        this.button.addEventListener('mouseleave', () => {
            this.button.classList.remove("hovered-on");
            this.button.style.setProperty("--button-bg-hover-color", "");
        })
    }

    initButtonOpacityEvents(popupContainer, popupObj) {
        const opacitySliderEl = popupContainer.querySelector("#button-opacity-slider");
        const opacitySlider = new InputSlider(opacitySliderEl, (value) => this.changeButtonStyle("opacity", value / 100));

        popupObj.callbackOnPopupSpawn = () => {
            const opacity = parseFloat(this.button.style.opacity || this.buttonStyles.opacity) * 100
            opacitySlider.changeSliderValue(opacity);
        }
    }

    initJustifyButtonEvents(popupContainer, popupObj) {
        const justifyOptions = Array.from(popupContainer.querySelectorAll(".align-btn"));
        justifyOptions.forEach(btn => {
            btn.addEventListener("click", () => {
                this.undoRedoManager.saveForUndo(ButtonUndoRedoManager.MUTATION_TYPES.SECTION_STYLE);
                this.sectionContainer.style.textAlign = btn.getAttribute("value");
                this.setActiveAlignmentOption(justifyOptions);
            });
        });

        popupObj.callbackOnPopupSpawn = () => this.setActiveAlignmentOption(justifyOptions);
    }

    setActiveAlignmentOption(justifyOptions) {
        justifyOptions.forEach(btn => btn.classList.remove("selected"));

        justifyOptions.find(btn => btn.getAttribute("value") === getComputedStyle(this.sectionContainer).textAlign)
        .classList.add("selected");
    }

    initButtonConfig(popupContainer, popupObj) {
        const paddingInputComponent = popupContainer.querySelector("#button-padding");
        const directionInputs = new DirectionInput(paddingInputComponent, this.changeButtonPadding.bind(this), this.getButtonPaddingValues());

        const borderWidthSlider = popupContainer.querySelector("#border-width-slider");
        const borderWidthInitialValue = parseFloat(getComputedStyle(this.button).borderWidth.replace('px', '')) || 0;
        const borderSlider = new InputSlider(borderWidthSlider, this.changeBorderWidth.bind(this), borderWidthInitialValue);

        const shadowSliderEl = popupContainer.querySelector("#shadow-width-slider");
        const shadowSlider = new InputSlider(shadowSliderEl, this.changeShadow.bind(this), this.getShadowWidth());

        popupObj.callbackOnPopupSpawn = () => {
            directionInputs.setValues( this.getButtonPaddingValues() );
            borderSlider.changeSliderValue( parseFloat(getComputedStyle(this.button).borderWidth.replace('px', '')) || 0 );
            shadowSlider.changeSliderValue( this.getShadowWidth() );
        }
    }

    getButtonPaddingValues() {
        return {
            top: parseFloat(this.buttonStyles.paddingTop),
            right: parseFloat(this.buttonStyles.paddingRight),
            bottom: parseFloat(this.buttonStyles.paddingBottom),
            left: parseFloat(this.buttonStyles.paddingLeft),
        }
    }

    changeButtonPadding(value, direction) {
        const normalizedDirection = direction[0].toUpperCase() + direction.substr(1).toLowerCase();
        this.changeButtonStyle("padding" + normalizedDirection, value + "px");
    }

    changeBorderWidth(value) {
        if(value == 0) this.changeButtonStyle("borderWidth", "");
        else this.changeButtonStyle("borderWidth", value + "px");
    }

    getShadowWidth() {
        const shadowWidth = this.button.style.boxShadow?.split(" ")?.pop();
        return (shadowWidth) ? parseFloat(shadowWidth) : 0;
    }

    changeShadow(value) {
        this.changeButtonStyle("boxShadow", "0px 0px " + parseFloat(value) + "px black");
    }

    /* END */

    /* Button Actions */

    initButtonActionEvents(popupContainer, popupObj) {
        const isFlow = (Store.getComponentState(App).experience.settings.type === experienceTypes.FLOW);
        const actionSelectBtn = popupContainer.querySelector("#button-action-select");
        const selectMenuOptions = isFlow ? flowActionTypes : spotlightActionTypes;
        const getActionFallbackValue = () => isFlow ? ACTION_TYPES.NEXT_FLOW.value : ACTION_TYPES.CLOSE_TUT_SPOTLIGHT.value;
        const action = new Action(this.button, this.sectionContainer, popupContainer, actionSelectBtn, selectMenuOptions, { getFallbackValue: getActionFallbackValue });

        popupObj.callbackOnPopupSpawn = () => action.setBtnActionPopupState(action.selectMenu);
        popupObj.callbackOnPopupClose = this.assignUpdateUserCompanyProperties.bind(this, popupContainer.querySelector("#user-properties"));

        // init and set updateUserProperties and trackEvent components
        this.initUpdateUserProps(popupContainer.querySelector("#user-properties"), popupContainer);
        this.initTrackEvents(popupContainer);
    }

    /* USER PROPERTIES */

    initUpdateUserProps(userPropsContainer, popupContainer) {
        const userPropsOptionsContainer = userPropsContainer.querySelector("#user-company-properties-options");

        const onToggleUnchecked = () => {
            Array.from(userPropsOptionsContainer.querySelectorAll(".select-set-user-property"))
                .forEach(entry => entry.remove());
        }

        const elements = this.getPropertiesElements();
        new UserCompnayProperties(userPropsContainer, popupContainer, {
            elements: elements,
            selectMenuOptions: {
                container: popupContainer
            },
            onToggleUncheckedCallback: onToggleUnchecked
        });
    }

    assignUpdateUserCompanyProperties(userPropsContainer) {
        this.updatePropertiesV1(userPropsContainer);
        this.updatePropertiesV2(userPropsContainer);
    }

    updatePropertiesV1(userPropsContainer) {
        const propertiesString = Array.from(userPropsContainer.querySelectorAll(`.select-set-user-property[type="user"]`))
            .map(el => {
                return {
                    property: el.querySelector(".userpilot-select").getAttribute("value")?.trim(),
                    value: el.querySelector("input[type='text']").value
                }
            }).filter(entry => entry.property && entry.value)
            .reduce((acc, curr) => acc + curr.property + ":" + curr.value + ";", "");

        (propertiesString.trim()) ? this.button.setAttribute("userpilot-update-props", propertiesString) : this.button.removeAttribute("userpilot-update-props");
    }

    updatePropertiesV2(userPropsContainer) {
        const propertiesList = Array.from(userPropsContainer.querySelectorAll(".select-set-user-property"))
            .map(el => {
                return {
                    property: el.querySelector(".userpilot-select").getAttribute("value")?.trim(),
                    value: el.querySelector("input[type='text']").value,
                    type: el.getAttribute("type"),
                }
            }).filter(entry => entry.property && entry.value);

        (propertiesList.length) ? this.button.setAttribute("userpilot-update-props-v2", JSON.stringify(propertiesList)) : this.button.removeAttribute("userpilot-update-props-v2");
    }

    getPropertiesElements = () => {
        const propertiesAttrV2 = this.button.getAttribute("userpilot-update-props-v2");
        if(propertiesAttrV2) {
            return JSON.parse(propertiesAttrV2)
                .map(propertyDetails => {
                    const propertyValue = propertyDetails.property;
                    const propertiesList = (propertyDetails.type === PROPERTIES_TYPES.USER)
                        ? UserCompnayProperties.getUserProperties({ withAutoProperties: false, withUserId: false, })
                        : UserCompnayProperties.getCompanyProperties({ withAutoProperties: false, });

                    return {
                        title: propertiesList.find(property => property.legacy_key === propertyValue)?.display_name || this.getPropertyFallbackValue(propertyValue, propertyDetails.type),
                        property: propertyValue,
                        value: propertyDetails.value,
                        type: propertyDetails.type,
                    }
                })
                .filter(entry => entry.property && entry.value);
        }

        return this.button.getAttribute("userpilot-update-props")?.split(";")
            ?.map(string => {
                const propertyValue = string.split(":")[0];
                const propertiesList = UserCompnayProperties.getUserProperties({ withAutoProperties: false, withUserId: false, });
                
                return {
                    title: propertiesList.find(property => property.legacy_key === propertyValue)?.display_name || this.getPropertyFallbackValue(propertyValue, PROPERTIES_TYPES.USER),
                    property: propertyValue,
                    value: string.split(":")[1],
                    type: PROPERTIES_TYPES.USER,
                }
            })?.filter(entry => entry.property && entry.value) || [];
    }

    getPropertyFallbackValue = (propertyKey, propertyType) => {
        const findPropertyTitle = (property) => property.legacy_key === propertyKey;
        const propertiesList = (propertyType === PROPERTIES_TYPES.USER) ? Common.userData.userPropertiesList : Common.userData.companyPropertiesList;

        return propertiesList.find(findPropertyTitle).display_name;
    }

    /* END */

    /* TRACK EVENT */

    initTrackEvents(popupContainer) {
        this.initTrackEventSelectMenu(popupContainer);

        const trackEventCheckbox = popupContainer.querySelector("#track-event");
        const trackEventInput = popupContainer.querySelector("#event-input-container input");
        trackEventCheckbox.addEventListener("change", () => {
            if (trackEventCheckbox.checked) {
                popupContainer.setAttribute("track-event", "on");
            } else {
                popupContainer.removeAttribute("track-event");
                this.button.removeAttribute("track-event");
                trackEventInput.value = "";
            }
        });

        trackEventInput.addEventListener("change", () => {
            this.button.setAttribute("track-event", trackEventInput.value);
        });

        // Set track event state
        const trackEventValue = this.button.getAttribute("track-event");
        if (trackEventValue) {
            trackEventCheckbox.checked = true;
            popupContainer.setAttribute("track-event", "on");
            const displayName = Common.userData.trackableEvents.find(item => item.key === trackEventValue)?.display_name || '';
            trackEventInput.value = displayName || trackEventValue;
        }
    }

    /* END */


    initTrackEventSelectMenu(popupContainer) {
        const eventsSelectButton = popupContainer.querySelector('#track-event-select');

        const elements = Common.userData.trackableEvents
            .filter(item => item.status !== 'archived')
            .map(item => {
                const itemEl = document.createElement('div');
                itemEl.setAttribute('value', item.key);
                itemEl.innerHTML = item.display_name;
                return itemEl;
            });

        const onSelectCallback = (elementSelected) => {
            const trackableEventValue = elementSelected.getAttribute('value');
            const trackableEventItem = Common.userData.trackableEvents.find(trackableEvent => trackableEvent.key === trackableEventValue);
            if(!trackableEventItem) {
                MessageHandler.postTrackableEvent({ title: trackableEventValue, display_name: trackableEventValue, source: "userpilot", type: "trackable_event" })
                    .then(response => {
                        if(!response?.errors) return Common.userData.trackableEvents.push(response);
                        if(response?.errors && !response?.errors?.[0]?.changeset_details?.title?.includes("has already been taken")) {
                            return Alert({
                                severity: "error",
                                content: "Oops something went wrong here!"
                            });
                        }
                    });
            }
            this.button.setAttribute("track-event", trackableEventValue);
        };

        new AdditionSelectMenu(
            eventsSelectButton,
            elements,
            onSelectCallback,
            this.button.getAttribute('track-event'),
            { container: popupContainer, defaultPosition: "bottom", } ,
            { inputPlaceholder: "Select or Add Custom event...", classes: ["track-event-menu"] },
            true
        );
    }

    /* Helpers */

    /** Paste event listner for buttons and text sections */
    initPasteListener() {
        this.sectionContainer.addEventListener('paste', function (e) {
            e.preventDefault();
            const dataPast = (e.clipboardData || window.clipboardData).getData('text');
            const newTextNode = document.createElement("span");
            newTextNode.innerHTML = cleanText(dataPast);

            RangeOperations.replaceSelectionWithNode(this.iframeDocument, this.sectionContainer, this.wysiwygOperations.selection, newTextNode);
            RangeOperations.placeCaretAfterNode(this.iframeDocument, newTextNode);
        }.bind(this));
    }

    setActiveStyles() {
        const toolbarFontSizeContainer = this.popupToolbar.querySelector("#font-size");
        const input = toolbarFontSizeContainer.querySelector("#font-size input");

        const size = parseFloat(this.button.style.fontSize || this.buttonStyles.fontSize);
        input.value = size;

        const fontColorBtn = this.popupToolbar.querySelector("#font-color");
        const fontColorIcon = fontColorBtn.querySelector("svg");
        fontColorIcon.style.fill = this.getButtonFontColor();
    }

    /* END */
}