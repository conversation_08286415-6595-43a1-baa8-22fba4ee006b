import _ from "lodash";
import Common from "../../index.js";
import Views from "../views/templateViews.js";
import ExperienceBuilderViews from "../../views/experienceBuilderViews.js";
import JsonOperations from "../../generic-utils/jsonOperations.js";
import SelectMenu from "../../generic-components/selectMenu.js";

// Utils
import { getTheme } from "../../common/user-data/themes.js";

import Actions from "../../store/constants/actions.js";
import { Store } from "../../store/store.js";
import App from "../../components/app.js";
import { experienceTypes } from "../../store/constants/experiences/constants.js";
import CssOperations from "../../generic-utils/cssOperations.js";
import { spotlightTypes } from "../../store/constants/experiences/spotlightTypes.js";
import { patternTypes } from "../../store/constants/experiences/uiPatternTypes.js";

export default class ThemeManager {
    constructor(groupSettings, templateSettings) {
        this.templateSettings = templateSettings;
        this.type = ThemeManager.getGroupType(templateSettings.type);
        this.stepThemeSettings = templateSettings.stepThemeSettings;
        this.groupSettings = groupSettings;
        this.defaultThemeJson = getTheme(this.groupSettings.theme_id);
        
        let isThemeExist = true;
        if(!this.defaultThemeJson) {
            isThemeExist = false;
            this.defaultThemeJson = Common.userData.themes[0].theme_data_v2;
        }

        this.localizedStepSettings = templateSettings.localizedStepSettings;

        this.overridenGroupSettings = {};
        this.customTheme = this.groupSettings;
        this.isCustomActive = false;
        this.overrideGroupTheme();

        this.themeSettingsNode = Views.getViewElement("theme-settings");
        this.initThemeSelectEvent(this.themeSettingsNode);
        setTimeout(() => {
            if(!isThemeExist) this.themeSelect.resetToFallbackValue() 
        });
    }

    overrideGroupTheme() {
        this.overridenGroupSettings = JSON.parse(JSON.stringify(this.defaultThemeJson));

        const overrideJson = (json, superJson) => {
            for (const key in json) {
                if (key === "individual") continue;
                if (json[key] instanceof Object && !(/boolean|number|string/).test(typeof json[key])) {
                    overrideJson(json[key], superJson[key]);
                } else {
                    if (json[key] !== "" && json[key] !== null && json[key] !== undefined) {
                        superJson[key] = json[key];
                    }
                }
            }
        }
        overrideJson(this.groupSettings, this.overridenGroupSettings);

        this.templateSettings.userpilotSlide.setAttribute("theme_id", this.overridenGroupSettings.theme_id);
        ThemeManager.setGeneralThemeStyle(this.templateSettings.iframeDocument, this.overridenGroupSettings, this.type);
        ThemeManager.addCustomCss(this.templateSettings.iframeDocument, this.overridenGroupSettings);
    }

    initThemeSelectEvent(themeSettingsNode) {
        const themeSelectNode = themeSettingsNode.querySelector("#theme-select");
        const themeSelectOptions = ExperienceBuilderViews.getThemeSelectOptions(Common.userData.themes);
        const customThemeOption = ExperienceBuilderViews.getThemeOptionElement("Custom", "custom", this.overridenGroupSettings.general);
        themeSelectOptions.unshift(customThemeOption);

        const changeGroupTheme = (selected) => {
            const themeId = selected.getAttribute("value");
            const isCustomThemeId = themeId == "custom";
            if (isCustomThemeId) {
                Object.assign(this.groupSettings, this.customTheme);
                this.defaultThemeJson = getTheme(this.customTheme.theme_id);
                this.overrideGroupTheme();
                this.isCustomActive = true;
            } else {
                // If previous theme option was custom, save a copy of the older custom settings
                if (this.isCustomActive) {
                    this.customTheme = JSON.parse(JSON.stringify(this.groupSettings));
                    this.isCustomActive = false;
                }
                this.defaultThemeJson = getTheme(themeId);
                this.resetGroupSettings(themeId);
                this.overrideGroupTheme();
            }
            if(this.type === experienceTypes.BANNER_v2 && this.templateSettings?.userpilotSlide?.style) this.templateSettings.userpilotSlide.style.backgroundColor = "";
            this.templateSettings.reinitGroupSettings();

            const appState = Store.getComponentState(App);
            if(appState.experience.settings.type === experienceTypes.SPOTLIGHT) Store.publishAction(Actions.SAVE_EXPERIENCE_THEME_ID, { theme_id: isCustomThemeId ? this.customTheme.theme_id : themeId });
            else Store.publishAction(Actions.SAVE_EXPERIENCE_CONTENT);
        }

        let experienceThemeId;
        if (this.areSettingsDefault()) {
            experienceThemeId = this.overridenGroupSettings.theme_id;
            customThemeOption.style.display = "none";
            this.isCustomActive = false;
        } else {
            experienceThemeId = "custom";
            customThemeOption.style.display = "";
            this.isCustomActive = true;
        }
        this.themeSelect = new SelectMenu(themeSelectNode, themeSelectOptions, changeGroupTheme.bind(this), experienceThemeId, "", false, {}, {
            getFallbackValue: () => Common.userData.themes[0].id,
            searchable: true,
        });
    }

    getCurrentSettingValue(jsonKeys) {
        let jsonObject;
        switch(jsonKeys[0]) {  
            case "locale":
                jsonObject = this.localizedStepSettings;
                jsonKeys = jsonKeys.slice(1);
                break;

            case "individual":
                jsonObject = this.stepThemeSettings;
                break;

            case "logic":
                jsonObject = this.templateSettings.stepSettings;
                break;

            default:
                jsonKeys[0] = ThemeManager.getGroupType(jsonKeys[0]);
                jsonObject = this.overridenGroupSettings;
        }

        let value;
        jsonKeys.forEach((key, i) => {
            if (i == jsonKeys.length - 1) {
                value = jsonObject[key];
            }
            jsonObject = jsonObject[key];
        });
        return value;
    }

    setJsonSetting(jsonKeys, value) {
        switch(jsonKeys[0]) {
            case "locale":
                this.changeLocalizedIndividualSetting(jsonKeys.slice(1), value);
                Store.publishAction(Actions.SAVE_STEP, {
                    step: {
                        ...this.stepThemeSettings,
                        content: this.templateSettings.templateEditor.languageCode === "default" ? this.stepThemeSettings.content : this.localizedStepSettings.data
                    }
                });
                break;

            case "individual":
                this.changeIndividualSetting(jsonKeys, value)
                Store.publishAction(Actions.SAVE_EXPERIENCE_CONTENT);
                break;

            case "logic":
                this.changeLogicSettings(jsonKeys, value)
                Store.publishAction(Actions.SAVE_EXPERIENCE_CONTENT);
                break;

            default:
                jsonKeys[0] = ThemeManager.getGroupType(jsonKeys[0]);
                this.changeGroupSetting(jsonKeys, value)
                Store.publishAction(Actions.SAVE_EXPERIENCE_CONTENT);
        }
    }

    changeLocalizedIndividualSetting(jsonKeys, value) {
        JsonOperations.setObjectValue(this.localizedStepSettings, jsonKeys, value);
    }

    changeIndividualSetting(jsonKeys, value) {
        JsonOperations.setObjectValue(this.stepThemeSettings, jsonKeys, value);
    }

    changeLogicSettings(jsonKeys, value) {
        JsonOperations.setObjectValue(this.templateSettings.stepSettings, jsonKeys, value);
    }

    changeGroupSetting(jsonKeys, value) {
        if (JsonOperations.setObjectValue(this.overridenGroupSettings, jsonKeys, value))
            JsonOperations.setObjectValue(this.groupSettings, jsonKeys, value);
        
        if (this.areSettingsDefault()) this.revertToDefaultTheme();
        else if (!this.isCustomActive) this.initNewCustomTheme();
    }

    revertToDefaultTheme() {
        const defaultThemeId = this.overridenGroupSettings.theme_id;
        this.themeSelect.setValue(defaultThemeId.toString());
        this.resetGroupSettings(this.themeSelect.selectedValue);
        this.isCustomActive = false;
    }

    resetGroupSettings(resetToThemeID) {
        const version = this.groupSettings.version;
        JsonOperations.clear(this.groupSettings);
        this.groupSettings.theme_id = resetToThemeID;
        this.groupSettings.version = version;
        this.groupSettings[ThemeManager.getGroupType(this.type)] = {};
    }

    initNewCustomTheme() {
        const themeSelectOption = this.themeSelect.getElementWithValue("custom");
        themeSelectOption.style.display = "";
        this.themeSelect.setSelectButtonValue(themeSelectOption);

        this.groupSettings.theme_id = this.overridenGroupSettings.theme_id;
        this.customTheme = this.groupSettings;
        this.isCustomActive = true;
    }

    areSettingsDefault() {
        return JsonOperations.deepEqual(
            this.overridenGroupSettings[this.type],
            this.defaultThemeJson[this.type],
        );
    }

    static setGeneralThemeStyle(iframeDocument, themeSettings, groupType, appendToHead = true) {
        const styles = [
            ...this.getGeneralThemeStyle(themeSettings, "general"),
            ...this.getGeneralThemeStyle(themeSettings, groupType),
        ].join("");
        let styleSheet = iframeDocument.querySelector("#userpilot-theme-style");

        if (!styleSheet) {
            styleSheet = document.createElement("style");
            styleSheet.id = "userpilot-theme-style";
        }
        styleSheet.innerHTML = styles;

        (appendToHead ? iframeDocument.head : iframeDocument).appendChild(styleSheet);
    }

    static getGeneralThemeStyle(themeSettings, groupType) {
        let style = [];
        const generalTheme = themeSettings[this.getGroupType(groupType)];
        const buttons = generalTheme.control || generalTheme.buttons || { next: generalTheme.next, back: generalTheme.back, primary: generalTheme.primary, };

        if (generalTheme.text) {
            style.push(".userpilot-slide:not([type='button'])" + ' { color: ' + generalTheme.text.font.color + ';}');
            style.push(".userpilot-slide:not([type='button'])" + ' > .userpilot-builder { color: ' + generalTheme.text.font.color + ';}');
            style.push(`
                .userpilot-slide .input-section-editor .userpilot-template-input,
                .userpilot-slide .input-section-editor .form-field-label,
                .userpilot-slide .input-section-editor .settings-toggle-row,
                .userpilot-slide .input-section-editor .userpilot-form-label,
                .userpilot-slide .input-section-editor .userpilot-select > .element-value,
                .userpilot-slide .input-section-editor .userpilot-select > .text-value {
                    color: ${generalTheme.text.font.color};
                }
            `);
        }

        if (generalTheme.background_color) {
            style.push(".userpilot-slide:not([type='button'])" + ' {background: ' + generalTheme.background_color + ';}');
            style.push(".userpilot-slide:not([type='button'])" + ' > .userpilot-builder {background: ' + generalTheme.background_color + ';}');
            style.push(`
                .userpilot-slide #userpilot-form input,
                .userpilot-slide #userpilot-form textarea,
                .userpilot-slide #userpilot-form .form-radio-container,
                .userpilot-slide #userpilot-form .phone-input-container .selected-code,
                .userpilot-slide .input-section-editor .userpilot-input,
                .userpilot-slide .input-section-editor .userpilot-select {
                    border-color: ${CssOperations.invertColor(generalTheme.background_color)};
                }
            `);
        }

        if(generalTheme.link) {
            style.push('.userpilot-slide:not([type="button"])' + ' a {color: ' + generalTheme.link.color + '}');
            style.push('.userpilot-slide:not([type="button"])' + ' a:hover{color: ' + generalTheme.link.color_on_hover + ';}');
        }

        if (buttons?.primary) {
            style.push('.userpilot-slide:not([type="button"])' + ' .userpilot-btn:not(.userpilot-progressive-action) {background: ' + buttons.primary.color + '; color: ' + buttons.primary.text_color +
                '; border-color: ' + buttons.primary.border.color + '; border-radius: ' + buttons.primary.border.roundness + 'px; }');
            style.push(`.userpilot-slide:not([type="button"]) .userpilot-builder-section .userpilot-btn:not(.userpilot-progressive-action):not(.userpilot-no-hover):hover {
                background: ${buttons.primary.color_on_hover} !important;
                border-color: ${groupType === experienceTypes.BANNER_v2 ? buttons.primary.border.color : "transparent"} !important;
            }`);
            style.push(`
                .userpilot-slide .likert-scale-preview .likert-scale-steps > div { background-color: ${buttons.primary.color}33; }
                .userpilot-slide .likert-scale-preview .likert-scale-steps > div.active { background-color: ${buttons.primary.color}; color: ${CssOperations.invertColor(buttons.primary.color)}; }
            `);
        }

        if(buttons?.primary?.border && buttons.primary.border.enabled) {
            style.push('.userpilot-slide:not([type="button"])' + ' .userpilot-btn:not(.userpilot-progressive-action) {border-width: ' + buttons.primary.border.thickness + 'px;}');
        }

        if (buttons.next) {
            style.push('.userpilot-slide:not([type="button"])' + ' div.userpilot-progressive-action[uppa="next"] {background: ' + buttons.next.color + '; color: ' + buttons.next.text_color + '; }');
            style.push('.userpilot-slide:not([type="button"])' + ' div.userpilot-progressive-action[uppa="next"]:hover {background: ' + buttons.next.color_on_hover + ';}');
        }

        if (buttons.back) {
            style.push('.userpilot-slide:not([type="button"])' + ' div.userpilot-progressive-action[uppa="back"] {background: ' + buttons.back.color + '; color: ' + buttons.back.text_color + '; }');
            style.push('.userpilot-slide:not([type="button"])' + ' div.userpilot-progressive-action[uppa="back"]:hover {background: ' + buttons.back.color_on_hover + ';}');
        }

        if (generalTheme?.text?.font) {
            const bodyFont = generalTheme.text.font.font_family.replace(/\+/g, " ")
            style.push(".userpilot-slide" + ' {font-family: "' + bodyFont + '", system-ui;}');
            style.push(".userpilot-node-native #badge-element.label" + ' {font-family: "' + bodyFont + '", system-ui;}');
        }

        if (generalTheme?.box_border) {
            style.push('.userpilot-slide:not([type="button"]) {border-radius: ' + generalTheme.box_border.corner_radius + 'px;}');
        }

        return style;
    }

    static getGroupType(groupType) {
        switch(groupType) {
            case patternTypes.DRIVEN_ACTION:
                return "driven_action";

            case patternTypes.SLIDEOUT:
                return "slides_out";

            case spotlightTypes.NATIVE:
            case spotlightTypes.HOTSPOT:
                return experienceTypes.SPOTLIGHT;
            
            default:
                return groupType;
        }
    }

    static addCustomCss(iframeDocument, themeSettings) {
        let customCssStyleEl = iframeDocument.getElementById("userpilot-custom-css");

        if (!customCssStyleEl) {
            customCssStyleEl = document.createElement("style");
            customCssStyleEl.id = "userpilot-custom-css";

            iframeDocument.head.appendChild(customCssStyleEl);
        }

        const cssRules = this.rulesForCssText(themeSettings.custom_css || "");
        const styles = this.fixCustomCSS(cssRules, '');

        customCssStyleEl.innerHTML = styles.cssText.join("");
    }

    static fixCustomCSS(rules, fixCss) {
        const customImportantString = Array.from(rules).reduce((acc, rule) => {
            if (rule.media) {
                let cssText = rule.cssText;
                if (rule.cssRules) {
                    const mediaCssText = ThemeManager.fixCustomCSS(rule.cssRules, '');
                    cssText = '@media ' + rule.media.mediaText + '{' + mediaCssText.cssText.join("") + mediaCssText.fixCss + '}';    
                }
                return [...acc, cssText];
            }

            if (!rule.style) return acc;

            let selector = rule.selectorText;

            if (!selector) {
                if (rule.cssText) return [...acc, rule.cssText];
                return acc;
            }

            selector = ThemeManager.updateCustomCssSelector(rule, fixCss);

            const cssText = rule.style.cssText.split("!important").join("").split(";").join(" !important;");
            return [...acc, selector + "{" + cssText + "}"];
        }, []);

        return {
            'cssText': customImportantString,
            'fixCss': fixCss
        };
    }

    static updateCustomCssSelector(rule, fixCss) {
        let selector = rule.selectorText;

        let last_string = selector.split(" ");
        let temp = last_string;
        last_string = last_string[last_string.length - 1];

        if (last_string == ".userpilot-slide" && selector.split("userpilot-slide-contaienr").length == 1) {
            temp[temp.length - 1] = '';
            temp = temp[0];
            switch (temp) {
                case temp.split(".userpilot-node-modal").length > 1:
                    selector = selector + ', ' + temp.join(" ") + ' .userpilot-slide-contaienr';
                    break;

                case temp.split(".userpilot-node-slideout").length > 1:
                    selector = selector + ', ' + temp.join(" ") + ' .userpilot-slide-contaienr';
                    break;

                case temp.split(".userpilot-node-tooltip").length > 1:
                    /** [todo] */
                    break;

                case temp.split(".userpilot-node-drivenaction").length > 1:
                    /** [todo] */
                    break;

                case temp.split(".userpilot-node-native").length > 1:
                    /** [todo] */
                    break;

                default:
                    break;
            }
            if (rule.style.borderRadius && rule.style.borderRadius !== 'initial') {
                fixCss += ' .userpilot-content-container {border-radius: ' + rule.style.borderRadius + ' !important; overflow: hidden;}';
            }
            if (rule.style.border && rule.style.border !== 'initial' || rule.style.boxShadow && rule.style.boxShadow !== 'initial') {
                fixCss += ' .userpilot-content-container,' + ' .userpilot-slide-contaienr {border: none !important; box-shadow: none !important}';
            }
        }

        return selector;
    }

    static rulesForCssText(styleContent) {
        const doc = document.implementation.createHTMLDocument("");
        const styleElement = doc.createElement("style");
        styleElement.textContent = (styleContent+"").replaceAll(";up-br;", "");
        doc.body.appendChild(styleElement);
        return styleElement.sheet.cssRules;
    }

    getThemeValue(path) {
        return _.get(this.overridenGroupSettings, path);
    }
}