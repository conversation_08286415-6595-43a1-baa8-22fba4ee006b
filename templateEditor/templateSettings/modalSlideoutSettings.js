import TemplateSettings from "./templateSettings.js";
import Views from "../views/templateViews.js";
import SelectMenu from "../../generic-components/selectMenu.js";
import { patternTypes } from "../../store/constants/experiences/uiPatternTypes.js";
import htmlTreeOperations from "../../generic-utils/htmlTreeOperations.js";
import ElementPicker from "../elementPicker.js";
import Element from '../../templateEditor/element/element.js';
import Common from "../../index.js";
import TargetElementManager from './tooltipSettings/targetElementManager.js';
import PostSelectPrompt from '../../generic-components/postSelectPrompt/PostSelectPrompt.js';
import Popup from '../../generic-utils/popup.js';

import { disableScroll, enableScroll, initShiftListener, removeShiftListener } from '../../generic-utils/events.js';

import ExperienceBuilderIcons from "../../views/experienceBuilderIcons.js";
import ExperienceBuilderViews from "../../views/experienceBuilderViews.js";
//constants
import { PROGRESSIVE_INTERACTION_TYPES, POST_SELECT_PROGRESSIVE_TITLES } from '../../store/constants/experiences/constants.js';

//utils
import _ from "lodash";
import tippy from 'tippy.js';

// Store
import Actions from "../../store/constants/actions.js";
import { Store } from "../../store/store.js";
import JsonOperations from "../../generic-utils/jsonOperations.js";

const INTERACTION_OPTIONS = (targetEl) => [
    {
        title: POST_SELECT_PROGRESSIVE_TITLES.CLICK, 
        value: PROGRESSIVE_INTERACTION_TYPES.CLICK,
        icon: ExperienceBuilderIcons.getFeatureTagInteractionTypeIcon(PROGRESSIVE_INTERACTION_TYPES.CLICK),
        disabled: false
    }, 
    {
        title: POST_SELECT_PROGRESSIVE_TITLES.HOVER, 
        value: PROGRESSIVE_INTERACTION_TYPES.HOVER,
        icon: ExperienceBuilderIcons.getFeatureTagInteractionTypeIcon(PROGRESSIVE_INTERACTION_TYPES.HOVER),
        disabled: false

    }, 
    {
        title: POST_SELECT_PROGRESSIVE_TITLES.TEXT_INPUT, 
        value: PROGRESSIVE_INTERACTION_TYPES.TEXT_INPUT,
        icon: ExperienceBuilderIcons.getFeatureTagInteractionTypeIcon(PROGRESSIVE_INTERACTION_TYPES.TEXT_INPUT),
        disabled: !["INPUT", "TEXTAREA"].includes(targetEl.tagName) 
    },
]

export default class ModalSlideoutSettings extends TemplateSettings {
    constructor(type, templateEditor) {
        super(templateEditor, type);
        this.individualSettingsNode = document.createElement("div");
        this.groupSettingsNode;
        this.progressiveTypeMenu = null;
        this.init();
    }

    init() {
        this.initIndividualSettingsNode();
        this.initUiPatternSelectEvent(this.containerNode);
        this.initGroupSettingsNode();
        this.switchIndividualGroupSettings("individual");
        this.positionSideBar();
        this.handleTemplateToolbarPosition();
        if(this.type === patternTypes.SLIDEOUT) this.initMutationObserver();

        this.initTippy();
        this.initLogicSettingsNode();
    }

    initMutationObserver() {
        const tooltipObserver = new MutationObserver(() => this.handleTemplateToolbarPosition());
        tooltipObserver.observe(this.templateEditor.userpilotContent, { childList: true, subtree: true, });
    }

    positionSideBar() {
        if (this.type == "slideout") {
            this.moveSettingsOppositeToSlideout();
            this.userpilotContent.style.position = "fixed";
        } else if (this.type == "modal") {
            this.positionSettingsToLeft();
        }
    }

    initUiPatternSelectEvent(settingsNode) {
        const uiType = this.stepSettings.type;
        const uiTypeSelect = settingsNode.querySelector("#modal-type-select");

        const changeUiPattern = async (select) => {
            const value = select.getAttribute("value");
            JsonOperations.setObjectValue(this.stepSettings, ["type"], value);
            this.switchTypeSettings(value, this.groupSettings);
            await Store.publishAction(Actions.SPLIT_GROUP_AT_STEP, { step: this.stepSettings });
            this.templateEditor.refresh();
        }
        new SelectMenu(uiTypeSelect, null, changeUiPattern.bind(this), uiType);
    }

    switchTypeSettings(type, newGroup) {
        if (newGroup?.settings?.[this.type]) {
            newGroup.settings[type] = newGroup.settings[this.type];
            if(newGroup.settings[type].container?.position) delete newGroup.settings[type].container.position;
            delete newGroup.settings[this.type];
        }

        if (newGroup?.color?.[this.type]) {
            newGroup.color[type] = newGroup.color[this.type];
            delete newGroup.color[this.type];
        }
    }

    initIndividualSettingsNode() {
        this.individualSettingsNode.innerHTML = (this.type === patternTypes.slideout) ? Views.getView("slideout-settings") : Views.getView("modal-settings");
        this.stepSettings.ea = this.stepSettings.ea || {}; // support old modals & slideouts with empty default element settings
        this.initCommonIndividualSettingsEvents(this.individualSettingsNode);
        this.elementDetectionManager = new TargetElementManager(this.getTargetElementSettings(), this.individualSettingsNode, {
            setManualTargetElementSettings: this.setManualTargetElementSettings,
            setAutoTargetElementSettings: this.setAutoTargetElementSettings,
            findTargetCallback: (target) => this.findTargetCallback(target, this.individualSettingsNode),
            getAutoTargetElementSettings: this.getAutoTargetElementSettings,
            getManualTargetElementSettings: this.getManualTargetElementSettings,
            setTargetElementCallback: (target) => this.setTargetElementCallback(target, this.individualSettingsNode),
            getTargetElementCallback: () =>  this.targetElement,
        });


        if(this.themeManager.getCurrentSettingValue([this.type, "backdrop_enabled"])) {
            this.disableProgressiveInteractionClicks();           
        }

        this.initProgressiveInteractionTippyEvents();
    }

    initProgressiveInteractionTippyEvents() {
        tippy(
            this.individualSettingsNode.querySelector("#progressive-interaction-settings"), 
            {   
                appendTo: Common.shadowRoot, 
                content:  Views.getViewElement("tooltip-html", {textContent: `The backdrop option is enabled for this group. </br>Disable it to add an interact to advance action.`}),
                placement: 'right', 
                offset: [0, 30],
                onShow: (instance) => {
                    return instance.reference.classList.contains('inactive-click');
                },
            }
        );
    }

    findTargetCallback(target, settingsNode) {
        const isManual = this.themeManager.getCurrentSettingValue(["individual", "detection"])?.element;
        //hide target element settings if target not set/not found for auto element detection
        if(!target && !isManual) {
            this.hideTargetElementSettings(settingsNode);
            return false;
        }
        this.showTargetElementSettings(settingsNode)
        return null;
    }

    hideTargetElementSettings(settingsNode) {
        settingsNode.querySelector('#detection-type').classList.add('hidden');
        settingsNode.querySelector('#auto-detection-settings').classList.add('hidden');
        settingsNode.querySelector('#manual-detection-settings').classList.add('hidden');
        settingsNode.querySelector('#progressive-interaction-type-menu').classList.add('hidden');
    }

    showTargetElementSettings(settingsNode) {
        settingsNode.querySelector('#detection-type').classList.remove('hidden');
        settingsNode.querySelector('#auto-detection-settings').classList.remove('hidden');
        settingsNode.querySelector('#manual-detection-settings').classList.remove('hidden');
        settingsNode.querySelector('#progressive-interaction-type-menu').classList.remove('hidden');
    }

    setTargetElementCallback(target, settingsNode) {
        const typeSelect = settingsNode.querySelector("#progressive-interaction-type-menu");
        this.targetElement = target;
        !["INPUT", "TEXTAREA"].includes(this.targetElement.tagName) && typeSelect.classList.add("non-input-field");
    }

    targetElementTypeEffect() {
        if(!["INPUT", "TEXTAREA"].includes(targetEl.tagName)) {
            typeSelect.classList.add("non-input-field");

            if(typeSelect.selectedValue === PROGRESSIVE_INTERACTION_TYPES.TEXT_INPUT) {
                typeSelect.setValue(PROGRESSIVE_INTERACTION_TYPES.CLICK);
                this.themeManager.setJsonSetting(["individual", "progressiveInteraction", "type"], PROGRESSIVE_INTERACTION_TYPES.CLICK);
            }
        } else typeSelect.classList.remove("non-input-field");
    }

    initCommonIndividualSettingsEvents(settingsNode) {
        this.initGenericSettingEvents(settingsNode);
        this.initNextEvents(settingsNode);
        this.initBackEvents(settingsNode);
        this.initTemplateSizeEvent(settingsNode);
        this.initArrowExpandEvents(settingsNode);
        this.initElementSelectEvents(settingsNode);
    }

    getTargetElementSettings = () => ({
        templateEditor: this.templateEditor,
        autoDetectionNodeEl: this.individualSettingsNode,
        autoDetectMenuContainer: null,
    })

    setAutoTargetElementSettings = (value) => {
        this.stepSettings.ea = value;
    }

    setManualTargetElementSettings = (field, value) => {
        if(!field) this.themeManager.setJsonSetting(["individual", "detection"], value);
        else this.themeManager.setJsonSetting(["individual", "detection", field], value);
    }

    getAutoTargetElementSettings = () => {
        return this.stepSettings.ea;
    }

    getManualTargetElementSettings = (field) => {
        return (field) ? this.themeManager.getCurrentSettingValue(["individual", "detection", field]) : this.themeManager.getCurrentSettingValue(["individual", "detection"]);
    }

    initElementSelectEvents(settingsNode) {
        const reselectElementButton = settingsNode.querySelector("#reselect-element-target");
        const progressiveInteractionCheckbox = settingsNode.querySelector('#progressive-interaction-checkbox');

        reselectElementButton.addEventListener("click", () => this.selectNewTarget(
            this.stepSettings, 
            "ea", 
            this.retarget
        ));
        progressiveInteractionCheckbox.addEventListener('change', (event) => {
            /*
            * run element targeting if the checkbox is set to true and no target element exists yet
            * & revert back the checkbox status to false if the element targeting is canceled
            */
            const hasProgressiveELement = Boolean(this.stepSettings.ea && !_.isEmpty(this.stepSettings.ea));
            if(Boolean(event.currentTarget.checked)) {
                hasProgressiveELement ? this.disabledBackdropClicks(this.groupSettingsNode) : this.selectNewTarget(this.stepSettings, "ea", this.retarget, () => progressiveInteractionCheckbox.click());
            } else {
                !this.oneStepHasProgressiveInteraction() && this.enableBackdropClicks(this.groupSettingsNode);
            }
        });

        const typeSelect = settingsNode.querySelector("#progressive-interaction-type-menu");
        const interactionType = this.themeManager.getCurrentSettingValue(["individual", "progressiveInteraction", "type"]);
        const changeTriggerType = (select) => {
            const value = select.getAttribute("value");
            this.themeManager.setJsonSetting(["individual", "progressiveInteraction", "type"], value);
        }
        this.progressiveTypeMenu = new SelectMenu(typeSelect, null, changeTriggerType, interactionType || PROGRESSIVE_INTERACTION_TYPES.CLICK);
    }

    retarget = (event, value) => {
        const targetEl = event.target;
        const typeSelect = this.individualSettingsNode.querySelector("#progressive-interaction-type-menu");
        const oldValue = this.themeManager.getCurrentSettingValue(["individual", "progressiveInteraction", "type"]);
        value = value || oldValue;        
        this.progressiveTypeMenu.setValue(value);

        if(!["INPUT", "TEXTAREA"].includes(targetEl.tagName)) {
            typeSelect.classList.add("non-input-field");
            if(this.progressiveTypeMenu.selectedValue === "text_input") {
                value = PROGRESSIVE_INTERACTION_TYPES.CLICK;
                this.progressiveTypeMenu.setValue(value);
            }
        } else typeSelect.classList.remove("non-input-field");

        this.themeManager.setJsonSetting(["individual", "progressiveInteraction", "type"], value);
        this.themeManager.setJsonSetting(["individual", "detection"], {}); // reset manual detection
        this.elementDetectionManager.resetManualDetectionTextIncludeFields();
        this.elementDetectionManager.findTarget();
        this.elementDetectionManager.initAutoDetectionEvents();
        this.elementDetectionManager.setDetectionType("auto");
        this.disabledBackdropClicks(this.groupSettingsNode);
    }

    selectNewTarget(object, key, callback, revertCallback) {
        let isPatternPromptDismissed = true;
        const hasProgressiveELement = Boolean(this.stepSettings.ea && !_.isEmpty(this.stepSettings.ea));
        this.templateEditor.enableScroll();
        const templateIframe = Common.shadowRoot.querySelector("#tool-editor");

        templateIframe.style.display = "none";
        this.containerNode.style.display = "none";
        this.settingsToggle.style.display = "none";

        const revertState = () => {
            templateIframe.style.display = 'initial';
            this.containerNode.style.display = 'initial';
            this.settingsToggle.style.display = "initial";
            Store.publishAction(Actions.HIDE_TOOLTIP_HELP_MESSAGE);
        }
        
        const callbackOnCancel = (cancelSelection = true) => {
            revertState();
            if (revertCallback && cancelSelection) revertCallback();
            this.templateEditor.enableScroll();
        }

        const setElement = (event) => {
            //don't show interaction type popup if the step has old progressive interaction configs
            if(hasProgressiveELement) {
                revertState();
                callback(event);
                return;
            }
            disableScroll();
            const shadowDomRoot = Common.shadowRoot.querySelector(".userpilot");
            const targetEl = event.target;

            Element.highlightElement(targetEl);
            
            const cleanup = () => {
                Element.unhighlightElement();
                postSelectPrompt.destory();
                enableScroll();
                fakeParentEl.remove();
            }

            const onSelect = (typeEvent, value) => {    
                isPatternPromptDismissed = false;
                uiPromptPopup.closePopup();
                revertState();
                callback(event, value);
            }

            const postSelectPrompt = new PostSelectPrompt(targetEl, {
                types: INTERACTION_OPTIONS(targetEl),
                onSelect: onSelect,
                backdropContainer: shadowDomRoot,
            });

            const fakeParentEl = document.createElement("div");
            const onPopupSpawn = () => postSelectPrompt.appendBackdrop();
            const onPopupClose = () => {
                cleanup();
                callbackOnCancel(false);
            }
            const uiPromptPopup = new Popup(postSelectPrompt.componentEl, fakeParentEl, onPopupSpawn, onPopupClose, {
                container: shadowDomRoot,
                defaultDirection: 'bottom',
                positionTo: targetEl,
            })
            uiPromptPopup.spawnPopup();
            
            Store.publishAction(Actions.HIDE_TOOLTIP_HELP_MESSAGE);
            this.templateEditor.disableScroll();
        }
        new ElementPicker(object, key, null, setElement, null, callbackOnCancel);
        Store.publishAction(Actions.SHOW_TOOLTIP_HELP_MESSAGE);
    }

    initGroupSettingsNode() {
        this.groupSettingsNode = Views.getViewElement("slideoutModal-grp-settings");
        this.groupSettingsNode.insertBefore(this.themeManager.themeSettingsNode, this.groupSettingsNode.firstElementChild)
        this.initGroupSettingsEvents(this.groupSettingsNode);
    }

    initGroupSettingsEvents(settingsNode) {
        this.initGenericSettingEvents(settingsNode);
        this.initArrowExpandEvents(settingsNode);

        this.initPlacementSettingsEvents(settingsNode);
        this.initProgressEvent(settingsNode);
        this.initSkippableEvent(settingsNode);
        this.initBackdropEvents(settingsNode);
        this.initBoxEvents(settingsNode);
    }

    initLogicSettingsNode() {
        this.logicSettingsNode = Views.getViewElement("logic-settings");
        this.LogicSettings.initLogicSettingsNodeEvents(this.logicSettingsNode);
    }

    reinitGroupSettings() {
        this.initGroupSettingsNode();
        this.appendGroupSettings();
    }

    changeTemplatePosition(type) {
        if (type == "slideout") {
            this.template.setAttribute("type", "slideout");
        }
        else if (type == "modal") {
            this.template.setAttribute("type", "modal");
        }
    }

    moveSettingsOppositeToSlideout() {
        if (this.type == "slideout") {
            const currentDirection = this.themeManager.getCurrentSettingValue([this.type, "default_position"]);

            this.userpilotContent.setAttribute("dir", currentDirection);
            let templatePosition = currentDirection.split("_").pop();
            if ((templatePosition == "left" && this.settingsToggle.classList.contains("toggle-left"))
                || (templatePosition == "right" && this.settingsToggle.classList.contains("toggle-right"))
            ) {
                this.switchSettingsPosition();
            }
        }
        else {
            console.error("DevError: This function only makes sense when you call it on a slideout template");
        }
    }

    handleTemplateToolbarPosition() {
        const toolbar = this.iframeUserpilotContainer.querySelector("#template-controller-toolbar");
        if(!toolbar) return;

        toolbar.classList.remove("shift-upward")
        const toolbarViewport = htmlTreeOperations.isInViewport(toolbar);

        if(toolbarViewport.vertical) return;
        toolbar.classList.add("shift-upward");
    }
}