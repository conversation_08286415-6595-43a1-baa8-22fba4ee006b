import Views from "../views/templateViews";
import SelectMenu from "../../generic-components/selectMenu";
import TemplateSettings from "./templateSettings";
import ExperienceBuilderViews from "../../views/experienceBuilderViews";
import { SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS } from "../../store/constants/experiences/individualSettings";

export default class BannerSettings extends TemplateSettings {
    constructor(type, templateEditor) {
        super(templateEditor, type);
        this.individualSettingsNode = document.createElement("div");
        this.groupSettingsNode;
        this.init();
    }

    init() {

        this.initResolve = new Promise((resolve) => {
            resolve()
        });

        this.initIndividualSettings();
        this.switchIndividualGroupSettings("individual");
        this.initGroupSettingsNode();
        this.positionSettingsToRight();
        this.initTippy();
    }

    initIndividualSettings() {
        this.individualSettingsNode = Views.getViewElement("banner-individual-settings");
        this.initPlacementSettingsEvents(this.individualSettingsNode, this.hanldeBannerOffset);
        this.initFrequencyEvents(this.individualSettingsNode);
        this.initCommonIndividualSettingsEvents(this.individualSettingsNode);

        this.hanldeBannerOffset();

        this.initBehaviorSettingsEvents(this.individualSettingsNode);
    }

    initCommonIndividualSettingsEvents(settingsNode) {
        this.initGenericSettingEvents(settingsNode);
        this.initArrowExpandEvents(settingsNode);
    }

    initGroupSettingsNode() {
        this.groupSettingsNode = Views.getViewElement("banner-grp-settings");
        this.groupSettingsNode.insertBefore(this.themeManager.themeSettingsNode, this.groupSettingsNode.firstElementChild)
        this.initGroupSettingsEvents(this.groupSettingsNode);
        this.initSkippableEvent(this.groupSettingsNode);
    }

    reinitGroupSettings() {
        this.initIndividualSettings();
        this.appendIndividualSettings();
        this.initGroupSettingsNode();
        this.appendGroupSettings();
    }

    initGroupSettingsEvents(settingsNode) {
        this.initGenericSettingEvents(settingsNode);
        this.initArrowExpandEvents(settingsNode);

        this.initBoxEvents(settingsNode);
        this.initTemplateSizeEvent(settingsNode);
    }

    hanldeBannerOffset = (value) => {
        const bannerContainerSettings = this.themeManager.getCurrentSettingValue([this.type, "default_position"]);
        const additionalInfoEl = this.individualSettingsNode.querySelector("#position-settings .settings-slider .additional-info");
        additionalInfoEl.innerHTML = bannerContainerSettings.position;

        if(bannerContainerSettings.position === "bottom") {
            this.userpilotContent.style.bottom = value || bannerContainerSettings.position_offset;
            this.userpilotContent.style.top = "";
        } else {
            this.userpilotContent.style.bottom = "";
            this.userpilotContent.style.top = value || bannerContainerSettings.position_offset;
            const fakeSpaceEl = this.iframeDocument.querySelector("#fake-space-el");
            fakeSpaceEl.style.height = `${70 + parseInt(this.userpilotContent.style.top)}px`;
        }

        (bannerContainerSettings.position_offset == "0") ? this.userpilotContent.classList.add("zero-offset") : this.userpilotContent.classList.remove("zero-offset");
    }

    initBehaviorSettingsEvents(settingsNode) {
        this.initBannerDisplayBehaviorEvents(settingsNode);
    }

    initBannerDisplayBehaviorEvents(settingsNode) {
        const displaySelect = settingsNode.querySelector("#embedding-display-select");
        const display = this.themeManager.getCurrentSettingValue(["individual", "behavior", "display", "position"]);
        const offsetSlider = settingsNode.querySelector("#position-settings .settings-slider");
        const placementContainer = settingsNode.querySelector("#position-settings");
        const bannerPositionSelectContainer = placementContainer.querySelector("#banner-position-select-container");
        const slidePositionBoxes = placementContainer.querySelector("#slide-position-boxes");
        let placeHolderElement = null;

        const hideElements = (elements) => elements.forEach(element => element.classList.add("display-none"));
        const showElements = (elements) => elements.forEach(element => element.classList.remove("display-none"));

        const displayOnchange = (select, firstRun = false) => {
            const widthSettingEl = this.groupSettingsNode.querySelector("#template-size-settings");
            const value = select.getAttribute("value");
            !firstRun && this.themeManager.setJsonSetting(["individual", "behavior", "display", "position"], value);
            
            const isOverlay = SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS.OVERLAY == value;
            const fullWidth = isOverlay ? this.themeManager.getCurrentSettingValue([this.type, "box_border", "full_width"]) : true;
            const offset = isOverlay ? "" : "0px";
            
            this.handleBannerWidthCheckbox(fullWidth, widthSettingEl);
            this.hanldeBannerOffset(offset);

            const initBannerPositionEvent = new CustomEvent('set-banner-position-setting', { detail: { firstRun } });
            if (isOverlay) {
                showElements([
                    offsetSlider,
                    bannerPositionSelectContainer,
                    slidePositionBoxes,
                    widthSettingEl,
                ]);

                const direction = this.themeManager.getCurrentSettingValue([this.type, "default_position"]);
                placementContainer.querySelector(`.clickable[value=${direction}]`).dispatchEvent(initBannerPositionEvent);

                placeHolderElement = removePlaceHolder(placeHolderElement);
            } else {
                hideElements([
                    offsetSlider,
                    bannerPositionSelectContainer,
                    slidePositionBoxes,
                    widthSettingEl,
                ]);

                placementContainer.querySelector(`.clickable[value="top"]`).dispatchEvent(initBannerPositionEvent);
                placeHolderElement = placeHolderElement || createPlaceHolder(this.userpilotContent);
            }
        }

        this.reInitBannerPlaceholder = () => {
            const display = this.themeManager.getCurrentSettingValue(["individual", "behavior", "display", "position"]);
            if (display == SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS.INLINE) {
                removePlaceHolder(placeHolderElement);
                placeHolderElement = createPlaceHolder(this.userpilotContent);
                this.hanldeBannerOffset("0px")
            }
        }

        new SelectMenu(displaySelect, null, displayOnchange, display);
        this.initResolve.then(() => {
            displayOnchange(displaySelect, true);
        });
    }

    handleBannerWidthCheckbox = (isChecked, widthSettingEl) => {
        if(isChecked) {
            widthSettingEl.classList.add("full-width")
            this.userpilotContent.classList.add("full-width");
        } else {
            widthSettingEl.classList.remove("full-width");
            this.userpilotContent.classList.remove("full-width");
        }
    }

    initTemplateSizeEvent = (settingsNode) => {
        super.initTemplateSizeEvent(settingsNode);
        const bannerFullWidth = this.themeManager.getCurrentSettingValue([this.type, "box_border", "full_width"]);

        const widthSettingEl = settingsNode.querySelector("#template-size-settings");
        const checkbox = settingsNode.querySelector("#template-size-settings .userpilot-checkbox input");
        checkbox.checked = bannerFullWidth;

        checkbox.addEventListener("input", () => {
            this.themeManager.setJsonSetting([this.type, "box_border", "full_width"], checkbox.checked);
            this.handleBannerWidthCheckbox(checkbox.checked, widthSettingEl);
        });

        this.handleBannerWidthCheckbox(checkbox.checked, widthSettingEl);
    }
}

// helpers

const createPlaceHolder = (userpilotContent) => {
    const size = userpilotContent.clientHeight;

    document.body.querySelector("userpilotinlineelement")?.remove();
    let placeHolderElement = ExperienceBuilderViews.getViewElement("userpilot-inline-placeholder", {
        width: size,
        height: size,
        marginLeft: 0,
        marginTop: 0,
        position: "relative",
    })
    
    document.body.insertBefore(placeHolderElement, document.body.firstElementChild);
    
    return placeHolderElement;
}

const removePlaceHolder = (placeHolderElement) => {
    placeHolderElement?.remove();
    return null;
};
