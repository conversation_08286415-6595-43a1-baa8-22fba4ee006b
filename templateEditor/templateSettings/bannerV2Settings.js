import Common from "../..";
import Find from "../element/find";
import { Store } from "../../store/store";
import Views from "../views/templateViews";
import ElementPicker from "../elementPicker";
import TemplateSettings from "./templateSettings";
import Actions from "../../store/constants/actions";
import SelectMenu from "../../generic-components/selectMenu";
import ExperienceBuilderViews from "../../views/experienceBuilderViews";
import TargetElementManager from "./tooltipSettings/targetElementManager";
import { SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS, BANNER_v2_PLACEMENT, BANNER_V2_LAYOUT_OPTIONS } from "../../store/constants/experiences/individualSettings";
import { WIDTH_MAX_VALUE, WIDTH_MIN_VALUE } from "../../store/constants/experiences/groupSettings";
import { isInView } from "./helpers/view";
import UploadImageButton from "../../generic-components/uploadImageButton";
import { isValidUrl } from "../../generic-utils/urlUtils";

export default class BannerV2Settings extends TemplateSettings {
    placeHolderElement = null;

    constructor(type, templateEditor) {
        super(templateEditor, type);
        this.individualSettingsNode = document.createElement("div");
        this.groupSettingsNode;
        this.init();
    }

    init() {

        this.initResolve = new Promise((resolve) => {
            resolve()
        });

        this.initIndividualSettings();
        this.switchIndividualGroupSettings("individual");
        this.initGroupSettingsNode();

        this.iframeUserpilotContainer.querySelector("#virtual-target").addEventListener("click", () => {
            this.selectNewTarget(this.stepSettings, "ea", this.retarget);
        });

        document.addEventListener("visibilitychange", this.handleVisibilityRef.bind(this));
        this.initTippy();
    }

    handleVisibilityRef() {
        const output = setPosition(this.placeHolderElement, this.userpilotContent);
        if (!output) {
            document.removeEventListener("visibilitychange", this.handleVisibilityRef);
        }
    }

    initIndividualSettings() {
        this.individualSettingsNode = Views.getViewElement("banner-v2-individual-settings");
        this.initPlacementSettingsEvents(this.individualSettingsNode, this.onPlacementChange.bind(this));
        this.initCommonIndividualSettingsEvents(this.individualSettingsNode);
        this.initMarginSettings();

        this.initBehaviorSettingsEvents(this.individualSettingsNode);
        this.initAnimationSettings(this.individualSettingsNode);
        this.initContentLayoutSettings(this.individualSettingsNode);

        this.initElementDetectionSettings();
        this.initElementSelectEvents(this.individualSettingsNode);

        this.onPlacementChange(true);
        this.hanldeBannerOffset();
    }

    initMarginSettings() {
        const container = this.themeManager.getCurrentSettingValue(["individual", "container"]);
        const marginSettings = container?.margins || { left: 0, top: 0, right: 0, bottom: 0 };
        const marginInputs = this.individualSettingsNode.querySelectorAll("#margins-controller .userpilot-input");
        const topMarginInput = this.individualSettingsNode.querySelector(`#margins-controller .userpilot-input[name="top"]`);
        const sliderInput = this.individualSettingsNode.querySelector(`[property="bannerOffset"] .slider-input input`);
        const sliderRange = this.individualSettingsNode.querySelector(`[property="bannerOffset"] input[type="range"]`);
        sliderInput.addEventListener("input", (_event) => {
            topMarginInput.value = sliderInput.value;
        });
        sliderRange.addEventListener("input", (_event) => {
            topMarginInput.value = sliderInput.value;
        });
        marginInputs.forEach((input) => {
            input.value = marginSettings[input.getAttribute("name")] || 0;

            const min = parseInt(input.getAttribute("min"));
            const max = parseInt(input.getAttribute("max"));
            input.addEventListener("input", (_event) => {
                const value = input.value || 0;
                if (parseInt(value) > max || parseInt(value) < min) {
                    input.value = value > max ? max : min;
                }
                this.themeManager.setJsonSetting(["individual", "container", "margins", input.getAttribute("name")], value);

                if (input.getAttribute("name") == "top") {
                    sliderInput.value = value;
                    const inputEvent = new Event("input");
                    sliderInput.dispatchEvent(inputEvent);
                }
                this.hanldeBannerOffset();
            });
        });
    }

    async initElementDetectionSettings() {
        this.targetElement = this.findTargetElement();
        if (this.targetElement && this.targetElement !== -1) {
            this.userpilotContent.style.visibility = "hidden";
            await isInView(getTarget(this.targetElement, this.themeManager), findDirection(this.themeManager));
        }
        this.elementDetectionManager = new TargetElementManager(this.getTargetElementSettings(), this.individualSettingsNode, {
            setManualTargetElementSettings: this.setManualTargetElementSettings,
            setAutoTargetElementSettings: this.setAutoTargetElementSettings,
            findTargetCallback: (target) => this.findTargetCallback(target, this.individualSettingsNode),
            getAutoTargetElementSettings: this.getAutoTargetElementSettings,
            getManualTargetElementSettings: this.getManualTargetElementSettings,
            setTargetElementCallback: (target, firstRun) => this.setTargetElementCallback(target, firstRun),
            getTargetElementCallback: () => this.targetElement,
        });
    }

    findTargetElement() {
        try {
            !this.stepSettings.ea && (this.stepSettings.ea = {});

            const settings = {
                manual: 0,
                data: this.stepSettings.ea,
            }
            const targetElement = Find.lookup(settings);
            return targetElement;
        } catch (e) {
            console.error(e);
            return -1;
        }
    }

    onPlacementChange(firstRun) {
        const currentDirection = this.themeManager.getCurrentSettingValue([this.type, "default_position"]);

        const display = this.themeManager.getCurrentSettingValue(["individual", "behavior", "display", "position"]);
        const isOverlayedTopOfPage = display === SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS.OVERLAY && currentDirection === BANNER_v2_PLACEMENT.TOP;
        const isPagePlacement = currentDirection === BANNER_v2_PLACEMENT.BOTTOM || isOverlayedTopOfPage;

        const bannerPlacementContainer = this.individualSettingsNode.querySelector(".banner-position-boxes");
        const elementPlacementContainer = this.individualSettingsNode.querySelector(".banner-position-element");

        const stickyBanner = this.individualSettingsNode.querySelector(`[json-setting="individual-behavior-display-viewport"]`)
        const widthSlider = this.groupSettingsNode?.querySelectorAll("#template-size-settings .settings-slider input") || [];

        const marginsController = this.individualSettingsNode.querySelector("#margin-settings");
        const offsetSlider = this.individualSettingsNode.querySelector(`[property="bannerOffset"]`);

        if (currentDirection === BANNER_v2_PLACEMENT.TOP) {
            this.individualSettingsNode.querySelector("#banner-embedding-settings").classList.remove("hidden");
        } else {
            this.individualSettingsNode.querySelector("#banner-embedding-settings").classList.add("hidden");
        }

        if (!isOverlayedTopOfPage) {
            stickyBanner.style.display = "none";
        } else {
            stickyBanner.style.display = "";
        }

        const resetSettings = () => {
            this.userpilotContent.style.transform = "";
            this.userpilotContent.style.minWidth = "";
            bannerPlacementContainer.style.display = "";
            elementPlacementContainer.style.display = "none";
            this.stepSettings.ea = {};
            !firstRun && this.themeManager.setJsonSetting(["individual", "detection"], {}); // reset manual detection
            this.iframeUserpilotContainer.setAttribute("position", "page");
            this.targetElement = null;
            adjustFlexDirection(this.userpilotSlide, true);

            widthSlider.forEach(input => {
                input.setAttribute("max", WIDTH_MAX_VALUE);
                input.setAttribute("min", WIDTH_MIN_VALUE);
            })
            if (!firstRun) {
                const inputEvent = new Event("input");
                widthSlider[0]?.dispatchEvent(inputEvent);
            }
        }

        removePlaceHolder(this.placeHolderElement, this.userpilotSlide);
        this.placeHolderElement = null;
        const fullWidth = this.themeManager.getCurrentSettingValue([this.type, "box_border", "full_width"]);

        if (isPagePlacement) {
            if (!firstRun) {
                this.widthSettingEl.classList.remove("hidden");
                const width = this.themeManager.getCurrentSettingValue(["individual", "width"]);
                if (!fullWidth) {
                    this.widthSettingEl.classList.remove("full-width");
                    this.userpilotContent.classList.remove("full-width");
                    this.userpilotContent.style.width = `${width}`;
                    this.userpilotSlide.style.width = `${width}`;
                }
            }
            resetSettings();
        } else {
            const hasEmbedElement = currentDirection === BANNER_v2_PLACEMENT.TOP ? true : Boolean(this.stepSettings.ea && !_.isEmpty(this.stepSettings.ea));

            if (currentDirection !== BANNER_v2_PLACEMENT.TOP) {
                this.widthSettingEl?.classList.remove("hidden");
                bannerPlacementContainer.style.display = "none";
                elementPlacementContainer.style.display = "";
            } else {
                this.widthSettingEl?.classList.add("hidden");
                resetSettings();
            }

            if (!hasEmbedElement) {
                this.selectNewTarget(
                    this.stepSettings,
                    "ea",
                    this.retarget
                )
            } else {
                const target = this.targetElement || document.body;
                this.placeHolderElement = createPlaceHolder(this.userpilotContent, target, currentDirection, { themeManager: this.themeManager, userpilotSlide: this.userpilotSlide, placeholder: this.placeHolderElement });
            }
            if (this.targetElement) {

                const maxWidth = this.targetElement.clientWidth;
                const minWidth = Math.floor(maxWidth / 2);


                widthSlider.forEach(input => {
                    input.setAttribute("max", maxWidth);
                    input.setAttribute("min", minWidth);
                })
                if (!firstRun && !fullWidth) {
                    const inputEvent = new Event("input");
                    widthSlider[0]?.dispatchEvent(inputEvent);
                }

                this.targetElement && this.iframeUserpilotContainer.setAttribute("position", "element");
            }
        }

        marginsController.style.display = isPagePlacement ? "none" : "";
        offsetSlider.style.display = isPagePlacement ? "" : "none";

        this.hanldeBannerOffset();

        this.userpilotContent.style.visibility = "";

    }

    initCommonIndividualSettingsEvents(settingsNode) {
        this.initGenericSettingEvents(settingsNode);
        this.initArrowExpandEvents(settingsNode);
    }

    initGroupSettingsNode() {
        this.groupSettingsNode = Views.getViewElement("banner-v2-grp-settings");
        this.groupSettingsNode.insertBefore(this.themeManager.themeSettingsNode, this.groupSettingsNode.firstElementChild)
        this.initGroupSettingsEvents(this.groupSettingsNode);
        this.initSkippableEvent(this.groupSettingsNode);
        this.initBackgroundImage(this.groupSettingsNode);
    }

    reinitGroupSettings() {
        this.initIndividualSettings();
        this.appendIndividualSettings();
        this.initGroupSettingsNode();
        this.appendGroupSettings();
    }

    initGroupSettingsEvents(settingsNode) {
        this.initGenericSettingEvents(settingsNode);
        this.initArrowExpandEvents(settingsNode);

        this.initBoxEvents(settingsNode);
        this.initTemplateSizeEvent(settingsNode, true);
    }

    hanldeBannerOffset = () => {
        const bannerContainerSettings = this.themeManager.getCurrentSettingValue(["individual", "container"]);
        const position = this.themeManager.getCurrentSettingValue([this.type, "default_position"]);
        const isPagePlacement = [BANNER_v2_PLACEMENT.TOP, BANNER_v2_PLACEMENT.BOTTOM].includes(position);
        const isOverlay = BANNER_v2_PLACEMENT.BOTTOM == bannerContainerSettings.position ? true : this.themeManager.getCurrentSettingValue(["individual", "behavior", "display", "position"]) == SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS.OVERLAY;

        const margins = bannerContainerSettings?.margins || { left: 0, top: 0, right: 0, bottom: 0 };

        this.userpilotContent.style.margin = "";
        this.userpilotContent.style.top = "";
        this.userpilotContent.style.bottom = "";

        if (isPagePlacement && isOverlay) {
            if (BANNER_v2_PLACEMENT.BOTTOM === position) {
                this.userpilotContent.style.bottom = `${margins.top}px`;
            } else {
                this.userpilotContent.style.top = `${margins.top}px`;
            }
            return;
        }

        const fakeSpaceEl = this.iframeDocument.querySelector("#fake-space-el");
        fakeSpaceEl.style.height = `${70 + parseInt(this.userpilotContent.style.top)}px`;

        const placeholder = this.placeHolderElement;
        if (placeholder) {
            placeholder.style.marginLeft = `${margins.left}px`;
            placeholder.style.marginTop = `${margins.top}px`;
            placeholder.style.marginRight = `${margins.right}px`;
            placeholder.style.marginBottom = `${margins.bottom}px`;

            setPosition(placeholder, this.userpilotContent)
        } else {
            this.userpilotContent.style.marginLeft = `${margins.left}px`;
            this.userpilotContent.style.marginTop = `${margins.top}px`;
            this.userpilotContent.style.marginRight = `${margins.right}px`;
            this.userpilotContent.style.marginBottom = `${margins.bottom}px`;
        }

    }

    initBehaviorSettingsEvents(settingsNode) {
        this.initBannerDisplayBehaviorEvents(settingsNode);
    }

    initBannerDisplayBehaviorEvents(settingsNode) {
        const displaySelect = settingsNode.querySelector("#embedding-display-select");
        const display = this.themeManager.getCurrentSettingValue(["individual", "behavior", "display", "position"]);

        const position = this.themeManager.getCurrentSettingValue([this.type, "default_position"]);

        if (position !== BANNER_v2_PLACEMENT.TOP) {
            settingsNode.querySelector("#banner-embedding-settings").classList.add("hidden");
        }

        const determineValueFromPlacement = (value) => {
            const position = this.themeManager.getCurrentSettingValue([this.type, "default_position"]);
            switch (position) {
                case BANNER_v2_PLACEMENT.BOTTOM:
                    return SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS.OVERLAY;
                case BANNER_v2_PLACEMENT.TOP:
                    return value;
                default:
                    return SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS.INLINE;
            }
        }

        const displayOnchange = (select, firstRun = false) => {
            const position = this.themeManager.getCurrentSettingValue([this.type, "default_position"]);
            const isPagePlacement = [BANNER_v2_PLACEMENT.TOP, BANNER_v2_PLACEMENT.BOTTOM].includes(position);

            const widthSettingEl = this.groupSettingsNode.querySelector("#template-size-settings");
            const value = determineValueFromPlacement(select.getAttribute("value"))
            !firstRun && this.themeManager.setJsonSetting(["individual", "behavior", "display", "position"], value);

            const isOverlay = !isPagePlacement ? true : SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS.OVERLAY == value;
            const fullWidth = isOverlay ? this.themeManager.getCurrentSettingValue([this.type, "box_border", "full_width"]) : true;

            if (value === SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS.INLINE && position === BANNER_v2_PLACEMENT.TOP) {
                widthSettingEl.classList.add("hidden");
            } else {
                widthSettingEl.classList.remove("hidden");
            }

            this.widthSettingEl = widthSettingEl;
            this.handleBannerWidthCheckbox(fullWidth, widthSettingEl, firstRun);

            if (isOverlay && isPagePlacement) {
                removePlaceHolder(this.placeHolderElement, this.userpilotSlide);
                this.placeHolderElement = null;
            } else {
                const target = this.targetElement || document.body;

                this.placeHolderElement = createPlaceHolder(this.userpilotContent, target, position, { themeManager: this.themeManager, userpilotSlide: this.userpilotSlide, placeholder: this.placeHolderElement });
            }

            this.hanldeBannerOffset();
        }

        this.reInitBannerPlaceholder = () => {
            const display = this.themeManager.getCurrentSettingValue(["individual", "behavior", "display", "position"]);
            const position = this.themeManager.getCurrentSettingValue([this.type, "default_position"]);
            const target = position == BANNER_v2_PLACEMENT.TOP ? document.body : this.targetElement;

            if (determineValueFromPlacement(display) == SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS.INLINE && target) {
                this.placeHolderElement = createPlaceHolder(this.userpilotContent, target, position, { themeManager: this.themeManager, userpilotSlide: this.userpilotSlide, placeholder: this.placeHolderElement });

                this.hanldeBannerOffset()
            }
        }

        new SelectMenu(displaySelect, null, displayOnchange, display);
        this.initResolve.then(() => {
            displayOnchange(displaySelect, true);
        });
    }

    initAnimationSettings = (settingsNode) => {
        const animationSelect = settingsNode.querySelector("#animation-select");
        const animation = this.themeManager.getCurrentSettingValue(["individual", "animation", "animation"]);

        const animationOnchange = (select) => {
            this.themeManager.setJsonSetting(["individual", "animation", "animation"], select.getAttribute("value"));
        }

        new SelectMenu(animationSelect, null, animationOnchange, animation);
    }

    handleLayoutChange = (layout) => {
        this.themeManager.setJsonSetting(["individual", "content_layout", "layout"], layout);
        this.userpilotSlide.setAttribute("layout", layout);
    }

    initContentLayoutSettings = (settingsNode) => {
        const layoutSelect = settingsNode.querySelector("#content-layout-select");
        const layout = this.themeManager.getCurrentSettingValue(["individual", "content_layout", "layout"]);
        this.userpilotSlide.setAttribute("layout", layout);

        const layoutOnchange = (select) => {
            const selectedLayout = select.getAttribute("value");
            this.handleLayoutChange(selectedLayout);
        }

        this.layoutSelectMenu = new SelectMenu(layoutSelect, null, layoutOnchange, layout);
    }

    handleBannerWidthCheckbox = (isChecked, widthSettingEl, firstRun) => {
        if (isChecked) {
            widthSettingEl.classList.add("full-width")
            this.userpilotContent.classList.add("full-width");
        } else {
            widthSettingEl.classList.remove("full-width");
            this.userpilotContent.classList.remove("full-width");
        }
        this.onPlacementChange(firstRun);
    }

    initTemplateSizeEvent = (settingsNode, firstRun) => {
        super.initTemplateSizeEvent(settingsNode);
        const bannerFullWidth = this.themeManager.getCurrentSettingValue([this.type, "box_border", "full_width"]);

        const widthSettingEl = settingsNode.querySelector("#template-size-settings");
        const checkbox = settingsNode.querySelector("#template-size-settings .userpilot-checkbox input");
        checkbox.checked = bannerFullWidth;

        checkbox.addEventListener("input", () => {
            this.themeManager.setJsonSetting([this.type, "box_border", "full_width"], checkbox.checked);
            this.handleBannerWidthCheckbox(checkbox.checked, widthSettingEl);
        });

        this.handleBannerWidthCheckbox(checkbox.checked, widthSettingEl, firstRun);
    }

    initBackgroundImage = (settingsNode) => {
        const uploadContainer = settingsNode.querySelector(".banner-upload-image-container");

        const setBackgroundUrl = (background_url) => this.userpilotSlide.style.background = `url('${background_url}') 50% 50% / cover`;

        const render = () => {
            uploadContainer.innerHTML = "";
            const generalSettings = this.themeManager.getCurrentSettingValue([this.type]);
            const backgroundColor = generalSettings.background_color;
            const background_url = generalSettings.background_url;
            
            if(isValidUrl(background_url)) {
                const uploadedImageEl = Views.getViewElement("uploaded-image-preview", {
                    imageUrl: background_url,
                });
                const reuploudBtn = uploadedImageEl.querySelector(".reupload-image-btn");
                const removeBtn = uploadedImageEl.querySelector(".remove-image-btn");
                removeBtn.addEventListener("click", removeBackgroundUrl);

                uploadContainer.appendChild(uploadedImageEl);
                setBackgroundUrl(background_url)
                new UploadImageButton({ triggerElement: reuploudBtn, onSave: onCustomImageSave, withUnsplash: true, });
                return;
            }

            const uploadButton = Views.getViewElement("upload-image-button", {
                text: "Upload Image",
                classes: "btn-style1",
            });
            uploadContainer.appendChild(uploadButton);
            this.userpilotSlide.style.background = backgroundColor;
            new UploadImageButton({ triggerElement: uploadButton, onSave: onCustomImageSave, withUnsplash: true, });

        }

        const onCustomImageSave = async (imageUrl) => {
            if(!imageUrl) return;
            this.themeManager.setJsonSetting([this.type, "background_url"], imageUrl);
            render();
        }

        const removeBackgroundUrl = () => {
            const generalSettings = _.cloneDeep(this.themeManager.getCurrentSettingValue([this.type]));
            generalSettings.background_url = null;

            this.themeManager.setJsonSetting([this.type], generalSettings);
            render();
        }

        render();
    }

    getTargetElementSettings = () => ({
        templateEditor: this.templateEditor,
        autoDetectionNodeEl: this.individualSettingsNode,
        autoDetectMenuContainer: null,
    })

    setAutoTargetElementSettings = (value) => {
        this.stepSettings.ea = value;
    }

    setManualTargetElementSettings = (field, value) => {
        if (!field) this.themeManager.setJsonSetting(["individual", "detection"], value);
        else this.themeManager.setJsonSetting(["individual", "detection", field], value);
    }

    getAutoTargetElementSettings = () => {
        return this.stepSettings.ea || {};
    }

    getManualTargetElementSettings = (field) => {
        return (field) ? this.themeManager.getCurrentSettingValue(["individual", "detection", field]) : this.themeManager.getCurrentSettingValue(["individual", "detection"]);
    }

    initElementSelectEvents(settingsNode) {
        const reselectElementButton = settingsNode.querySelector("#reselect-tooltip-target");
        reselectElementButton.addEventListener("click", () => {
            this.selectNewTarget(this.stepSettings, "ea", this.retarget);
        });
    }

    retarget = (_event) => {
        this.themeManager.setJsonSetting(["individual", "detection"], {}); // reset manual detection
        this.elementDetectionManager.resetManualDetectionTextIncludeFields();
        this.elementDetectionManager.findTarget();
        this.elementDetectionManager.initAutoDetectionEvents();
        this.elementDetectionManager.setDetectionType("auto");
    }

    selectNewTarget(object, key, callback, revertCallback) {
        this.userpilotContent.classList.remove("invalid")

        removePlaceHolder(this.placeHolderElement, this.userpilotSlide);
        this.placeHolderElement = null;
        this.templateEditor.enableScroll();
        const templateIframe = Common.shadowRoot.querySelector("#tool-editor");

        templateIframe.style.display = "none";
        this.containerNode.style.display = "none";

        const revertState = () => {
            templateIframe.style.display = 'initial';
            this.containerNode.style.display = 'initial';
            Store.publishAction(Actions.HIDE_TOOLTIP_HELP_MESSAGE);
        }

        const callbackOnCancel = (cancelSelection = true) => {
            this.onPlacementChange();
            revertState();
            if (revertCallback && cancelSelection) revertCallback();
            this.templateEditor.enableScroll();
        }

        const setElement = (event, elementTree) => {
            this.userpilotContent.classList.remove("invalid")

            revertState();
            this.setAutoTargetElementSettings(elementTree)
            callback(event);
            this.showTargetElementSettings(this.individualSettingsNode)
            Store.publishAction(Actions.HIDE_TOOLTIP_HELP_MESSAGE);
            Store.publishAction(Actions.SAVE_STEP, { step: this.stepSettings });
        }
        new ElementPicker(object, key, null, setElement, null, callbackOnCancel);
        Store.publishAction(Actions.SHOW_TOOLTIP_HELP_MESSAGE);
    }

    findTargetCallback(target, settingsNode) {
        const position = this.themeManager.getCurrentSettingValue([this.type, "default_position"]);
        if (!target && ![BANNER_v2_PLACEMENT.TOP, BANNER_v2_PLACEMENT.BOTTOM].includes(position)) {
            this.userpilotContent.classList.add("invalid")
        }
        const isManual = this.themeManager.getCurrentSettingValue(["individual", "detection"])?.element;
        //hide target element settings if target not set/not found for auto element detection
        if (!target && !isManual) {
            this.hideTargetElementSettings(settingsNode);
            return false;
        }
        this.showTargetElementSettings(settingsNode)
        return null;
    }

    hideTargetElementSettings(settingsNode) {
        settingsNode.querySelector('#detection-type').classList.add('hidden');
        settingsNode.querySelector('#auto-detection-settings').classList.add('hidden');
        settingsNode.querySelector('#manual-detection-settings').classList.add('hidden');
    }

    showTargetElementSettings(settingsNode) {
        settingsNode.querySelector('#detection-type').classList.remove('hidden');
        settingsNode.querySelector('#auto-detection-settings').classList.remove('hidden');
        settingsNode.querySelector('#manual-detection-settings').classList.remove('hidden');
    }
    async setTargetElementCallback(target, firstRun = false) {
        target && this.userpilotContent.classList.remove("invalid")
        this.targetElement = target;
        this.userpilotContent.style.visibility = "hidden";
        await isInView(getTarget(this.targetElement, this.themeManager), findDirection(this.themeManager));
        this.onPlacementChange(firstRun);
    }
}

// helpers

const findDirection = (themeManager) => {
    let placement = themeManager.getCurrentSettingValue(["banner_v2", "default_position"]);
    switch (placement) {
        case BANNER_v2_PLACEMENT.CONTAINER_TOP:
        case BANNER_v2_PLACEMENT.BEFORE:
            return "top";
        case BANNER_v2_PLACEMENT.AFTER:
        case BANNER_v2_PLACEMENT.CONTAINER_BOTTOM:
            return "bottom";
    }
}

const getTarget = (target, themeManager) => {
    let placement = themeManager.getCurrentSettingValue(["banner_v2", "default_position"]);
    switch (placement) {
        case BANNER_v2_PLACEMENT.CONTAINER_TOP:
        case BANNER_v2_PLACEMENT.CONTAINER_BOTTOM:
            return target.parentNode
        case BANNER_v2_PLACEMENT.BEFORE:
        case BANNER_v2_PLACEMENT.AFTER:
            return target;
    }
}

const adjustFlexDirection = (slide, reset = false) => {
    const colsSection = slide.querySelector(".userpilot-cols-section");
    if (!colsSection) {
        return false;
    }

    if (reset) {
        colsSection.classList.remove("userpilot-cols-section--column-display")
        return false;
    }

    const isColumnDisplay = colsSection.classList.contains("userpilot-cols-section--column-display");
    if (colsSection.scrollWidth == colsSection.clientWidth && isColumnDisplay) {
        colsSection.classList.remove("userpilot-cols-section--column-display")
    }

    if (colsSection.scrollWidth > colsSection.clientWidth) {
        colsSection.classList.add("userpilot-cols-section--column-display")
    } else {
        colsSection.classList.remove("userpilot-cols-section--column-display")
    }
}

const createPlaceHolder = (userpilotContent, element, place, { themeManager, userpilotSlide, placeholder }) => {
    if (userpilotContent.classList.contains("invalid")) {
        userpilotContent.style.transform = "translate(25%, 50%)"
        return null;
    }

    const fullWidth = themeManager.getCurrentSettingValue(["banner_v2", "box_border", "full_width"]);
    const width = fullWidth ? "100%" : themeManager.getCurrentSettingValue(["individual", "width"]) + 'px';

    const display = themeManager.getCurrentSettingValue(["individual", "behavior", "display", "position"]);

    removePlaceHolder(placeholder, userpilotSlide);

    const elementInTable = (element) => {
        let isInTable = 0;
        let parent = element;
        while (parent && parent.tagName !== "BODY") {
            if (parent.tagName === "TABLE") {
                isInTable = 1;
                break;
            }
            parent = parent.parentNode;
        }
        return isInTable;
    }

    const isInTable = elementInTable(element);
    const defaultWidth = isInTable ? 330 : 0;
    let placeHolderElement = ExperienceBuilderViews.getViewElement("userpilot-inline-banner_v2-placeholder", {
        width: width,
        height: 0,
        marginLeft: 0,
        marginTop: 0,
        position: "relative",
        // set min width to 300px when element is in table, 300px is the default width of an iframe.
        minWidth: isInTable ? `${defaultWidth}px` : "",
    })

    if (element?.parentNode?.querySelector("userpilotinlineelement")) {
        element.parentNode.querySelector("userpilotinlineelement").remove();
    }

    switch (place) {
        case BANNER_v2_PLACEMENT.TOP:
            element.insertBefore(placeHolderElement, element.childNodes[0]);
            break;
        case BANNER_v2_PLACEMENT.CONTAINER_TOP:
            element.parentNode.insertBefore(placeHolderElement, element.parentNode.childNodes[0]);
            break;
        case BANNER_v2_PLACEMENT.BEFORE:
            element.parentNode.insertBefore(placeHolderElement, element);
            break;
        case BANNER_v2_PLACEMENT.AFTER:
            element.parentNode.insertBefore(placeHolderElement, element.nextSibling);
            break;
        case BANNER_v2_PLACEMENT.CONTAINER_BOTTOM:
            element.parentNode.appendChild(placeHolderElement);
            break;
    }

    if (place !== BANNER_v2_PLACEMENT.TOP) {
        userpilotContent.style.margin = "";
        setPosition(placeHolderElement, userpilotContent);
        setTimeout(() => {
            setPosition(placeHolderElement, userpilotContent);
        }, 100)
    }

    userpilotContent.style.width = "";
    if (display == SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS.INLINE && place == BANNER_v2_PLACEMENT.TOP) {
        userpilotContent.style.width = `100%`;
        userpilotSlide.style.width = `100%`;
    } else if (fullWidth) {
        userpilotContent.style.minWidth = "auto";
        userpilotContent.style.width = `${placeHolderElement.clientWidth || defaultWidth}px`;
        userpilotSlide.style.width = `${placeHolderElement.clientWidth || defaultWidth}px`;
        if (isInTable) {
            // wait on the placeholder to properly render when it's in a table view.
            setTimeout(() => {
                userpilotContent.style.width = `${placeHolderElement.clientWidth || defaultWidth}px`;
                userpilotSlide.style.width = `${placeHolderElement.clientWidth || defaultWidth}px`;
            }, 50)
        }
    } else if (!fullWidth) {
        userpilotContent.style.width = `${width}`;
        userpilotSlide.style.width = `${width}`;
    }

    place !== BANNER_v2_PLACEMENT.TOP && adjustFlexDirection(userpilotSlide);

    placeHolderElement.style.flex = "0 0 auto";
    placeHolderElement.style.height = userpilotContent.offsetHeight + 'px';
    placeHolderElement.style.minWidth = "300px";
    userpilotSlide.placeholder = placeHolderElement;

    return placeHolderElement;
}

const removePlaceHolder = (placeHolderElement, userpilotSlide) => {
    placeHolderElement?.remove();
    delete userpilotSlide?.placeholder;
    return null;
};

const setPosition = (placeHolderElement, userpilotContent) => {
    try {
        const position = placeHolderElement.getBoundingClientRect();
        userpilotContent.style.transform = `translate(${position.left}px, ${position.top}px)`;

        return true;
    } catch (e) {
        return false;
    }
}
