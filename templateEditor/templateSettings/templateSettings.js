import tippy from "tippy.js";

import Views from "../views/templateViews.js";
import WysiwygViews from "../wysiwyg-builder/wysiwigViews.js";
import SelectMenu from "../../generic-components/selectMenu.js";
import ThemeManager from "./themeManager.js";
import CssOperations from "../../generic-utils/cssOperations.js";
import Common from "../../index.js"
import SolidColorPicker from "../../generic-components/colorPickers/solidColorPicker.js";
import SectionAdditionManager from "../sectionManager/sectionAddition.js";
import htmlTreeOperations from "../../generic-utils/htmlTreeOperations.js";
import { patternTypes } from "../../store/constants/experiences/uiPatternTypes.js";
import { iconAnimationTypes, spotlightTypes } from "../../store/constants/experiences/spotlightTypes.js";
import { FLOW_SKIPPABLE_ACTIONS } from "../../store/constants/experiences/groupSettings.js";
import { BANNER_v2_PLACEMENT, SPOTLIGHT_BEHAVIOR_DISPLAY_DIRECTION, SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS } from "../../store/constants/experiences/individualSettings.js";
import ExperienceBuilderViews from "../../views/experienceBuilderViews.js";
import { COLOR_TYPES, experienceTypes, SKIPPABLE_TYPES } from "../../store/constants/experiences/constants.js";
import InitDraggable from "../../../src/components/appBar/editExperience/featureTag/draggable.js";
import ExperienceBuilderIcons from "../../views/experienceBuilderIcons.js";
import LogicSettings from "./logicSettings.js";
import Preview from "../../components/appBar/editExperience/menus/preview.js";
import App from "../../components/app.js";
import { Store } from "../../store/store.js";
export default class TemplateSettings {
    constructor(templateEditor, type) {
        this.templateEditor = templateEditor;
        this.type = type;
        this.containerNode;
        this.settingsToggle;
        this.currentExpandedSection;
        Views.templateType = type;
        WysiwygViews.templateType = type;

        this.iframeDocument = templateEditor.iframeDocument;
        this.userpilotWindowContainer = Common.shadowRoot.querySelector(".userpilot #window-container");
        this.templateContainer = Common.shadowRoot.querySelector(".userpilot");
        this.template = templateEditor.userpilotSlide;
        this.templateIframe = templateEditor.iframeDocument;
        this.iframeUserpilotContainer = this.templateIframe.querySelector(".userpilot");
        this.userpilotSlide = templateEditor.userpilotSlide;
        this.userpilotContent = templateEditor.userpilotContent;
        this.userpilotSlideContainer = templateEditor.userpilotSlideContainer;
        this.userpilotBuilderContainer = templateEditor.userpilotBuilderContainer;
        this.groupStepsSum = templateEditor.group.steps.length;
        this.stepIndex = templateEditor.stepIndex;
        this.stepSettings = templateEditor.stepSettings;
        this.stepThemeSettings = templateEditor.stepSettings;
        this.groupSettings = templateEditor.groupSettings;
        this.localizedStepSettings = templateEditor.stepLocalizedContent;
        this.infoTooltips = {
            backdrop: null,
            progressiveInteraction: null
        };
        
        this.themeManager = new ThemeManager(this.groupSettings, this);
        if (templateEditor.mode == "EDIT") {
            this.reInitSettingsController();
            this.reInitStepSectionsSidebar();
        } else {
            this.initSettingsController();
            this.initStepSectionsSidebar();
        }

        this.LogicSettings = new LogicSettings(this, templateEditor);
        
        tippy(this.containerNode.querySelectorAll(".userpilot #window-container [label-on-hover]"), { appendTo: Common.shadowRoot });
    }

    initSettingsController() {
        // This is to fix an issue which renders multiple sidebars when previewing/clicking on steps very fast        
        Common.shadowRoot.querySelector("#userpilot-template-settings")?.remove();
        Common.shadowRoot.querySelector("#userpilot-settings-toggle")?.remove();

        this.containerNode = Views.getViewElement("userpilot-template-settings");
        this.containerNode.setAttribute("component", this.type);
        this.userpilotWindowContainer.appendChild(this.containerNode);
        this.initCommonEvents(this.containerNode);

        if (this.type !== experienceTypes.BANNER_v2) {
            this.settingsToggle = Views.getViewElement("userpilot-settings-toggle");
            this.userpilotWindowContainer.appendChild(this.settingsToggle);
            this.initialiseSidebarsEvents(this.settingsToggle);
        }
    }

    reInitSettingsController() {
        this.containerNode = Common.shadowRoot.querySelector("#userpilot-template-settings");
        this.containerNode.innerHTML = Views.getViewElement("userpilot-template-settings").innerHTML;
        this.initCommonEvents(this.containerNode);

        this.settingsToggle = Common.shadowRoot.querySelector("#userpilot-settings-toggle");
    }

    initStepSectionsSidebar() {
        Common.shadowRoot.querySelector("#userpilot-step-sections-sidebar")?.remove();

        this.stepSectionsSidebarNode = Views.getViewElement("userpilot-step-sections-sidebar");
        this.renderStepSections();
        this.initStepSectionsSidebarListeners();
        this.userpilotWindowContainer.appendChild(this.stepSectionsSidebarNode);
    }

    reInitStepSectionsSidebar() {
        this.stepSectionsSidebarNode = Common.shadowRoot.querySelector("#userpilot-step-sections-sidebar");
        this.stepSectionsSidebarNode.innerHTML = Views.getViewElement("userpilot-step-sections-sidebar").innerHTML;
        this.renderStepSections();
    }

    initStepSectionsSidebarListeners() {
        this.stepSectionsSidebarNode.addEventListener("stepSectionsChange", (event) => {
            this.reInitStepSectionsSidebar();
        });
    }

    renderStepSections() {
        const sectionsContainer = this.stepSectionsSidebarNode.querySelector(".step-sections");

        const getSectionLabel = (classList) => {
            switch (true) {
                case classList.contains("userpilot-header-section") === true:
                    return "Header";

                case classList.contains("userpilot-text-section") === true:
                    return "Text";

                case classList.contains("userpilot-image-section") === true:
                    return "Image";

                case classList.contains("userpilot-button-section") === true:
                    return "Button";

                case classList.contains("userpilot-emoji-section") === true:
                    return "Emoji";

                case classList.contains("userpilot-input-radio") === true:
                    return "Multiple choice";

                case classList.contains("userpilot-input-text") === true:
                    return "Single input";

                case classList.contains("userpilot-input-text-large") === true:
                    return "Open text";

                case classList.contains("userpilot-input-likert-scale") === true:
                    return "Likert scale";

                case classList.contains("userpilot-embed-section") === true:
                    return "Embed";

                case classList.contains("userpilot-html-section") === true:
                    return "HTML";

                case classList.contains("userpilot-video-section") === true:
                    return "Video";

                default:
                    return "";
            }
        }

        const stepTemplate = document.createElement("div");
        stepTemplate.innerHTML = this.stepSettings.content;

        Array.from(stepTemplate.querySelectorAll(".userpilot-builder-section") || [])
            .forEach(section => {
                const label = getSectionLabel(section.classList);
                const sectionId = section.getAttribute("unit_id");
                const isHidden = section.hasAttribute("hide-on-mobile");

                const viewEl = Views.getViewElement("step-section-item", {
                    label: label,
                    isHidden: isHidden,
                    sectionId: sectionId,
                    eyeIcon: ExperienceBuilderIcons.getEyeIcon(),
                    hiddenIcon: ExperienceBuilderIcons.getHiddenIcon(),
                });

                viewEl.querySelector(".visibility-icon").addEventListener("click", (e) => {
                    const containerElement = e.currentTarget.parentElement;
                    const isHiddenNow = !(containerElement.getAttribute("is-hidden") === "true");

                    containerElement.setAttribute("is-hidden", isHiddenNow);
                    
                    const sectionEl = this.userpilotBuilderContainer.querySelector(`[unit_id='${sectionId}']`);
                    isHiddenNow ? sectionEl?.setAttribute("hide-on-mobile", "") : sectionEl?.removeAttribute("hide-on-mobile");

                    const localizationData = this.templateEditor.updateHideOnMobileAttribute(sectionId);
                    this.templateEditor.saveStepContent(localizationData);
                });
    
                sectionsContainer.appendChild(viewEl);
            });
    }

    reinitNodeSettings = (oldNodeId, newNode, reinitCallback, firstRun = false) => {
        const oldNode = this.individualSettingsNode.querySelector(`#${oldNodeId}`) || this.groupSettingsNode.querySelector(`#${oldNodeId}`);

        reinitCallback(newNode, firstRun);
        this.addExpandSettingListener([newNode]);
        oldNode.replaceWith(newNode);
    }

    initCommonEvents(settingsNode) {
        const individualGroupSwitchBtns = Array.from(settingsNode.querySelectorAll("#individual-grp-switch > div"));
        individualGroupSwitchBtns.forEach(btn => {
            if (experienceTypes.FLOW !== Common.backgroundState.type && btn.getAttribute("value") === "logic") {
                btn.remove();
            } else{
                btn.addEventListener("click", this.switchIndividualGroupSettings.bind(this, btn.getAttribute("value")));
            }
        });

        let moveIcon = this.containerNode.querySelector("#move-sidebar");
        let closeIcon = this.containerNode.querySelector("#close-sidebar");
        let dragIcon = this.containerNode.querySelector("#drag-sidebar");
        if (this.type === experienceTypes.BANNER_v2) {
            const DIALOG_POSITION_KEY = "userpilot-chrome-extension-dialog-position";
            const storageData = localStorage.getItem(DIALOG_POSITION_KEY);
            const dialogPosition = storageData ? JSON.parse(storageData) : { left: 20, top: 100 };
            const dragContainer = dragIcon.parentElement;

            moveIcon.remove();
            closeIcon.remove();
            let draggableInstance = null;

            const Draggable = InitDraggable();
            const dialog = this.templateEditor.userpilotWindowContainer.querySelector("#userpilot-template-settings")

            dialog.style.left = dialogPosition.left + "px";
            dialog.style.top = dialogPosition.top + "px";

            const destroyDraggable = () => {
                if (!draggableInstance) return;

                draggableInstance.destroy();
                dragContainer.style.cursor = "";
                this.templateEditor.iframe.style.pointerEvents = "";
            }


            const handleDragEnd = () => {
                const style = dialog.style;
                const position = {
                    left: parseInt(style.left),
                    top: parseInt(style.top)
                }

                position.top < 0 && (position.top = 0);
                position.left < 0 && (position.left = 0);

                localStorage.setItem(DIALOG_POSITION_KEY, JSON.stringify(position));

                destroyDraggable();
            }

            dragContainer.addEventListener("mousedown", (event) => {
                // prevent draggable on right click
                if (event.which !== 1) return;
                destroyDraggable();
                this.templateEditor.iframe.style.pointerEvents = "none";
                draggableInstance = new Draggable(dialog, { setPosition: false, onDragEnd: handleDragEnd, limit: document.body })
                dragContainer.style.cursor = "grabbing";
            })

            dragContainer.addEventListener("mouseup", handleDragEnd);
            dialog.addEventListener("mouseup", handleDragEnd);

            return false;
        }

        dragIcon.remove();

        moveIcon.addEventListener("click", (event) => {
            this.switchSettingsPosition(this.settingsToggle);
            if (this.containerNode.classList.contains("settings-slide-left")) {
                this.containerNode.style.left = "0px";
                this.stepSectionsSidebarNode.style.right = "0px";
            } else {
                this.containerNode.style.right = "0px";
                this.stepSectionsSidebarNode.style.left = "0px";
            }
        });
        closeIcon.addEventListener("click", this.hideSettingsWidget.bind(this));
    }

    toggleWidget() {
        (this.containerNode.style.transform) ? (this.containerNode.style.transform = "", this.stepSectionsSidebarNode.style.transform = "") : this.hideSettingsWidget();
    }

    hideSettingsWidget() {
        const widgetWidth = this.containerNode.offsetWidth;
        if (this.containerNode.classList.contains("settings-slide-left")) {
            this.containerNode.style.transform = "translateX(-" + widgetWidth + "px)";
        } else if (this.containerNode.classList.contains("settings-slide-right")) {
            this.containerNode.style.transform = "translateX(" + widgetWidth + "px)";
        }

        const widgetWidth2 = this.stepSectionsSidebarNode.offsetWidth;
        if (this.stepSectionsSidebarNode.classList.contains("settings-slide-left")) {
            this.stepSectionsSidebarNode.style.transform = "translateX(-" + widgetWidth2 + "px)";
        } else if (this.stepSectionsSidebarNode.classList.contains("settings-slide-right")) {
            this.stepSectionsSidebarNode.style.transform = "translateX(" + widgetWidth2 + "px)";
        }
    }

    switchIndividualGroupSettings(type) {
        let individualSetting = this.containerNode.querySelector("#individual-switch");
        let groupSetting = this.containerNode.querySelector("#group-switch");
        let logicSetting = this.containerNode.querySelector("#logic-switch");

        if (type == "individual") {
            individualSetting.classList.add("on");
            groupSetting.classList.remove("on")
            logicSetting?.classList.remove("on")
            this.appendIndividualSettings();
        } else if (type == "group") {
            groupSetting.classList.add("on");
            individualSetting.classList.remove("on");
            logicSetting?.classList.remove("on");
            this.appendGroupSettings();
        } else {
            if (logicSetting?.classList.contains("disabled")) return;
            logicSetting?.classList.add("on");
            individualSetting.classList.remove("on");
            groupSetting.classList.remove("on");
            this.LogicSettings.appendLogicSettings();
        }
    }

    appendIndividualSettings() {
        let settingsNode = this.containerNode.querySelector("#template-current-settings");
        settingsNode.innerHTML = "";
        settingsNode.appendChild(this.individualSettingsNode);
    }

    appendGroupSettings() {
        let settingsNode = this.containerNode.querySelector("#template-current-settings");
        settingsNode.innerHTML = "";
        settingsNode.appendChild(this.groupSettingsNode);
    }

    initialiseSidebarsEvents(settingsToggle) {
        settingsToggle.addEventListener("click", () => {
            this.containerNode.style.transform = "";
            this.stepSectionsSidebarNode.style.transform = "";
        });
    }

    switchSettingsPosition() {
        if (this.settingsToggle.classList.contains("toggle-left")) {
            this.positionSettingsToRight()
        } else {
            this.positionSettingsToLeft()
        }
    }

    positionSettingsToLeft() {
        this.settingsToggle.classList.remove("toggle-right");
        this.settingsToggle.classList.add("toggle-left");

        this.containerNode.classList.remove("settings-slide-right");
        this.containerNode.classList.add("settings-slide-left");

        this.stepSectionsSidebarNode.classList.add("settings-slide-right");
        this.stepSectionsSidebarNode.classList.remove("settings-slide-left");
    }

    positionSettingsToRight() {
        this.settingsToggle.classList.remove("toggle-left");
        this.settingsToggle.classList.add("toggle-right");

        this.containerNode.classList.remove("settings-slide-left");
        this.containerNode.classList.add("settings-slide-right");

        this.stepSectionsSidebarNode.classList.add("settings-slide-left");
        this.stepSectionsSidebarNode.classList.remove("settings-slide-right");
    }

    initArrowExpandEvents(settingsNode) {
        const expandingOptions = Array.from(settingsNode.querySelectorAll(".expandable-row"));
        this.addExpandSettingListener(expandingOptions);
    }

    addExpandSettingListener(expandingOptions) {
        expandingOptions.forEach(option => {
            const expandBtn = option.querySelector(".settings-row");
            expandBtn && expandBtn.addEventListener("click", this.expandOrCollapseSection.bind(this, option));
        });
    }

    expandOrCollapseSection(section) {
        if (!section.classList.contains("toggled") && !section.classList.contains("not-optional")) {
            return;
        }
        if (section.classList.contains("expanded")) {
            this.collapseSection(section)
        } else {
            this.expandSection(section)
        }
    }

    expandSection(section) {
        //prevent section expand if click disabled
        if (section.classList.contains('inactive-click')) return;

        if (!section.classList.contains("inner-expand")) {
            if (this.currentExpandedSection) this.collapseSection(this.currentExpandedSection);
            this.currentExpandedSection = section;
        }
        section.classList.add("expanded");
    }

    collapseSection(section) {
        section.classList.remove("expanded");
    }

    initGenericSettingEvents(settingsNode) {
        const togglesAndCheckboxes = Array.from(settingsNode.querySelectorAll(".userpilot-toggle, .userpilot-checkbox"));
        togglesAndCheckboxes.forEach(toggle => {
            this.initToggleEvent(toggle, settingsNode);
        });

        const colorPickers = Array.from(settingsNode.querySelectorAll(".settings-color-picker"));
        colorPickers.forEach(colorPicker => {
            this.initColorPickerEvent(colorPicker);
        });

        const sliders = Array.from(settingsNode.querySelectorAll(".settings-slider"));
        sliders.forEach(slider => {
            this.initSliderEvent(slider)
        });

        const previewButton = settingsNode.querySelector("#preview-from-step");

        const appState = Store.getComponentState(App);

        previewButton && previewButton.addEventListener("click", () => {
            const preview = Preview({
                previewFrom: "step",
                stepId: this.stepSettings.action_id
            })
            preview(appState.experience.content, "step")
        });
        this.initRawInputsEvents(settingsNode);
    }

    initRawInputsEvents(settingsNode) {
        const rawInputs = Array.from(settingsNode.querySelectorAll(".settings-raw-input"));
        rawInputs.forEach(rawInput => {
            this.initRawInputEvent(rawInput);
        })
    }

    initToggleEvent(toggle, settingsNode) {
        const jsonSetting = toggle.getAttribute("json-setting");
        if (!jsonSetting) {
            return;
        }
        const action = toggle.getAttribute("action");
        const input = toggle.querySelector("input");
        const jsonKeys = jsonSetting.split("-");
        let expandingSection = toggle.getAttribute("for");

        if (action == "expand" && !expandingSection) {
            console.error("DevError: If this toggle is supposed to expand a section then it must have that section id defined in the for attribute");
        }

        if (action == "expand") {
            expandingSection = settingsNode.querySelector("#" + expandingSection);
        }
        const isGroupSetting = ![ "locale", "individual", "logic", ].includes(jsonKeys[0]);
        const settingValue = this.themeManager.getCurrentSettingValue(jsonKeys);
        if ( (isGroupSetting && settingValue) || settingValue == "1" ) {
            input.checked = true;
            if (expandingSection) {
                expandingSection.classList.add("toggled");
                if (expandingSection.classList.contains("inner-expand")) {
                    this.expandSection(expandingSection);
                }
            }
        }

        this.initToggleChangeEvent({ input, expandingSection, action, jsonKeys, jsonSetting, isGroupSetting });
    }

    initToggleChangeEvent = ({ input, expandingSection, action, jsonKeys, jsonSetting, isGroupSetting } = {}) => {
        input.addEventListener("change", () => {
            if (input.checked) {
                if (action == "expand") {
                    expandingSection.classList.add("toggled");
                    this.expandSection(expandingSection);
                }
                if (jsonSetting) {
                    this.themeManager.setJsonSetting(jsonKeys, isGroupSetting ? true : "1");
                }
            } else {
                if (action == "expand") {
                    expandingSection.classList.remove("toggled");
                    this.collapseSection(expandingSection);
                }
                if (jsonSetting) {
                    this.themeManager.setJsonSetting(jsonKeys, isGroupSetting ? false : "0");
                }
            }
        });
    }

    initColorPickerEvent(colorPicker) {
        const colorContainer = colorPicker.querySelector(".color-box");
        const colorText = colorPicker.querySelector(".color-text");

        const getCurrentColor = () => this.themeManager.getCurrentSettingValue(jsonKeys);

        const updateDisplay = () => {
            const currentColor = getCurrentColor();
            colorContainer.style.background = currentColor;
            colorText.textContent = currentColor.toUpperCase();
        }

        const jsonSetting = colorPicker.getAttribute("json-setting");
        const jsonKeys = jsonSetting.split("-");
        const setColor = (color) => {
            this.themeManager.setJsonSetting(jsonKeys, color);
            updateDisplay();
        }

        const targetElements = colorPicker.getAttribute("action-on").split(",");
        const colorProperties = colorPicker.getAttribute("property");
        new SolidColorPicker(colorPicker, getCurrentColor, {
            onChangeCallback: ({ hexa, rgba }) => {
                this.changeTargetColor(targetElements, colorProperties, rgba);
                setColor(hexa);
            }
        },
            {
                rgba_response: true,
                ...jsonSetting.endsWith("-backdrop-color") && { components: { opacity: false, } },
            }
        );

        updateDisplay();
    }

    changeTargetColor(targetElements, colorProperties, newColor) {
        const elementsToColor = targetElements.reduce((acc, element) => [...acc, ...Array.from(this.templateIframe.querySelectorAll(element))], []);

        elementsToColor.forEach(element => {
            switch (colorProperties) {
                case "rgba-background":
                    element.style.background = CssOperations.updateRGBofRGBA(newColor, getComputedStyle(element).backgroundColor);
                    break;

                case "rgba-backdrop":
                    const backdropIndividualSettings = this.themeManager.getCurrentSettingValue(["individual"]);

                    if (backdropIndividualSettings.backdropfocus == 1) {
                        element.style.borderColor = CssOperations.updateRGBofRGBA(newColor, getComputedStyle(element).borderColor);
                    } else {
                        const backdropSettings = this.themeManager.getCurrentSettingValue([this.type, "backdrop"]);
                        const backgroundGradient = element.style.background;
                        const rgba = CssOperations.updateRgbaOpacity(newColor, backdropSettings.opacity / 100);

                        element.style.background = CssOperations.updateGradientRgbaColor(backgroundGradient, rgba);
                    }
                    break;

                case "shadow-color":
                    const shadowWidth = this.themeManager.getCurrentSettingValue([this.type, "box_border", "width"]);
                    const shadowIntensity = this.themeManager.getCurrentSettingValue([this.type, "box_border", "shadow_intensity"]);
                    element.style.boxShadow = newColor + " 0px 0px " + shadowIntensity + "px " + shadowWidth + "px";
                    break;

                default:
                    colorProperties.split(",").forEach(property => {
                        element.style.setProperty(property.trim(), newColor);
                    });
                    break;
            }
        });
    }

    initSliderEvent(slider) {
        const actionOnElements = slider.getAttribute("action-on").split(",");
        const jsonSetting = slider.getAttribute("json-setting");
        const jsonKeys = jsonSetting.split("-");
        const numericalInput = slider.querySelector("input[type='number']");
        const sliderInput = slider.querySelector("input[type='range']");
        const property = slider.getAttribute("property");
        const sliderSecondColor = "var(--color-slider-background)";

        let maxValue = parseInt(sliderInput.getAttribute("max"));
        let minValue = parseInt(sliderInput.getAttribute("min"));
        let minMaxDiff = maxValue - minValue;

        const sliderValue = this.themeManager.getCurrentSettingValue(jsonKeys);
        const sliderPercentage = (parseFloat(sliderValue) - parseInt(minValue)) / minMaxDiff * 100
        numericalInput.value = sliderValue;
        sliderInput.value = sliderValue;
        sliderInput.style.background = `linear-gradient(to right, var(--color-primary) 0%, var(--color-primary) ${sliderPercentage}%, ${sliderSecondColor} ${sliderPercentage}%, ${sliderSecondColor} 100%)`;

        const applyInput = (value) => {
            maxValue = parseInt(sliderInput.getAttribute("max"));
            minValue = parseInt(sliderInput.getAttribute("min"));
            minMaxDiff = maxValue - minValue;

            const elementsToStyle = actionOnElements.map(element => this.templateIframe.querySelector(element)).filter(element => element != null);
            const sliderPercentage = (parseFloat(value) - parseInt(minValue)) / minMaxDiff * 100;
            sliderInput.style.background = `linear-gradient(to right, var(--color-primary) 0%, var(--color-primary) ${sliderPercentage}%, ${sliderSecondColor} ${sliderPercentage}%, ${sliderSecondColor} 100%)`;
            this.themeManager.setJsonSetting(jsonKeys, value);
            elementsToStyle.forEach(element => {
                switch (property) {
                    case "background-color-opacity":
                        element.style.background = CssOperations.updateRgbaOpacity(getComputedStyle(element).backgroundColor, value / 100);
                        break;
                    case "backdrop-color-opacity":
                        const backdropIndividualSettings = this.themeManager.getCurrentSettingValue(["individual"]);

                        if (backdropIndividualSettings.backdropfocus == 1) {
                            element.style.borderColor = CssOperations.updateRgbaOpacity(getComputedStyle(element).borderColor, value / 100);
                        } else {
                            const backgroundGradient = element.style.background;
                            element.style.background = CssOperations.updateGradientRgbaColorOpacity(backgroundGradient, value / 100);
                        }
                        break;
                    case "backdrop-focus-radius":
                        element.style.borderRadius = (10000 + Number(value)) + "px";
                        break;
                    case "shadow-width":
                    case "shadow-intensity":
                        const shadowColor = this.themeManager.getCurrentSettingValue([this.type, "box_border", "color"]);
                        const shadowWidth = this.themeManager.getCurrentSettingValue([this.type, "box_border", "width"]);
                        const shadowIntensity = this.themeManager.getCurrentSettingValue([this.type, "box_border", "shadow_intensity"]);
                        element.style.boxShadow = shadowColor + " 0px 0px " + shadowIntensity + "px " + shadowWidth + "px";
                        break;
                    case "backdrop-focus-width":
                    case "backdrop-focus-height":
                        this.setBackdropFocus();
                        break;
                    case "opacity":
                        element.style["opacity"] = value / 100;
                        break;
                    case "badgeIconSize":
                    case "badgeLabelSize":
                        this.handleBadgeSize(element, property, value);
                        break;
                    case "pulseIconSize":
                        this.handleHotspotSize(element, value);
                        break;
                    case "template-width":
                        this.changeTemplateWidth(value);
                        break;
                    case "bannerOffset":
                        this.hanldeBannerOffset(value);
                        break;
                    case "fontWeight":
                        element.style.fontWeight = value;
                        break;
                    default:
                        property.split(",").forEach(property => {
                            element.style[property.trim()] = value + "px";
                        });
                        break;
                }
            });
        }

        const setInputValue = () => {
            const value = this.getInputOrMinMaxValue(numericalInput);
            numericalInput.value = value;
            sliderInput.value = value;
            applyInput(value);
        }

        numericalInput.addEventListener("input", (event) => {
            if (this.isInputOutOfRange(numericalInput)) return;
            setInputValue();
        });

        numericalInput.addEventListener("change", () => {
            if (this.isInputOutOfRange(numericalInput)) setInputValue();
        });

        sliderInput.addEventListener("input", (event) => {
            const value = sliderInput.value;
            numericalInput.value = value;
            applyInput(value);
        });
    }

    initRawInputEvent(rawInput) {
        const jsonSetting = rawInput.getAttribute("json-setting");
        const jsonKeys = jsonSetting.split("-");
        const input = rawInput.querySelector("input");

        const inputValue = this.themeManager.getCurrentSettingValue(jsonKeys);
        input.value = inputValue;

        const applyInput = (event) => {
            const isNumber = (input.type === "number");
            if (isNumber && this.isInputOutOfRange(input)) return;

            setInputValue();
        }
        const setInputValue = (event) => {
            const isNumber = (input.type === "number");
            const value = (isNumber) ? this.getInputOrMinMaxValue(input) : event.currentTarget.value;
            input.value = value;
            this.themeManager.setJsonSetting(jsonKeys, value);
        }
        input.addEventListener("input", applyInput.bind(this));
        input.addEventListener("change", () => {
            const isNumber = (input.type === "number");
            if (isNumber && this.isInputOutOfRange(input)) setInputValue();
        });
    }

    static addProgressionButton(type, buttonNode, userpilotBuilder) {
        const inverseType = type === "next" ? "back" : "next";
        const sibling = userpilotBuilder.querySelector(`#userpilot-${inverseType}-button`);

        if (sibling) {
            SectionAdditionManager.insertHorizontalSection(buttonNode, sibling, type === "next" ? "right" : "left", userpilotBuilder);
            if (type === "back") sibling.style.textAlign = "right";
        } else {
            userpilotBuilder.appendChild(buttonNode);
        }
    }

    initNextEvents(settingsNode) {
        const nextSettingsNode = settingsNode.querySelector("#next-back-setting");
        const nextCheckBox = nextSettingsNode.querySelector("#next-checkbox");

        if (this.templateEditor.languageCode !== "default") nextSettingsNode.style.display = "none";

        const existingNextSection = this.userpilotSlideContainer.querySelector("#userpilot-next-button");
        const defaultNextSection = Views.getNextBackButtonsViewElement("next", this.type);
        defaultNextSection.setAttribute("unit_id", this.stepSettings.action_id + "-" + Math.floor(Math.random(1, 1000) * 10000));

        let nextSectionRef = existingNextSection || defaultNextSection;
        const addNextButton = () => {
            const userpilotBuilder = this.userpilotSlideContainer.querySelector(".userpilot-builder");
            nextSectionRef = nextSectionRef.cloneNode(true);
            TemplateSettings.addProgressionButton("next", nextSectionRef, userpilotBuilder);
            this.templateEditor.setupNewSectionEvents(nextSectionRef);
        }

        const removeNextButton = () => {
            nextSectionRef.remove();
            SectionAdditionManager.readjustColumnSections(this.userpilotSlide);
        }

        nextCheckBox.addEventListener("change", () => {
            nextCheckBox.checked ? addNextButton() : removeNextButton()
            this.templateEditor.saveStepContent();
        });

        nextCheckBox.checked = existingNextSection ? true : false;
    }

    initBackEvents(settingsNode) {
        const backSettingsNode = settingsNode.querySelector("#back-setting");
        const backCheckbox = backSettingsNode.querySelector("#back-checkbox");

        if (this.stepIndex === 0) {
            backSettingsNode.style.display = "none";
            return;
        }

        if (this.templateEditor.languageCode !== "default") backSettingsNode.style.display = "none";

        const existingBackSection = this.userpilotSlideContainer.querySelector("#userpilot-back-button");
        const defaultBackButton = Views.getNextBackButtonsViewElement("back", this.type);
        defaultBackButton.setAttribute("unit_id", this.stepSettings.action_id + "-" + Math.floor(Math.random(1, 1000) * 10000));

        let backSectionRef = existingBackSection || defaultBackButton;
        const addBackButton = () => {
            const userpilotBuilder = this.userpilotSlideContainer.querySelector(".userpilot-builder");
            backSectionRef = backSectionRef.cloneNode(true);
            TemplateSettings.addProgressionButton("back", backSectionRef, userpilotBuilder);
            this.templateEditor.setupNewSectionEvents(backSectionRef);
        }

        const removeBackButton = () => {
            backSectionRef.remove();
            SectionAdditionManager.readjustColumnSections(this.userpilotSlide);
        }

        backCheckbox.addEventListener("change", () => {
            backCheckbox.checked ? addBackButton() : removeBackButton()
            this.templateEditor.saveStepContent();
        });

        backCheckbox.checked = existingBackSection ? true : false;
    }

    initTemplateSizeEvent(settingsNode) {
        const width = this.themeManager.getCurrentSettingValue(["individual", "width"]);
        this.changeTemplateWidth(width);
    }

    changeTemplateWidth = (width) => {
        this.userpilotSlide.setAttribute("template-width", width);
        this.userpilotSlide.style.width = TemplateSettings.calculateTemplateWidth(width, this.type);

        if (this.type == experienceTypes.BANNER_v2) {
            this.userpilotContent.style.width = `${width}px`;

            this.placeHolderAjdustTimeout = clearTimeout(this.placeHolderAjdustTimeout);
            this.placeHolderAjdustTimeout = setTimeout(() => {
                this.reInitBannerPlaceholder();
            }, 200);
        }

    }

    static calculateTemplateWidth(width, templateType) {
        switch (templateType) {
            case patternTypes.MODAL:
                return (width * 16) + "px";

            case patternTypes.SLIDEOUT:
                return (width * 10) + "px";

            case patternTypes.TOOLTIP:
            case patternTypes.DRIVEN_ACTION:
            case spotlightTypes.NATIVE:
            case spotlightTypes.BANNER:
            case spotlightTypes.BUTTON:
            case experienceTypes.BANNER_v2:
                return width + "px";

            default:
                console.warn("DevError: unknown type", this.type);
        }
    }

    initProgressEvent(settingsNode) {
        const progressSettingsNode = settingsNode.querySelector("#progress-settings");
        const showProgressCheckbox = settingsNode.querySelector("#progress-checkbox");

        const progressionTheme = this.themeManager.getCurrentSettingValue([this.type, "progress_bar"]);

        const barProgressNode = this.userpilotSlide.querySelector("#userpilot-progressive-bar-progress") || Views.getViewElement("userpilot-progressive-bar-progress");
        const ballProgressNode = this.userpilotSlide.querySelector("#userpilot-progressive-ball-progress") || Views.getViewElement("userpilot-progressive-ball-progress");

        const addProgressNode = (type) => {
            barProgressNode.remove();
            ballProgressNode.remove();

            if (showProgressCheckbox.checked) {
                switch (type) {
                    case "bar":
                        barProgressNode.innerHTML = "";
                        for (let i = 0; i < this.groupStepsSum; i++) {
                            const span = document.createElement("span")
                            span.style.width = (100.0 / this.groupStepsSum) + "%";
                            if (i <= this.stepIndex) {
                                span.style.background = progressionTheme.color;
                                span.classList.add("selected");
                            }
                            barProgressNode.appendChild(span);

                        }
                        this.userpilotSlideContainer.appendChild(barProgressNode);
                        break;

                    case "ball":
                        ballProgressNode.innerHTML = "";
                        Array.from(Array(this.groupStepsSum))
                            .map((curr, i) => {
                                const span = document.createElement("span")
                                span.style.background = progressionTheme.color;
                                span.style.borderColor = progressionTheme.color;
                                if (i == this.stepIndex) {
                                    span.classList.add("selected");
                                    span.classList.add("userpilot-ball-selected");
                                }
                                return span;
                            })
                            .forEach(span => ballProgressNode.appendChild(span));
                        this.userpilotSlideContainer.appendChild(ballProgressNode);
                        break;

                    default:
                        console.error("unsupported progression type");
                }
            }
        }
        showProgressCheckbox.addEventListener("change", () => addProgressNode(progressionTheme.type));

        // Inner options
        const progressTypeSelect = progressSettingsNode.querySelector("#progression-type");
        const changeProgressionType = (select) => {
            const type = select.getAttribute("value");
            this.themeManager.setJsonSetting([this.type, "progress_bar", "type"], type);
            addProgressNode(type);
        }
        new SelectMenu(progressTypeSelect, null, changeProgressionType.bind(this), progressionTheme.type);

        // If applicable, add the progression element
        if (this.groupStepsSum > 1)
            addProgressNode(progressionTheme.type);
        else {
            progressSettingsNode.classList.add("inactive-click");
            showProgressCheckbox.checked = false;
        }

        this.initTippy(settingsNode);
    }

    initSkippableEvent(settingsNode) {
        const skippableSettingsNode = settingsNode.querySelector("#skippable-settings");
        const skippableCheckbox = skippableSettingsNode.querySelector("#skippable-checkbox");
        const colorTypeSelect = skippableSettingsNode.querySelector("#color-type");

        const generalTheme = this.themeManager.getCurrentSettingValue(["general"]);
        const buttonThemePath = this.getButtonsThemePath();
        const skippableSettings = this.themeManager.getCurrentSettingValue([...buttonThemePath, "close"]);
        const skippableTheme = this.themeManager.getCurrentSettingValue([...buttonThemePath, "close"]);
        const initialSkippableType = skippableSettings.type || SKIPPABLE_TYPES.X_BUTTON;

        const xButton = this.userpilotSlide.querySelector(".up-x-to-close") || Views.getViewElement("userpilot-close-button");
        const dismissButton = this.userpilotSlide.querySelector(".userpilot-dismiss-section") || Views.getViewElement("userpilot-dismiss-section");
        const addSkipIcon = () => {
            const skippableType = this.themeManager.getCurrentSettingValue([...buttonThemePath, "close", "type"]) || SKIPPABLE_TYPES.X_BUTTON;
            xButton.remove();
            dismissButton.remove();

            const firstTemplateSection = (this.type === experienceTypes.BANNER_v2) ? this.userpilotSlide : this.userpilotSlide.querySelector(".userpilot-builder-section");
            const color = (() => {
                if (skippableSettings.color_type == COLOR_TYPES.AUTOMATIC) {
                    if (skippableType === SKIPPABLE_TYPES.X_BUTTON) {
                        return CssOperations.autoColorAgainstElement(firstTemplateSection, "backgroundColor", CssOperations.hexToRgb(generalTheme.container.background_color, true));
                    } else return CssOperations.lightOrDark(CssOperations.hexToRgb(generalTheme.container.background_color, true));
                } else return skippableTheme.color;
            })();

            if (skippableCheckbox.checked == true) {
                if (skippableType === SKIPPABLE_TYPES.X_BUTTON) {
                    xButton.querySelector("path").style.fill = color;
                    this.userpilotSlideContainer.appendChild(xButton);
                } else if (skippableSettings.type == SKIPPABLE_TYPES.DISMISS) {
                    const userpilotBuilderEl = this.userpilotSlideContainer.querySelector(".userpilot-builder");

                    dismissButton.firstElementChild.style.color = color;
                    htmlTreeOperations.insertAfter(userpilotBuilderEl, dismissButton)
                }
            }
        }
        addSkipIcon();

        skippableCheckbox.addEventListener("change", () => {
            if (skippableCheckbox.checked == true) {
                addSkipIcon();
            } else {
                xButton.remove();
                dismissButton.remove();
            }
        });

        if (["tooltip", "driven", "hotspot", "native", "button"].includes(this.type)) {
            const dismissUiOption = skippableSettingsNode.querySelector("#dismiss-ui-options");
            dismissUiOption.style.display = "";

            const dismissUiOptions = Array.from(skippableSettingsNode.querySelectorAll("input[name='dismiss-ui-radio']"));
            dismissUiOptions.forEach(radio => {
                radio.addEventListener("change", () => {
                    if (radio.value == "xbutton") {
                        this.themeManager.setJsonSetting([...buttonThemePath, "close", "type"], SKIPPABLE_TYPES.X_BUTTON);
                    } else {
                        this.themeManager.setJsonSetting([...buttonThemePath, "close", "type"], SKIPPABLE_TYPES.DISMISS);
                    }
                    addSkipIcon();
                });
            });
            (initialSkippableType == SKIPPABLE_TYPES.X_BUTTON) ? dismissUiOptions.find(option => option.value == "xbutton").checked = true : dismissUiOptions.find(option => option.value == SKIPPABLE_TYPES.DISMISS).checked = true;
        }

        const manualColorPicker = skippableSettingsNode.querySelector(".manual-color-block");
        const changeColorType = (value, save = true) => {
            if (save) this.themeManager.setJsonSetting([...buttonThemePath, "close", "color_type"], value);
            switch (value) {
                case COLOR_TYPES.AUTOMATIC:
                    manualColorPicker.style.display = "none";
                    break;
                case COLOR_TYPES.MANUAL:
                    manualColorPicker.style.display = "";
                    break;
                default:
                    break;
            }
            addSkipIcon();
        }
        changeColorType(skippableSettings.color_type, false);

        new SelectMenu(colorTypeSelect, null, (select) => changeColorType(select.getAttribute("value")), skippableSettings.color_type);

        this.initSkippableBehaviorEvents(skippableSettingsNode, skippableSettings);
        this.initTippy(settingsNode);
    }

    initSkippableBehaviorEvents(skippableSettingsNode, skippableSettings) {
        const behaviorSelect = skippableSettingsNode.querySelector("#skippable-behavior");
        const additionalActionSelect = skippableSettingsNode.querySelector("#skippable-additional-action-menu");
        const skippableAdditionalActionSection = skippableSettingsNode.querySelector("#skippable-additional-action-settings");
        const additionalActionToggle = skippableSettingsNode.querySelector("#skippable-additional-action-checkbox");

        const isBanner = [spotlightTypes.BANNER, experienceTypes.BANNER_v2].includes(this.type);
        const isAdditionalAction = [
            FLOW_SKIPPABLE_ACTIONS.NEVER_SHOW_THIS_FLOW_AGAIN,
            FLOW_SKIPPABLE_ACTIONS.SHOW_THIS_FLOW_IN_THE_NEXT_SESSION,
            FLOW_SKIPPABLE_ACTIONS.DISCARD_CATEGORY,
        ].includes(skippableSettings.default_behavior);

        const showHideAdditionalActionSection = (behaviorValue) => skippableAdditionalActionSection.style.display = (behaviorValue == FLOW_SKIPPABLE_ACTIONS.DISMISS_FLOW) ? "" : "none";
        const handleBehaviorChange = ((value, firstRun = false) => {
            !firstRun && this.themeManager.setJsonSetting([...this.getButtonsThemePath(), "close", "default_behavior"], value);
            skippableSettings.default_behavior = value;
        });
        const handleMainBehaviorChange = (select) => {
            const value = select.getAttribute("value");
            handleBehaviorChange((!isBanner && value == FLOW_SKIPPABLE_ACTIONS.DISMISS_FLOW && additionalActionToggle.checked) ? additionalActionSelectMenu.selectedValue : value);
            showHideAdditionalActionSection(value);
        };
        const behaviorSelectMenu = new SelectMenu(behaviorSelect, null, handleMainBehaviorChange, (!isBanner && isAdditionalAction) ? FLOW_SKIPPABLE_ACTIONS.DISMISS_FLOW : skippableSettings.default_behavior);

        const additionalActionSelectMenu = new SelectMenu(additionalActionSelect, null, (select) => handleBehaviorChange(select.getAttribute("value")), isAdditionalAction ? skippableSettings.default_behavior : FLOW_SKIPPABLE_ACTIONS.NEVER_SHOW_THIS_FLOW_AGAIN);

        additionalActionToggle.addEventListener("change", () => handleBehaviorChange(additionalActionToggle.checked ? additionalActionSelectMenu.selectedValue : FLOW_SKIPPABLE_ACTIONS.DISMISS_FLOW));
        this.initToggleChangeEvent({
            input: additionalActionToggle,
            expandingSection: skippableAdditionalActionSection,
            action: "expand",
        });

        setTimeout(() => {
            if (isAdditionalAction) {
                additionalActionToggle.checked = 1;
                handleBehaviorChange(additionalActionSelectMenu.selectedValue, true);
                skippableAdditionalActionSection.classList.add("toggled");
                this.expandSection(skippableAdditionalActionSection);
            }
            showHideAdditionalActionSection(behaviorSelectMenu.selectedValue);
        });
    }

    initBackdropEvents(settingsNode) {
        const userpilotContainer = this.templateIframe.querySelector(".userpilot");
        const userpilotInnerContainer = this.templateIframe.querySelector(".inner-container");
        const backdropCheckbox = settingsNode.querySelector("#backdrop-checkbox");

        const backdropTheme = this.themeManager.getCurrentSettingValue([this.type, "backdrop"]);

        //if type is a slider and progressive interaction is enabled, set option as disabled
        if (this.oneStepHasProgressiveInteraction()) {
            this.disabledBackdropClicks(settingsNode);
        }

        this.initBackdropTippyEvents();

        const addBackdrop = () => {
            if (backdropCheckbox.checked) {
                const rgb = CssOperations.hexToRgb(backdropTheme.color);
                let rgba = "rgb(" + rgb.join() + ")";
                rgba = CssOperations.updateRgbaOpacity(rgba, backdropTheme.opacity / 100);
                // userpilotContainer.style.background = rgba;
                userpilotInnerContainer.style.background = rgba;
                this.disableProgressiveInteractionClicks();
            } else {
                this.enableProgressiveInteractionClicks();
                // userpilotContainer.style.background = "";
                userpilotInnerContainer.style.background = "";
            }
        }
        addBackdrop();
        backdropCheckbox.addEventListener("change", addBackdrop);
    }

    oneStepHasProgressiveInteraction() {
        return this.templateEditor.group.steps.some(item => item?.individual?.progressiveInteraction?.status == 1)
    }

    disabledBackdropClicks(settingsNode) {
        settingsNode.querySelector('#backdrop-settings').classList.add('inactive-click');
    }

    enableBackdropClicks(settingsNode) {
        settingsNode.querySelector('#backdrop-settings').classList.remove('inactive-click');
    }

    disableProgressiveInteractionClicks() {
        this.individualSettingsNode.querySelector('#progressive-interaction-settings').classList.add('inactive-click');
    }

    enableProgressiveInteractionClicks() {
        this.individualSettingsNode.querySelector('#progressive-interaction-settings').classList.remove('inactive-click');
    }

    initBackdropTippyEvents() {
        tippy(
            this.groupSettingsNode.querySelector("#backdrop-settings"),
            {
                appendTo: Common.shadowRoot,
                content: Views.getViewElement("tooltip-html", { textContent: `The interact to advance option is enabled for a </br>step in this group. Disable it to add a backdrop.` }),
                placement: 'right',
                offset: [0, 30],
                onShow: (instance) => {
                    return instance.reference.classList.contains('inactive-click');
                },
            }
        );
    }

    initBoxEvents(settingsNode) {
        const solidColorPickerEl = settingsNode.querySelector("#solid-border-settings .color-display");
        const shadowColorPickerEl = settingsNode.querySelector("#shadow-border-settings .color-display");

        const boxTheme = this.themeManager.getCurrentSettingValue([this.type, "box_border"]);

        // Set corner roundness of template
        this.userpilotSlide.style.borderRadius = boxTheme.corner_radius + "px";
        this.userpilotContent.style.borderRadius = boxTheme.corner_radius + "px";

        const renderBox = () => {
            (boxTheme.enabled) ? switchBorderShadowOptions(boxTheme.type) : removeBoxOutline();
        }

        const removeBoxOutline = () => {
            this.userpilotContent.style.boxShadow = "";
            this.userpilotSlide.style.border = "";
            this.userpilotSlide.style.borderWidth = "";

            const beacon = this.userpilotContent.querySelector(".userpilot-beacon");
            if (beacon) {
                beacon.style.boxShadow = "";
                beacon.style.border = "";
                beacon.style.borderWidth = "";
                beacon.style.boxShadow = "";
                beacon.style.display = "";
                beacon.style.zIndex = 0;
            }
        }

        const addBorderOrShadow = () => {
            const type = boxTheme.type;
            removeBoxOutline();

            if (type === "solid") {
                const borderWidth = boxTheme.width;
                this.userpilotSlide.style.border = "solid";
                this.userpilotSlide.style.borderWidth = borderWidth + "px";
                this.userpilotSlide.style.borderColor = boxTheme.color;

                this.changeColorPickerColor(solidColorPickerEl, boxTheme.color);
            } else if (type === "shadow") {
                const shadowWidth = boxTheme.width;
                const shadowIntensity = boxTheme.shadow_intensity;
                this.userpilotContent.style.boxShadow = boxTheme.color + " 0px 0px " + shadowIntensity + "px " + shadowWidth + "px";

                this.changeColorPickerColor(shadowColorPickerEl, boxTheme.color);
            }

            if (["tooltip", "driven", "native", "hotspot"].includes(this.type)) {
                this.manageBeaconBoxSettings();
            }
        }

        const solidBorderOptions = settingsNode.querySelector("#solid-border-settings");
        const shadowBorderOptions = settingsNode.querySelector("#shadow-border-settings");
        const switchBorderShadowOptions = (type) => {
            if (type == "solid") {
                shadowBorderOptions.style.display = "none";
                solidBorderOptions.style.display = "";
            } else if (type == "shadow") {
                solidBorderOptions.style.display = "none";
                shadowBorderOptions.style.display = "";
            }
            addBorderOrShadow();
        }

        const onBorderTypeChange = (selectedOption) => {
            const borderType = selectedOption.getAttribute("value");
            this.themeManager.setJsonSetting([this.type, "box_border", "type"], borderType);
            switchBorderShadowOptions(borderType);
        }
        const borderTypeSelect = settingsNode.querySelector("#border-type");
        new SelectMenu(borderTypeSelect, null, (select) => onBorderTypeChange(select), boxTheme.type);

        const boxBorderToggle = settingsNode.querySelector("#border-checkbox");
        boxBorderToggle.addEventListener("change", () => renderBox());

        renderBox();
    }

    changeColorPickerColor(containerEl, color) {
        const colorBox = containerEl.querySelector(".color-box");
        const colorText = containerEl.querySelector(".color-text");

        colorBox.style.background = color;
        colorText.textContent = color.toUpperCase();
    }

    getInputOrMinMaxValue(inputEl) {
        const value = Number(inputEl.value);

        if (inputEl.max && (value > inputEl.max)) return inputEl.max;
        if (inputEl.min && (value < inputEl.min)) return inputEl.min;

        return value;
    }

    isInputOutOfRange(inputEl) {
        const value = Number(inputEl.value);

        if (inputEl.max && (value > inputEl.max)) return true;
        if (inputEl.min && (value < inputEl.min)) return true;

        return false;
    }

    initPlacementSettingsEvents(settingsNode, changePlacementCallback) {
        const userpilotContent = this.userpilotContent;
        let currentDirection = this.themeManager.getCurrentSettingValue([this.type, "default_position"]);

        const bannerDisplay = this.type == spotlightTypes.BANNER ? this.themeManager.getCurrentSettingValue(["individual", "behavior", "display", "position"]) : null;

        if (bannerDisplay === SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS.INLINE) {
            currentDirection = "top"
        }

        const slidePositionSelect = settingsNode.querySelector("#slide-position-select");
        const slidePositionBoxMenu = settingsNode.querySelector("#slide-position-boxes");
        let activeOption = null;
        if ([BANNER_v2_PLACEMENT.BEFORE, BANNER_v2_PLACEMENT.AFTER, BANNER_v2_PLACEMENT.CONTAINER_BOTTOM, BANNER_v2_PLACEMENT.CONTAINER_TOP].includes(currentDirection)) {
            slidePositionBoxMenu.style.display = "none";
            settingsNode.querySelector(".banner-position-element").style.display = "";
        } else {
            activeOption = slidePositionBoxMenu.querySelector("div[value='" + currentDirection + "']");
            activeOption?.classList.add("active");
        }


        const changeSlidePosition = (option, firstRun = false) => {
            const direction = option.getAttribute("value");
            const bannerDisplay = this.type == spotlightTypes.BANNER ? this.themeManager.getCurrentSettingValue(["individual", "behavior", "display", "position"]) : null;

            if (bannerDisplay === SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS.INLINE) {
                currentDirection = "top"
            } else {
                !firstRun && this.themeManager.setJsonSetting([this.type, "default_position"], direction);
                currentDirection = direction;
            }

            userpilotContent.setAttribute("dir", direction);
            if (this.type === patternTypes.SLIDEOUT) {
                this.moveSettingsOppositeToSlideout();
                this.handleTemplateToolbarPosition();
            }

            changePlacementCallback?.();

            if ([spotlightTypes.BANNER, experienceTypes.BANNER_v2].includes(this.type)) {
                this.reInitBannerPlaceholder();
            }
        }

        const positionSelectMenu = new SelectMenu(slidePositionSelect, null, (select) => {
            changeSlidePosition(select);
            // handle before/after new positions for banner v2
            activeOption?.classList.remove("active");
            activeOption = slidePositionBoxMenu.querySelector("div[value='" + currentDirection + "']");
            activeOption?.classList.add("active");
        }, currentDirection);

        const handlePositionBoxChange = (optionEl, firstRun = false) => {
            changeSlidePosition(optionEl, firstRun);
            // handle before/after new positions for banner v2
            activeOption?.classList.remove("active");
            activeOption = optionEl;
            activeOption?.classList.add("active");
            positionSelectMenu.setValue(currentDirection);
        }

        Array.from(slidePositionBoxMenu.querySelectorAll("div.clickable")).forEach(option => {
            option.addEventListener("click", (event) => handlePositionBoxChange(option));
            option.addEventListener("set-banner-position-setting", (event) => handlePositionBoxChange(option, event.detail.firstRun));
        });

        userpilotContent.setAttribute("dir", currentDirection);
        if (this.type === patternTypes.SLIDEOUT) currentDirection.includes("-left") ? this.positionSettingsToRight() : this.positionSettingsToLeft();
    }

    initFrequencyEvents(settingsNode) {
        const displaySelect = settingsNode.querySelector("#display-select");
        const displayValue = this.themeManager.getCurrentSettingValue(["individual", "frequency", "display"]) || "1";
        const displayOnchange = async (select) => {
            const value = select.getAttribute("value");
            this.themeManager.setJsonSetting(["individual", "frequency", "display"], value);
        }
        new SelectMenu(displaySelect, null, displayOnchange, displayValue);
    }

    isUserpilotContentInDom = () => this.iframeDocument.contains(this.userpilotContent);

    initTippy = (settingsNode) => {
        tippy((settingsNode || this.containerNode).querySelectorAll("[label-on-hover]"), {
            appendTo: Common.shadowRoot,
            content: (el) => {
                const label = el.getAttribute('label-on-hover');
                el.removeAttribute('label-on-hover');

                return ExperienceBuilderViews.getViewElement("tippy-tooltip", { id: "", classes: "template-settings-tooltip", content: label });
            }
        });
    }

    getButtonsThemePath = () => {
        if([patternTypes.MODAL, patternTypes.SLIDEOUT].includes(this.type)) return [this.type, "buttons"];

        return [this.type];
    }

    // Spotlight
    static setPlaceholderDirection(parent, placeholder, direction) {
        switch (direction) {
            case SPOTLIGHT_BEHAVIOR_DISPLAY_DIRECTION.LEFT:
                parent.prepend(placeholder);
                break;
            case SPOTLIGHT_BEHAVIOR_DISPLAY_DIRECTION.RIGHT:
                parent.appendChild(placeholder);
                break;
        }
    }

    static createPlaceHolder = ({ target, position, displayDirection, width, height }) => {
        !height && (width = height)
        target.querySelector("userpilotinlineelement")?.remove();
        const placeHolderElement = ExperienceBuilderViews.getViewElement("userpilot-inline-placeholder", {
            width: width,
            height: height,
            marginLeft: 0,
            marginTop: 0,
            position: position,
        });
        TemplateSettings.setPlaceholderDirection(target, placeHolderElement, displayDirection);

        return placeHolderElement;
    }

    static getScaledValue = (value, animation) => {
        if (![iconAnimationTypes.default].includes(animation)) {
            const scaledValue = value * 1.8;
            return scaledValue > 32 ? scaledValue : 32;
        } else {
            const scaledValue = value * 1.1;
            return scaledValue > 18 ? scaledValue : 18
        }
    }

    static calculateInlinePosition = (event, boundingRect, scaledValue) => {
        return {
            top: event.clientY - boundingRect.top - boundingRect.height / 2 + scaledValue.height / 2,
            left: event.clientX - boundingRect.left - boundingRect.width / 2 + scaledValue.width / 2
        }
    }
}