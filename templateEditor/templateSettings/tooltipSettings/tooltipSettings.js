import TooltipsSharedSettings from './tooltipsSharedSettings.js';
import Views from "../../views/templateViews.js";

export default class TooltipSettings extends TooltipsSharedSettings {
    constructor(targetElement, templateEditor) {
        super("tooltip", targetElement, templateEditor);
        this.init();
    }

    init() {
        this.initIndividualSettings();
        this.switchIndividualGroupSettings("individual");

        this.initTippy();
    }

    initIndividualSettings() {
        this.individualSettingsNode = Views.getViewElement("tooltip-individual-settings");
        this.initSharedIndividualSettings(this.individualSettingsNode);
        this.initNextEvents(this.individualSettingsNode);
        this.initBackEvents(this.individualSettingsNode);
        this.handleTemplateReposition();
    }

    initGroupSettingsEvents(settingsNode) {
        super.initGroupSettingsEvents(settingsNode);
        
        this.initElementOverlayEvents(settingsNode);
    }

    initElementOverlayEvents(settingsNode) {
        const elementOverlayToggle = settingsNode.querySelector("#backdrop-settings #element-overlay");
        
        elementOverlayToggle.addEventListener('change', (event) => {
            this.themeManager.setJsonSetting([this.type, "backdrop", "overlay"], event.target.checked);
        });
    }

    positionTooltip(target) {
        this.positionElement(target);
    }
}
