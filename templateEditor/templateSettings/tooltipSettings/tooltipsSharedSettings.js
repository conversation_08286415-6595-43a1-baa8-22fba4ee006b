import _ from 'lodash';

import TemplateSettings from "../templateSettings.js";
import Views from "../../views/templateViews.js";
import Element from "../../element/element.js";
import Positioning from "../../element/positioning.js";
import JsonOperations from "../../../generic-utils/jsonOperations.js";
import SelectMenu from "../../../generic-components/selectMenu.js";
import CssOperations from "../../../generic-utils/cssOperations.js";
import ElementPicker from "../../elementPicker.js";
import Common from "../../../index.js";
import TargetElementManager from './targetElementManager.js';
import Find from '../../element/find.js';
import htmlTreeOperations from "../../../generic-utils/htmlTreeOperations";
import SelectButton from '../../../generic-components/selectButton.js';

// Store
import Actions from "../../../store/constants/actions.js";
import { Store } from "../../../store/store.js";

// Constants
import { spotlightTypes, spotlightTypesArray } from '../../../store/constants/experiences/spotlightTypes';
import { patternTypes } from '../../../store/constants/experiences/uiPatternTypes.js';
import { experienceTypes, TOOLTIP_BEACON_TYPES } from '../../../store/constants/experiences/constants.js';

export default class TooltipsSharedSettings extends TemplateSettings {
    constructor(type, targetElement, templateEditor) {
        super(templateEditor, type);
        this.targetElement = targetElement;
        this.targetElementCoords = Element.getBoundingClientRect(targetElement, null, null, htmlTreeOperations.getBodyZoom());
        this.virtualTarget = this.iframeUserpilotContainer.querySelector("#virtual-target");
        this.backdropFocusDefaultPadding = { x: 10, y: 10 };

        this.individualSettingsNode;
        this.groupSettingsNode;
        if (!spotlightTypesArray.includes(type)) {
            this.initGroupSettingsNode();
            this.initLogicSettingsNode();
        }

        let timeoutObserver;
        const tooltipObserver = new MutationObserver((event) => {
            clearTimeout(timeoutObserver);
            timeoutObserver = setTimeout(() => {
                const isButton = this.type == spotlightTypes.BUTTON;
                const isHotSpot = this.type == spotlightTypes.HOTSPOT;

                const isInvalidTarget = this.userpilotContent.classList.contains("invalid");
                const target = isInvalidTarget ? this.virtualTarget : this.targetElement;
                if(!isButton && !isHotSpot) this.setTemplateDirection(target);
            }, 300);
        });
        const observerConfig = { childList: true, subtree: true, characterData: true, attributeFilter : ["template-width"] };
        setTimeout(() => {
            tooltipObserver.observe(this.userpilotContent, observerConfig);
            tooltipObserver.observe(this.userpilotSlideContainer, { attributes: true });
        }); 
        window.addEventListener("resize", this.handleTooltipSharedSettingsWindowResize);
        this.placeSettingsSideBar()
    }

    handleTooltipSharedSettingsWindowResize = () => {
        if(!this.isUserpilotContentInDom()) return window.removeEventListener("resize", this.handleTooltipSharedSettingsWindowResize);
        this.setTemplateDirection();
    }

    placeSettingsSideBar(target = this.targetElement) {
        const viewport = target.getBoundingClientRect();
        /**
         * in some case, viewport.right returns the wrong value.
         */
        const direction = viewport.left > (window.innerWidth - viewport.left - viewport.width) ? "right" : "left" 
        direction == "left" ? this.positionSettingsToRight() : this.positionSettingsToLeft();
    }

    positionElement(target) {
        const currentTargetElement = target ? target : this.virtualTarget;

        (target) ? this.userpilotContent.classList.remove("invalid") : this.userpilotContent.classList.add("invalid");

        this.setTemplateDirection(currentTargetElement);
    }

    setTemplateDirection(target = this.targetElement) {
        const isBadge = this.type === spotlightTypes.NATIVE && parseInt(this.themeManager.getCurrentSettingValue(["individual", "behavior", "beacon"]));
        const isHotSpot = this.type === spotlightTypes.HOTSPOT;
        const isButton = this.type == spotlightTypes.BUTTON;
        const bodyZoom = isBadge || isButton || isHotSpot ? 1 : htmlTreeOperations.getBodyZoom();
        setTimeout(() => {
            this.targetElementCoords = Element.getBoundingClientRect(target, null, null, bodyZoom);
            Positioning.position({
                tooltip: this.userpilotContent,
                target: target,
                targetCoords: this.targetElementCoords,
                direction: this.themeManager.getCurrentSettingValue(["individual", "placement", "direction"]),
                bodyZoom: htmlTreeOperations.getBodyZoom(),
                includeIframePosition: !(isBadge || isButton || isHotSpot),
            });
            this.handleTemplateToolbarPosition();
            this.setBackdropFocus();
        });
    }

    initSharedIndividualSettings(settingsNode) {
        this.initGenericSettingEvents(settingsNode);
        this.initArrowExpandEvents(settingsNode);

        this.initElementSettings(settingsNode.querySelector("#element-settings"));
        this.initUiPatternSelectEvent(this.containerNode);
        this.initTemplateSizeEvent(settingsNode);
        this.initPlacementEvents(settingsNode.querySelector("#placement-settings"), true);
        this.initBehaviorEvents(settingsNode.querySelector("#behavior-settings"));
        this.initBackdropFocusEvents(settingsNode.querySelector("#backdrop-focus-settings"));
    }

    findTargetCallback = (target) => {
        this.positionTooltip(target);
    }

    initGroupSettingsNode() {
        const templateName = (this.type === spotlightTypes.NATIVE) ? "native-tooltip-grp-settings" : "tooltip-grp-settings";
        
        this.groupSettingsNode = Views.getViewElement(templateName);
        this.groupSettingsNode.insertBefore(this.themeManager.themeSettingsNode, this.groupSettingsNode.firstElementChild)
        this.initGroupSettingsEvents(this.groupSettingsNode);
    }

    initLogicSettingsNode() {
        this.logicSettingsNode = Views.getViewElement("logic-settings");
        this.LogicSettings.initLogicSettingsNodeEvents(this.logicSettingsNode);
    }

    initGroupSettingsEvents(settingsNode) {
        this.initGenericSettingEvents(settingsNode);
        this.initArrowExpandEvents(settingsNode);

        this.initProgressEvent(settingsNode);
        this.initSkippableEvent(settingsNode);
        this.initBackdropEvents(settingsNode);
        this.initBeaconEvents(settingsNode);
        this.initBoxEvents(settingsNode);
    }

    resetBackdropFocusSettings(backdropCheckbox) {
        if(!backdropCheckbox) return;

        const backdropFocusSettingsNode = this.individualSettingsNode.querySelector("#backdrop-focus-settings")
        backdropCheckbox.checked ? backdropFocusSettingsNode.classList.remove("inactive") : backdropFocusSettingsNode.classList.add("inactive");
    }

    reinitGroupSettings() {
        this.initGroupSettingsNode();
        this.appendGroupSettings();
        this.resetBackdropFocusSettings(this.groupSettingsNode.querySelector("#backdrop-checkbox"));
    }

    reinitIndividualSettings() {
        this.initIndividualSettings();
        this.appendIndividualSettings();
    }

    getTargetElementSettings = () => ({
        templateEditor: this.templateEditor,
        autoDetectionNodeEl: this.individualSettingsNode.querySelector("#element-settings"),
        autoDetectMenuContainer: null,
    })

    setAutoTargetElementSettings = (value) => {
        this.stepSettings.ea = value;
    }

    setManualTargetElementSettings = (rootField, field, value) => {
        if(!field) this.themeManager.setJsonSetting(["individual", rootField], value);
        else this.themeManager.setJsonSetting(["individual", rootField, field], value);
    }

    getAutoTargetElementSettings = () => {
        return this.stepSettings.ea;
    }

    getManualTargetElementSettings = (rootField, field) => {
        return (field) ? this.themeManager.getCurrentSettingValue(["individual", rootField, field]) : this.themeManager.getCurrentSettingValue(["individual", rootField]);
    }

    initElementSettings = (settingsNode) => {
        this.elementDetectionManager = new TargetElementManager(this.getTargetElementSettings(), settingsNode, {
            setManualTargetElementSettings: (field, value) => this.setManualTargetElementSettings("detection", field, value),
            setAutoTargetElementSettings: this.setAutoTargetElementSettings,
            findTargetCallback: this.findTargetCallback,
            getAutoTargetElementSettings: this.getAutoTargetElementSettings,
            getManualTargetElementSettings: (field) => this.getManualTargetElementSettings("detection", field),
            setTargetElementCallback: (target) =>  this.targetElement = target,
            getTargetElementCallback: () =>  this.targetElement,
            reInitPlacementSliders: () => this.reInitPlacementSliders
        });

        this.initReselectElementEvents(settingsNode);
    }

    initReselectElementEvents = (settingsNode) => {
        const reselectElementButton = settingsNode.querySelector("#reselect-tooltip-target");
        reselectElementButton.addEventListener("click", () => this.selectNewTarget(this.stepSettings, "ea", this.retarget));
    }

    initUiPatternSelectEvent(settingsNode) {
        const uiType = this.stepSettings.type;
        const uiTypeSelect = settingsNode.querySelector("#tooltip-type-select");

        const changeUiPattern = async (select) => {
            const value = select.getAttribute("value");
            JsonOperations.setObjectValue(this.stepSettings, ["type"], value);
            this.switchTypeSettings(value, this.groupSettings);
            this.revertProgressionButtons(value)
            await Store.publishAction(Actions.SPLIT_GROUP_AT_STEP, { step: this.stepSettings });
            this.templateEditor.refresh();
        }
        new SelectMenu(uiTypeSelect, null, changeUiPattern.bind(this), uiType);
    }

    switchTypeSettings(type, newGroup) {
        const individualSettings = this.themeManager.getCurrentSettingValue(["individual"]);

        if (type == "driven") {
            individualSettings.driven_action_type = "1";
            individualSettings.exp_cont_element = "";
            individualSettings.exp_cont_type = "1";
            individualSettings.exp_cont_detection = {};
        }
        else if (type == "tooltip") {
            delete individualSettings.driven_action_type;
            delete individualSettings.exp_cont_element;
            delete individualSettings.exp_cont_type;
            delete individualSettings.exp_cont_detection;
        }
        else console.error("Unsupported type " + type);

        if (newGroup?.settings?.[this.type]) {
            newGroup.settings[type] = newGroup.settings[this.type];

            if(type === patternTypes.TOOLTIP && newGroup.settings[type].beacon?.type == '5') delete newGroup.settings[type].beacon.type;
            if(type === patternTypes.DRIVEN_ACTION && newGroup.settings[type].backdrop?.overlay) delete newGroup.settings[type].backdrop.overlay;

            delete newGroup.settings[this.type];
        }

        if (newGroup?.color?.[this.type]) {
            newGroup.color[type] = newGroup.color[this.type];
            delete newGroup.color[this.type];
        }
    }

    revertProgressionButtons(type) {
        Array.from(this.userpilotSlide.querySelectorAll("#userpilot-next-button, #userpilot-back-button"))
        .forEach(button => (
            button.parentElement.classList.contains("userpilot-cols-section")
            ? button.parentElement.style.display = type == "driven" ? "none" : ""
            : button.style.display = type == "driven" ? "none" : ""
        ));
        this.templateEditor.saveStepContent();
    }

    reInitPlacementSliders(arg, firstRun = false) {
        const {left, top, marginTop, marginLeft} = arg || {top: 30, left: 30, marginTop: 0, marginLeft: 0}

        let elements;
        switch (this.type) {
            case spotlightTypes.BUTTON: 
                elements = ".userpilot-slide";
                break;
            case spotlightTypes.HOTSPOT: 
                elements = "#userpilot-pulse";
                break;
            case spotlightTypes.NATIVE:
                elements = "#content-container, #badge-element";
                break;
            case patternTypes.DRIVEN_ACTION:
                elements = "#content-container, .driven-action-hotspot #userpilot-pulse"
                break;
            default:
                elements = "#content-container"
                break;
        }

        const leftSlider = Views.getView("settings-slider", {
            elements: elements,
            jsonSetting: "individual-placement-margin-left",
            property: "marginLeft",
            title: "Left",
            max: left,
            min: -left,
            step: 0,
            sliderIcon: "",
            inputUnit: "px"
        });

        const topSlider = Views.getView("settings-slider", {
            elements: elements,
            jsonSetting: "individual-placement-margin-top",
            property: "marginTop",
            title: "Top",
            max: top,
            min: -top,
            step: 0,
            sliderIcon: "",
            inputUnit: "px"
        });

        (!firstRun || Common.ELEMENT_PICKER_EVENT_REF) && this.themeManager.setJsonSetting(["individual", "placement", "margin"], {top: marginTop, left: marginLeft});

        const newNode = Views.getViewElement("placement-setting", {leftSlider: leftSlider, topSlider: topSlider});
        this.reinitNodeSettings("placement-settings", newNode, this.initPlacementEvents.bind(this), firstRun);

        const sliders = Array.from(this.individualSettingsNode.querySelectorAll(".settings-slider"));
        sliders.forEach(slider => {
            this.initSliderEvent(slider);
            !firstRun && slider.querySelector("input[type='number']").dispatchEvent(new Event("input"));
        });
        this.initRawInputsEvents(newNode);
    }

    initPlacementEvents(settingsNode, firstRun = false) {
        const defaultPlacementCheckbox = settingsNode.querySelector("#default-placement");
        const positionNodes = Array.from(settingsNode.querySelectorAll(".position-node"));
        const positionTextNode = settingsNode.querySelector("#position-text");

        const placementSettings = this.themeManager.getCurrentSettingValue(["individual", "placement"]);

        let selectedPositionNode;
        const setPlacement = (value, firstRun = false) => {

            !firstRun && this.themeManager.setJsonSetting(["individual", "placement", "direction"], value);
            if (selectedPositionNode) selectedPositionNode.classList.remove("selected");
            if (value == "auto") {
                defaultPlacementCheckbox.checked = true;
                selectedPositionNode = defaultPlacementCheckbox;
                positionTextNode.textContent = "default position";
            }
            else {
                defaultPlacementCheckbox.checked = false;
                selectedPositionNode = positionNodes.find(node => node.getAttribute("value") == value);
                selectedPositionNode.classList.add("selected");
                positionTextNode.textContent = selectedPositionNode.getAttribute("value").split("-").join(" ");
            }
        }
        setPlacement(placementSettings.direction, firstRun);

        positionNodes.forEach(node => {
            node.addEventListener("click", () => {
                setPlacement(node.getAttribute("value"));
                this.positionTooltip(this.targetElement);
            });
        });

        defaultPlacementCheckbox.addEventListener("change", () => {
            defaultPlacementCheckbox.checked ? setPlacement("auto") : setPlacement("right-center");
            this.positionTooltip(this.targetElement);
        });
        const container = this.type == spotlightTypes.BUTTON ? this.userpilotSlide : this.userpilotContent;

        if (![spotlightTypes.HOTSPOT].includes(this.type) && placementSettings?.margin?.left) {
            container.style.marginLeft = placementSettings.margin.left + 'px';
        }
        if (![spotlightTypes.HOTSPOT].includes(this.type) && placementSettings?.margin?.top) {
            container.style.marginTop = placementSettings.margin.top + 'px';
        }

        this.virtualTarget.addEventListener("click",  () => this.selectNewTarget(this.stepSettings, "ea", this.retarget));

        if([spotlightTypes.HOTSPOT, spotlightTypes.BUTTON].includes(this.type)) {
            settingsNode.querySelector("#manual-placement").classList.add("display-none");
            settingsNode.querySelector("#default-placement-container").classList.add("display-none");
        }
    }

    retarget = (event) => {
        const isBadge = this.type === spotlightTypes.NATIVE && parseInt(this.themeManager.getCurrentSettingValue(["individual", "behavior", "beacon"]));
        const isHotSpot = this.type === spotlightTypes.HOTSPOT;
        const isButton = this.type === spotlightTypes.BUTTON;
        const bodyZoom = (isBadge || isButton || isHotSpot) ? 1 : htmlTreeOperations.getBodyZoom();
        const target = Find.lookup({ manual: 0, data: this.stepSettings.ea });
        const targetCoords = target.getBoundingClientRect();
        const newPosition = {
            width: targetCoords.width,
            height: targetCoords.height,
            left: (event.clientX * bodyZoom) - targetCoords.left,
            top: (event.clientY * bodyZoom) - targetCoords.top,
        }
        this.themeManager.setJsonSetting(["individual", "detection"], {});
        this.themeManager.setJsonSetting(["individual", "placement", "position"], newPosition);

        this.elementDetectionManager.resetManualDetectionTextIncludeFields();
        this.elementDetectionManager.findTarget();
        this.elementDetectionManager.initAutoDetectionEvents();
        this.elementDetectionManager.setDetectionType("auto");
        this.placeSettingsSideBar(this.targetElement);
        if(this.type === patternTypes.DRIVEN_ACTION) this.reinitNodeSettings("action-settings", Views.getViewElement("driven-action-type"), this.initDrivenActionTypeEvents);
    }

    initBehaviorEvents(settingsNode) {
        const behaviorSelect = settingsNode.querySelector("#tooltip-behavior");
        const behaviorType = this.themeManager.getCurrentSettingValue(["individual", "trig_type"]);

        const changeTooltipBehavior = (select) => {
            const value = select.getAttribute("value");
            this.themeManager.setJsonSetting(["individual", "trig_type"], value);
        }
        new SelectMenu(behaviorSelect, null, changeTooltipBehavior, behaviorType || "1");
    }

    initBackdropFocusEvents(backdropFocusSettingsNode) {
        const focusTypeSelect = backdropFocusSettingsNode.querySelector("#focus-type");
        const borderRadiusSlider = backdropFocusSettingsNode.querySelector(".settings-slider[json-setting='individual-highlighted-corners']");
        const backdropHeightSlider = backdropFocusSettingsNode.querySelector(".settings-slider[json-setting='individual-highlighted-height']");
        const individualSettings = this.themeManager.getCurrentSettingValue(["individual"]);

        const changeBackdropType = (select) => {
            const value = select.getAttribute("value");
            this.themeManager.setJsonSetting(["individual", "backdropfocus"], value);
            (value == "1") ? (borderRadiusSlider.style.display = "", backdropHeightSlider.style.display = "") : (borderRadiusSlider.style.display = "none", backdropHeightSlider.style.display = "none");
            this.setBackdropFocus();
        }
        (individualSettings.backdropfocus == "1") ? (borderRadiusSlider.style.display = "", backdropHeightSlider.style.display = "") : (borderRadiusSlider.style.display = "none", backdropHeightSlider.style.display = "none");
        new SelectMenu(focusTypeSelect, null, changeBackdropType, individualSettings.backdropfocus);


        const backdropSettings = this.themeManager.getCurrentSettingValue([this.type, "backdrop"]);
        if(!backdropSettings.enabled) {
            backdropFocusSettingsNode.classList.add("inactive");
        }
    }

    setBackdropFocus() {
        const bodyZoom = htmlTreeOperations.getBodyZoom();
        const backdropWrapper = this.iframeDocument.getElementById("userpilot-backdrop-focus");
        if(!backdropWrapper) return;
        const viewPort = this.targetElement.getBoundingClientRect();
        const targetWidth = (viewPort.width || this.targetElement.offsetWidth) * bodyZoom;
        const targetHeight = (viewPort.height || this.targetElement.offsetHeight) * bodyZoom;

        const backdropTheme = this.themeManager.getCurrentSettingValue([this.type, "backdrop"]);
        const individualSettings = this.themeManager.getCurrentSettingValue(["individual"]);
        const backdropFocusSettings = this.themeManager.getCurrentSettingValue(["individual", "highlighted"]);
        const targetElementCoords = _.cloneDeep(this.targetElementCoords);
        backdropWrapper.style = "";

        let rgba = "rgb(" + CssOperations.hexToRgb(backdropTheme.color).join() + ")";
        rgba = CssOperations.updateRgbaOpacity(rgba, backdropTheme.opacity / 100);
        
        if (individualSettings.backdropfocus == "1") {
            backdropWrapper.classList.remove("soft");
            backdropWrapper.classList.add("hard");

            backdropWrapper.style.left = targetElementCoords.left - this.backdropFocusDefaultPadding.x / 2 - parseFloat(backdropFocusSettings.width) + Positioning.iframePositioning.left;
            backdropWrapper.style.top = targetElementCoords.bounding.top - this.backdropFocusDefaultPadding.y / 2 - parseFloat(backdropFocusSettings.height) + Positioning.iframePositioning.top;
            backdropWrapper.style.paddingLeft = (parseFloat(targetWidth) + this.backdropFocusDefaultPadding.x + parseFloat(backdropFocusSettings.width)*2) + "px";
            backdropWrapper.style.paddingTop = (parseFloat(targetHeight) + this.backdropFocusDefaultPadding.y + parseFloat(backdropFocusSettings.height)*2) + "px";
            backdropWrapper.style.borderRadius = (10000 + Number(backdropFocusSettings.corners)) + "px";
            backdropWrapper.style.borderColor = rgba;
            targetElementCoords.backdrop = {width: parseFloat(backdropFocusSettings.width), height: parseFloat(backdropFocusSettings.height)}
        }
        else {
            backdropWrapper.classList.add("soft");
            backdropWrapper.classList.remove("hard");
            const left = targetElementCoords.left + (targetWidth * bodyZoom) / 2;
            const top = targetElementCoords.bounding.top + (targetHeight * bodyZoom) / 2;
            const width = (parseFloat(targetWidth) + this.backdropFocusDefaultPadding.x + parseFloat(backdropFocusSettings.width)) + "px";
            backdropWrapper.style.background = "-webkit-radial-gradient(" + (left+Positioning.iframePositioning.left) + "px " + (top+Positioning.iframePositioning.top) + "px, " + width + " " + width + ", transparent 11%, " + rgba + ")";
            targetElementCoords.backdrop = {width: parseFloat(backdropFocusSettings.width), height: parseFloat(backdropFocusSettings.width)}
        }

        Positioning.position({
            tooltip: this.userpilotContent,
            target: this.targetElement,
            targetCoords: targetElementCoords,
            direction: this.themeManager.getCurrentSettingValue(["individual", "placement", "direction"]),
            bodyZoom: htmlTreeOperations.getBodyZoom()
        });
    }


    initBackdropEvents(settingsNode) {
        const backdropSettingsNode = settingsNode.querySelector("#backdrop-settings");
        const container = this.templateIframe.querySelector(".userpilot");
        const backdropCheckbox = backdropSettingsNode.querySelector("#backdrop-checkbox");

        let backdropWrapper = container.querySelector("#userpilot-backdrop-focus");
        if(!backdropWrapper) {
            const backdropContainer = document.createElement("div");
            backdropContainer.classList.add("backdrop-container");
            
            backdropWrapper = document.createElement("div");
            backdropWrapper.id = "userpilot-backdrop-focus";

            backdropContainer.appendChild(backdropWrapper);
            container.appendChild(backdropContainer);
        }

        if (container.style.background) {
            container.style.background = "";
        }

        const addBackdrop = () => {
            if(backdropCheckbox.checked) {
                backdropWrapper.classList.add("active");
                this.setBackdropFocus();
            } else {
                backdropWrapper.classList.remove("active");
            }
        }
        addBackdrop();

        backdropCheckbox.addEventListener("change", () => {
            this.resetBackdropFocusSettings(backdropCheckbox);
        });

        backdropCheckbox.addEventListener("change", addBackdrop);
    }

    initBeaconType(value = TOOLTIP_BEACON_TYPES.ARROW, firstRun = false)  {
        const animationContainer = this.groupSettingsNode.querySelector(".animation-type-container");
        const beaconTheme = this.themeManager.getCurrentSettingValue([this.getRootFieldKey(), "beacon"]);
        
        const beaconColor = beaconTheme?.color 
            || this.themeManager.getCurrentSettingValue([this.getRootFieldKey(), "background_color"]);

        const driven_action_type = this.themeManager.getCurrentSettingValue(["individual", "driven_action_type"]);
        const isDrivenActionTypeHotspot = (driven_action_type == "5");
        const isDrivenAction = (this.type === patternTypes.DRIVEN_ACTION);
        const isPulseBeacon = value === TOOLTIP_BEACON_TYPES.PULSE;

        const normalBeacon = Views.getViewElement("userpilot-beacon", { beaconID: "beacon-arrow", type: TOOLTIP_BEACON_TYPES.ARROW, classes: "" });
        const lineBeacon = Views.getViewElement("userpilot-beacon", { beaconID: "beacon-line", type: TOOLTIP_BEACON_TYPES.LINE, classes: "userpilot-beacon-line" });
        const ballBeacon = Views.getViewElement("userpilot-beacon", { beaconID: "beacon-line", type: TOOLTIP_BEACON_TYPES.BALL, classes: "userpilot-beacon-line" });
        const pulseBeacon = this.iframeUserpilotContainer.querySelector("#userpilot-pulse") || Views.getViewElement("userpilot-pulse");
        const cursor = this.iframeUserpilotContainer.querySelector("#userpilot-cursor-preview") || Views.getViewElement("userpilot-cursor-preview");

        let currentBeacon = this.userpilotContent.querySelector(".userpilot-beacon");
        !firstRun && this.themeManager.setJsonSetting([this.getRootFieldKey(), "beacon", "icon"], value);

        if(isDrivenAction && isDrivenActionTypeHotspot) {
            animationContainer && animationContainer.classList.remove('hidden');
            pulseBeacon.setAttribute("animation", beaconTheme.animation_type);
            pulseBeacon.classList.add('flow-tooltip');
            Array.from(pulseBeacon.children).forEach(el => {
                el.style.color = beaconColor
                el.style.background = beaconColor
                el.style.borderColor = beaconColor
            } );
            return;
        }

        (isPulseBeacon) ? animationContainer?.classList.remove('hidden') : animationContainer?.classList.add('hidden');
        

        if (currentBeacon) currentBeacon.remove();
        if (pulseBeacon) pulseBeacon.remove();
        if (cursor) cursor.remove();
        currentBeacon?.classList.remove("userpilot-beacon-line");
        currentBeacon?.removeAttribute("beacon-ball");
        
        if(!beaconTheme.enabled) return;
        switch (value) {
            case TOOLTIP_BEACON_TYPES.ARROW:
                currentBeacon = normalBeacon;
                currentBeacon.setAttribute("type", TOOLTIP_BEACON_TYPES.ARROW);
                break;
            case TOOLTIP_BEACON_TYPES.LINE:
                currentBeacon = lineBeacon;
                currentBeacon.classList.add("userpilot-beacon-line");
                break;
            case TOOLTIP_BEACON_TYPES.BALL:
                currentBeacon = ballBeacon;
                currentBeacon.classList.add("userpilot-beacon-line");
                currentBeacon.setAttribute("beacon-ball", 1);
                break;
            case TOOLTIP_BEACON_TYPES.PULSE:
                currentBeacon = normalBeacon;
                currentBeacon.setAttribute("type", TOOLTIP_BEACON_TYPES.PULSE);
                pulseBeacon.setAttribute("animation", beaconTheme.animation_type);
                pulseBeacon.classList.add('flow-tooltip');
                Array.from(pulseBeacon.children).forEach(el => {
                    el.style.color = beaconColor
                    el.style.background = beaconColor
                    el.style.borderColor = beaconColor
                } );
                this.iframeUserpilotContainer.appendChild(pulseBeacon);
                break;
            case TOOLTIP_BEACON_TYPES.MOVING_CURSOR:
                currentBeacon = normalBeacon;
                currentBeacon.setAttribute("type", TOOLTIP_BEACON_TYPES.ARROW);
                this.setPointerTarget();
                break;
            default:
                console.error("Unsupported type of beacon: " + value);
                break;
        }
        currentBeacon.style.background = beaconColor;
        this.userpilotContent.appendChild(currentBeacon);
        this.manageBeaconBoxSettings();
        /**
         * re-position tooltip the moment the caret type of status change
         */
        Positioning.position({
            tooltip: this.userpilotContent,
            target: this.targetElement,
            targetCoords: this.targetElementCoords,
            direction: this.themeManager.getCurrentSettingValue(["individual", "placement", "direction"]),
            bodyZoom: htmlTreeOperations.getBodyZoom()
        });

    }

    handleBeaconAnimation(beaconType = TOOLTIP_BEACON_TYPES.ARROW, animationType = "default") {
        
        const isHotSpot = beaconType === TOOLTIP_BEACON_TYPES.PULSE;
        const driven_action_type = this.themeManager.getCurrentSettingValue(["individual", "driven_action_type"]);
        const isDrivenActionTypeHotspot = (driven_action_type == "5");

        if(!isHotSpot && !isDrivenActionTypeHotspot) return;
        
        let currentBeacon = this.iframeUserpilotContainer.querySelector("#userpilot-pulse");
        currentBeacon && currentBeacon.setAttribute("animation", animationType);
    }

    initBeaconEvents(settingsNode) {
        const beaconSettingsNode = settingsNode.querySelector("#beacon-settings");
        const beaconSettings = this.themeManager.getCurrentSettingValue([this.getRootFieldKey(), "beacon"]);
        const beaconCheckbox = settingsNode.querySelector("#beacon-checkbox");
        
        this.initBeaconType(beaconSettings.icon, true);
        const hotSpotAnimationsNode = settingsNode.querySelector("#hot-spot-animations");

        new SelectMenu(hotSpotAnimationsNode, null, (select) => {
            const animationType = select.getAttribute("value");
            this.themeManager.setJsonSetting([this.getRootFieldKey(), "beacon", "animation_type"], animationType);
            this.handleBeaconAnimation(beaconSettings.icon, animationType)
        }, beaconSettings.animation_type || "default");

        const handleBeacon = () => {
            const beaconType = this.themeManager.getCurrentSettingValue([this.getRootFieldKey(), "beacon", "icon"]);
            this.initBeaconType(beaconType);
        }
        beaconCheckbox.addEventListener("change", handleBeacon);

        if(this.type === spotlightTypes.NATIVE) return;

        const beaconStyleSelectBtn = beaconSettingsNode.querySelector("#beacon-type");
        const beaconMenu = new SelectButton(beaconStyleSelectBtn, (select) => this.initBeaconType(select.getAttribute("value")), beaconSettings.icon);

        // Cursor beacon is only available for driven actions
        if(this.type == patternTypes.DRIVEN_ACTION) beaconMenu.selectElements.find(element => element.getAttribute("value") == TOOLTIP_BEACON_TYPES.MOVING_CURSOR).style.display = "";
    }

    manageBeaconBoxSettings() {
        const boxTheme = this.themeManager.getCurrentSettingValue([this.type, "box_border"]);
        const borderType = boxTheme.type;

        const beaconElement = this.userpilotContent.querySelector(".userpilot-beacon");
        
        if(!beaconElement) return;

        const beaconType = beaconElement.getAttribute("type");

        if (!boxTheme.enabled) return;

        if (borderType === "shadow") {
            const shadowWidth = boxTheme.width;
            const shadowIntensity = boxTheme.shadow_intensity;
            beaconElement.style.boxShadow = boxTheme.color + " 0px 0px " + shadowIntensity + "px " + shadowWidth + "px";
        }
        else if (borderType === "solid") {
            if (beaconType == TOOLTIP_BEACON_TYPES.PULSE || beaconType == TOOLTIP_BEACON_TYPES.ARROW) {
                beaconElement.style.zIndex = -1;
            }
        }
    }

    selectNewTarget(object, key, callback, revertCallback) {
        this.templateEditor.enableScroll();
        const templateIframe = Common.shadowRoot.querySelector("#tool-editor");

        templateIframe.style.display = "none";
        this.containerNode.style.display = "none";
        this.settingsToggle.style.display = "none";

        const revertState = () => {
            templateIframe.style.display = 'initial';
            this.containerNode.style.display = 'initial';
            this.settingsToggle.style.display = "initial";
            Store.publishAction(Actions.HIDE_TOOLTIP_HELP_MESSAGE);
        }
        
        const callbackOnCancel = () => {
            revertState();
            if (revertCallback) revertCallback();
            this.templateEditor.enableScroll();
        }

        const setElement = (event) => {
            this.targetElement.querySelector("userpilotinlineelement")?.remove();

            revertState();
            callback(event);
            [spotlightTypes.HOTSPOT, spotlightTypes.NATIVE, spotlightTypes.BUTTON].includes(this.type) ? this.initDisplayOptions(null, event) : this.reInitPlacementSliders();
            Store.publishAction(Actions.HIDE_TOOLTIP_HELP_MESSAGE);
            this.templateEditor.disableScroll();
        }
        new ElementPicker(object, key, null, setElement, null, callbackOnCancel);
        Store.publishAction(Actions.SHOW_TOOLTIP_HELP_MESSAGE);
    }

    handleTemplateToolbarPosition() {
        const toolbar = this.iframeUserpilotContainer.querySelector("#template-controller-toolbar");
        const beaconArrow = this.iframeUserpilotContainer.querySelector(".userpilot-beacon");
        let tooltipDirection = this.themeManager.getCurrentSettingValue(["individual", "placement", "direction"]);

        if(tooltipDirection === "auto") {
            const targetCoords = _.cloneDeep(this.targetElementCoords);
            targetCoords.bounding.top += Positioning.iframePositioning.top;
            targetCoords.left += Positioning.iframePositioning.left;

            tooltipDirection = Positioning.findOptimalDirection(targetCoords);
        }
        const isTooltipOnLeftOfTarget = tooltipDirection.startsWith("left");
        const isTooltipOnRightOfTarget = tooltipDirection.startsWith("right");

        if(!toolbar) return;
        toolbar.style.top = "";
        const toolbarCoords = toolbar.getBoundingClientRect();

        toolbar.classList.remove("toolbar-on-beacon");
        (isTooltipOnLeftOfTarget) ? toolbar.setAttribute("position", "left") : toolbar.setAttribute("position", "right"); 
        const toolbarViewport = htmlTreeOperations.isInViewport(toolbar);

        if (this.type == spotlightTypes.BUTTON) {
            if (!toolbarViewport.vertical && toolbarCoords.top > 0) {
                toolbar.style.top = innerHeight - toolbarCoords.top - toolbarCoords.height - 5 + "px";
            }   

            const buttonElementCoords = this.userpilotSlide.getBoundingClientRect();

            if (buttonElementCoords.left - toolbarCoords.width < 0) {
                toolbar.style.left = `${this.userpilotSlide.clientWidth + 5}px`
                toolbar.style.right = "auto";
            } else {
                toolbar.style.left = ""
                toolbar.style.right = ""
            }
            return;
        }
        if(!toolbarViewport.vertical) toolbar.style.top = innerHeight - toolbarCoords.top - toolbarCoords.height + 5 + "px";
        if (toolbarViewport.sides) return;
        ((isTooltipOnLeftOfTarget) ? toolbar.setAttribute("position", "right") : toolbar.setAttribute("position", "left"));
        
        if (this.type == spotlightTypes.BUTTON) {
            toolbar.classList.add("toolbar-on-beacon");
        }
        
        if(!beaconArrow) return;
        if(!isTooltipOnRightOfTarget && !isTooltipOnLeftOfTarget) return;

        toolbar.classList.add("toolbar-on-beacon");
    }

    getRootFieldKey = () => {
        return (Common.EXPERIENCE_TYPE === experienceTypes.SPOTLIGHT) ? experienceTypes.SPOTLIGHT : this.type;
    }

    handleTemplateReposition() {
        this.individualSettingsNode.addEventListener("repositionTemplate", () => {
            if (this.targetElement.ownerDocument !== document) {
                let element = this.targetElement.ownerDocument.defaultView.frameElement;
                Positioning.iframePositioning = {top: 0, left: 0}
                while (element) {
                    let boundingRect = element.getBoundingClientRect();
                    Positioning.iframePositioning.top += boundingRect.top;
                    Positioning.iframePositioning.left += boundingRect.left;    

                    element = element.ownerDocument.defaultView.frameElement;
                }
            }
            this.positionTooltip(this.targetElement)
        });
    }

}
