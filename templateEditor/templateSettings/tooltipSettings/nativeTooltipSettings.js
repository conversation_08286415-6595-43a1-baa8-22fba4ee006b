import SpotlightSettings from './spotlightSettings.js';
import Views from "../../views/templateViews.js";
import TemplateIcons from "../../views/templateIcons.js";
import Element from "../../element/element.js";
import Positioning from "../../element/positioning.js";
import SelectMenu from "../../../generic-components/selectMenu.js";
import { cleanText } from '../../../generic-utils/textTransformation.js';
import htmlTreeOperations from '../../../generic-utils/htmlTreeOperations.js';

// Constants
import { spotlightTypes } from '../../../store/constants/experiences/spotlightTypes';
import { Store } from '../../../store/store.js';
import Actions from '../../../store/constants/actions.js';
import { SPOTLIGHT_BEHAVIOR_SHOW_ON_OPTIONS } from '../../../store/constants/experiences/individualSettings.js';
import { escape } from 'lodash/fp.js';
import { NATIVE_BADGE_ICONS_TYPES, NATIVE_BADGE_TYPES } from '../../../store/constants/experiences/constants.js';

const defaultLabelValue = "Upgrade";

export default class NativeTooltipSettings extends SpotlightSettings {
    constructor(targetElement, templateEditor) {
        super(spotlightTypes.NATIVE, targetElement, templateEditor);

        this.badgeEl = document.createElement("div");
        this.badgeEl.id = "badge-element";

        const badgeObserverOptions = {
            attributes: true,
            characterData: true,
            subtree: true,
            attributeOldValue: true
        }
        const badgeObserver = new MutationObserver(() => {
            const isBadge = parseInt(this.themeManager.getCurrentSettingValue(["individual", "behavior", "beacon"]));
            if (isBadge) this.setTemplateDirection()
        });

        this.init();
        window.addEventListener("resize", this.handleNativeWindowResize);
        badgeObserver.observe(this.badgeEl, badgeObserverOptions);
    }

    init() {
        this.initIndividualSettings();
        this.switchIndividualGroupSettings("individual");
        this.initGroupSettingsNode();
        this.initTippy();
    }

    initIndividualSettings() {
        this.individualSettingsNode = Views.getViewElement("native-tooltip-individual-settings");
        this.initSpotlightSharedIndividualSettings(this.individualSettingsNode, spotlightTypes.NATIVE);
        this.initNativeTooltipBehaviorEvents(this.individualSettingsNode);
    }

    initCaret()  {
        const beaconStatus = this.themeManager.getCurrentSettingValue(["spotlight", "beacon", "enabled"]);
        const normalCaret = this.userpilotContent.querySelector(".userpilot-beacon") || Views.getViewElement("userpilot-beacon", { beaconID: "beacon-arrow", type: "arrow" });
        
        normalCaret.remove();
        normalCaret.style.background = this.themeManager.getCurrentSettingValue(["spotlight", "background_color"]);
        if(beaconStatus) {
            this.userpilotContent.appendChild(normalCaret);
            this.manageBeaconBoxSettings();
        }
    }

    initGroupSettingsEvents(settingsNode) {
        this.initGenericSettingEvents(settingsNode);
        this.initArrowExpandEvents(settingsNode);

        this.initBadgeEvents(settingsNode);
        this.initBoxEvents(settingsNode);
        this.initTemplateSizeEvent(settingsNode);
        this.initBeaconEvents(settingsNode);
    }

    initNativeTooltipBehaviorEvents(settingsNode) {
        const spotlightEmbeddingSettingsSection = settingsNode.querySelector("#spotlight-embedding-settings");
        const showonSelect = settingsNode.querySelector("#show-on-select");
        const showonValue = this.themeManager.getCurrentSettingValue(["individual", "behavior", "beacon"]);

        const showHideDisplaySettingContainer = (shownonValue) => {
            if(shownonValue == SPOTLIGHT_BEHAVIOR_SHOW_ON_OPTIONS.TARGET_ELEMENT) spotlightEmbeddingSettingsSection.classList.add("inactive");
            else spotlightEmbeddingSettingsSection.classList.remove("inactive");
        }

        const showonOnchange = async (select) => {
            const value = select.getAttribute("value");
            this.themeManager.setJsonSetting(["individual", "behavior", "beacon"], value);
            this.handleBadgeElement(value);
            showHideDisplaySettingContainer(value);
            switch (parseInt(value)) {
                case SPOTLIGHT_BEHAVIOR_SHOW_ON_OPTIONS.TARGET_ELEMENT:
                    this.reInitPlacementSliders()
                    break;
                case SPOTLIGHT_BEHAVIOR_SHOW_ON_OPTIONS.BADGE:
                    this.initDisplayOptions()
                    break;
            } 
        }
        new SelectMenu(showonSelect, null, showonOnchange, showonValue);
        showHideDisplaySettingContainer(showonValue);
    }

    initBeaconEvents(settingsNode) {
        const beaconSettings = this.themeManager.getCurrentSettingValue(["spotlight", "beacon"]);
        const beaconCheckbox = settingsNode.querySelector("#beacon-checkbox");
        
        this.initBeaconType(beaconSettings.icon, true);

        const handleBeacon = () => {
            const beaconType = this.themeManager.getCurrentSettingValue(["spotlight", "beacon", "icon"]);
            this.initBeaconType(beaconType);
        }
        beaconCheckbox.addEventListener("change", handleBeacon);
    }

    initBadge() {
        const userpilotContainer = this.templateIframe.querySelector(".userpilot");
        const badgeType = this.themeManager.getCurrentSettingValue(["spotlight", "type"]);

        userpilotContainer.insertBefore(this.badgeEl, userpilotContainer.firstChild);
        this.badgeEl.style = {};
        badgeType === NATIVE_BADGE_TYPES.ICON ? this.prepareIcon() : this.prepareLabel();

        this.setInlineBadgePosition();
    }

    positionTooltip(target) {
        const isBadge = parseInt(this.themeManager.getCurrentSettingValue(["individual", "behavior", "beacon"]));

        if (!this.templateEditor.targetNotFound && target) {
            this.userpilotContent.classList.remove("invalid");
        } else {
            target = this.virtualTarget;
            this.userpilotContent.classList.add("invalid");
        }

        (isBadge) ? this.positionBadge(target) : this.setTemplateDirection(target);
    }

    handleNativeWindowResize = () => {
        if(!this.isUserpilotContentInDom()) return window.removeEventListener("resize", this.handleNativeWindowResize);

        const isBadge = parseInt(this.themeManager.getCurrentSettingValue(["individual", "behavior", "beacon"]));
        if(isBadge) this.positionSpotlight(this.badgeEl);
    }

    positionBadge(target) {
        const badgeMargin = this.themeManager.getCurrentSettingValue(["individual", "placement", "margin"]);
        const badgePosition = this.themeManager.getCurrentSettingValue(["individual", "placement", "position"]);

        const spotlightPosition = this.getSpotlightPosition(target, badgePosition, this.badgeEl, htmlTreeOperations.getBodyScale());

        this.badgeEl.style.left = spotlightPosition.left + "px";
        this.badgeEl.style.top = spotlightPosition.top + "px";

        this.setTemplateDirection();

        this.badgeEl.style.marginLeft = badgeMargin.left + "px";
        this.badgeEl.style.marginTop = badgeMargin.top + "px";
    }

    setTemplateDirection(target) {
        const isBadge = parseInt(this.themeManager.getCurrentSettingValue(["individual", "behavior", "beacon"]));
        const bodyZoom = isBadge ? 1 : htmlTreeOperations.getBodyZoom();

        if (isBadge) target = this.badgeEl;
        else if (!target) target = this.targetElement
        setTimeout(() => {
            this.targetElementCoords = Element.getBoundingClientRect(target, null, null, bodyZoom);
            Positioning.position({
                tooltip: this.userpilotContent,
                target: target,
                targetCoords: this.targetElementCoords,
                direction: this.themeManager.getCurrentSettingValue(["individual", "placement", "direction"]),
                bodyZoom: bodyZoom,
                includeIframePosition: !isBadge,
            });
            this.handleTemplateToolbarPosition();
        });
    }

    handleBadgeElement(beacon) {
        const badgeSettingsNode = this.groupSettingsNode?.querySelector("#badge-settings");
        if (beacon == "0") {
            this.badgeEl.remove();
            this.positionTooltip(this.targetElement);
            badgeSettingsNode?.classList.remove("expanded");
            badgeSettingsNode?.classList.add("inactive");
        } else {
            this.initBadge();
            this.positionTooltip(this.targetElement);
            badgeSettingsNode?.classList.remove("inactive");
        }
    }

    prepareIcon() {
        const badgeTheme = this.themeManager.getCurrentSettingValue(["spotlight", "badge"]);
        const isSquareIcon = (badgeTheme.shape_type === "square");

        this.badgeEl.innerHTML = TemplateIcons.getNativeTooltipIcon(badgeTheme.shape, isSquareIcon);
        this.badgeEl.classList.add("icon");
        this.badgeEl.classList.remove("label");
        this.badgeEl.style.opacity = (badgeTheme.opacity / 100);
        this.badgeEl.style.setProperty("--badge-fill-hover", badgeTheme.fill_color_on_hover);

        const iconBackground = this.badgeEl.querySelector("#background");
        const iconParts = this.badgeEl.querySelectorAll(".icon");

        switch (badgeTheme.shape) {
            case NATIVE_BADGE_ICONS_TYPES.MORE:
            case NATIVE_BADGE_ICONS_TYPES.PIN:
                iconParts.forEach(el => {
                    el.style.fill = badgeTheme.fill_color;
                    el.style.stroke = badgeTheme.icon_border_color
                });
                break;

            default:
                iconBackground.style.fill = badgeTheme.fill_color;
                iconBackground.style.stroke = badgeTheme.icon_border_color;
                iconParts.forEach(el => el.style.fill = badgeTheme.icon_color);
        }

        const svg = this.badgeEl.querySelector("svg");
        svg.style.width = badgeTheme.size + "px";
        svg.style.height = badgeTheme.size + "px";
    }

    prepareLabel() {
        const badgeTheme = this.themeManager.getCurrentSettingValue(["spotlight", "label"]);
        const localizedBadgeSettings = _.get(this.localizedStepSettings, `theme_v2.spotlight.label`);

        this.badgeEl.innerHTML = escape(localizedBadgeSettings?.label || badgeTheme.label);
        this.badgeEl.classList.add("label");
        this.badgeEl.classList.remove("icon");

        this.badgeEl.style.backgroundColor = badgeTheme.background_color;
        this.badgeEl.style.color = badgeTheme.text_color;
        this.badgeEl.style.borderColor = badgeTheme.border_color;
        this.badgeEl.style.borderRadius = badgeTheme.corner_radius + "px";
        this.badgeEl.style.height = badgeTheme.height + "px";
        this.badgeEl.style.padding = "0px 8px";
        // falling back to 700 because no value is returned for the font weight
        this.badgeEl.style.fontWeight = badgeTheme.font_weight || 700;
        this.badgeEl.style.setProperty("--badge-background-hover", badgeTheme.background_color_on_hover);
        this.badgeEl.style.setProperty("--badge-font-hover", badgeTheme.text_color_on_hover);
        this.badgeEl.style.setProperty("--badge-box-shadow", badgeTheme.border_color);
    }

    renderBadgeOptions(type, settingsNode, firstRun = false) {
        const iconBadgeSettings = settingsNode.querySelector("#icon-badge-settings");
        const labelBadgeSettings = settingsNode.querySelector("#label-badge-settings");
        !firstRun && this.themeManager.setJsonSetting(["spotlight", "type"], type);

        if (type === NATIVE_BADGE_TYPES.ICON) {
            labelBadgeSettings.style.display = "none";
            iconBadgeSettings.style.display = "";
        } else if (type === NATIVE_BADGE_TYPES.LABEL) {
            const badgeLabelSettings = this.themeManager.getCurrentSettingValue(["spotlight", "label"]);
            if(!badgeLabelSettings?.label) this.themeManager.setJsonSetting(["spotlight", "label", "label"], defaultLabelValue);

            iconBadgeSettings.style.display = "none";
            labelBadgeSettings.style.display = "";
        }
    }

    initBadgeEvents(settingsNode) {
        const badgetype = this.themeManager.getCurrentSettingValue(["spotlight", "type"]);
        const showonValue = this.themeManager.getCurrentSettingValue(["individual", "behavior", "beacon"]);

        if (showonValue == "0") {
            const badgeSettingsNode = settingsNode.querySelector("#badge-settings");
            badgeSettingsNode.classList.remove("expanded");
            badgeSettingsNode.classList.add("inactive");
        }

        const badgeTypeSelect = settingsNode.querySelector("#badge-type");
        const handleBadgeTypeSelection = (type) => {
            const showon = this.themeManager.getCurrentSettingValue(["individual", "behavior", "beacon"]);
            this.renderBadgeOptions(type, settingsNode);
            this.handleBadgeElement(showon);
        }
        new SelectMenu(badgeTypeSelect, null, (select) => handleBadgeTypeSelection(select.getAttribute("value")), badgetype);

        this.renderBadgeOptions(badgetype, settingsNode, true);
        this.handleBadgeElement(showonValue);
        this.initBadgeIconEvents(settingsNode);
        this.initBadgeLabelEvents(settingsNode);
    }

    initBadgeIconEvents(settingsNode) {
        const badgeTheme = this.themeManager.getCurrentSettingValue(["spotlight", "badge"]);

        const handleIconShapeSelection = (shape_type) => {
            this.themeManager.setJsonSetting(["spotlight", "badge", "shape_type"], shape_type);
            iconTypesSelect.setAttribute("type", shape_type);
            this.handleBadgeElement("1");
        }
        const iconShapeSelect = settingsNode.querySelector("#icon-shape");
        new SelectMenu(iconShapeSelect, null, (select) => handleIconShapeSelection(select.getAttribute("value")), badgeTheme.shape_type);

        const iconColorPickerContainer = settingsNode.querySelector("#icon-color-picker");
        const handleIconTypesSelection = (type) => {
            this.themeManager.setJsonSetting(["spotlight", "badge", "shape"], type);
            this.handleBadgeElement("1");
            showHideIconColorPicker(type);
        }
        const showHideIconColorPicker = (shape) => {
            if ([NATIVE_BADGE_ICONS_TYPES.MORE, NATIVE_BADGE_ICONS_TYPES.PIN].includes(shape)) iconColorPickerContainer.classList.add("display-none");
            else iconColorPickerContainer.classList.remove("display-none");
        }
        const iconTypesSelect = settingsNode.querySelector("#icon-types");
        const supportedIcons = [NATIVE_BADGE_ICONS_TYPES.HELP, NATIVE_BADGE_ICONS_TYPES.INFO, NATIVE_BADGE_ICONS_TYPES.ALERT, NATIVE_BADGE_ICONS_TYPES.ADD, NATIVE_BADGE_ICONS_TYPES.MORE, NATIVE_BADGE_ICONS_TYPES.PIN];
        const iconTypesSelectOptions = supportedIcons.map((icon) => {
            return Views.getViewElement("icon-types-select-option", {
                squareIcon: TemplateIcons.getNativeTooltipIcon(icon, true),
                circleIcon: TemplateIcons.getNativeTooltipIcon(icon, false),
                label: icon,
                value: icon
            });
        });
        iconTypesSelect.setAttribute("type", badgeTheme.shape_type);
        new SelectMenu(iconTypesSelect, iconTypesSelectOptions, (select) => handleIconTypesSelection(select.getAttribute("value")), badgeTheme.shape);
        showHideIconColorPicker(badgeTheme.shape);
    }

    initBadgeLabelEvents(settingsNode) {
        const badgetype = this.themeManager.getCurrentSettingValue(["spotlight", "type"]);
        const badgeLabelSettings = this.themeManager.getCurrentSettingValue(["spotlight", "label"]);
        const localizedBadgeSettings = _.get(this.localizedStepSettings, `theme_v2.spotlight.label`);

        const labelBadgeInput = settingsNode.querySelector("#label-badge-input");
        labelBadgeInput.addEventListener('input', (event) => {
            const value = event.target.value;

            if(this.templateEditor.languageCode === "default") {
                this.themeManager.setJsonSetting(["spotlight", "label", "label"], value);
                Store.publishAction(Actions.SAVE_STEP, { step: this.stepSettings });
            } else {
                this.userpilotSlide.querySelector(".userpilot-builder").setAttribute("label-text", value);
                this.themeManager.setJsonSetting(["locale", "theme_v2", "spotlight", "label", "label"], value);
                this.templateEditor.saveStepContent();
            }

            this.badgeEl.innerHTML = escape(value);
        })

        labelBadgeInput.value = localizedBadgeSettings?.label || badgeLabelSettings.label || defaultLabelValue;
        labelBadgeInput.addEventListener('paste', (e) => {
            e.preventDefault();
            const text = (e.clipboardData || window.clipboardData).getData('text');
            labelBadgeInput.value = cleanText(text);
        });

        const handleLabelFontWeightSelection = (fontWeight) => {
            this.themeManager.setJsonSetting(["spotlight", "label", "font_weight"], fontWeight);
            this.badgeEl.style.fontWeight = fontWeight;
        }
        const labelFontWeightSelect = settingsNode.querySelector("#font-weight-select");
        // falling back to 700 because no value is returned for the font weight
        new SelectMenu(labelFontWeightSelect, null, (select) => handleLabelFontWeightSelection(select.getAttribute("value")), badgeLabelSettings.font_weight || 700);

        const handleLabelBorderTypeSelection = (type, firstRun) => {
            !firstRun && this.themeManager.setJsonSetting(["spotlight", "label", "border_type"], type);
            (type === "solid") ? this.badgeEl.classList.remove("with-shadow") : this.badgeEl.classList.add("with-shadow");
        }
        const labelBorderTypeSelect = settingsNode.querySelector("#label-border-type");
        // falling back to solid because no value is returned for the border type
        const border_type = badgeLabelSettings.border_type || "solid";
        new SelectMenu(labelBorderTypeSelect, null, (select) => handleLabelBorderTypeSelection(select.getAttribute("value")), border_type);
        if (badgetype === NATIVE_BADGE_TYPES.LABEL) handleLabelBorderTypeSelection(border_type, true);
    }

    handleBadgeSize(element, property, value) {
        const isBadgeIcon = (property === "badgeIconSize");
        const spotlightContainerEl = isBadgeIcon ? element.parentNode : element;
        const placeHolderElement = this.targetElement.querySelector("userpilotinlineelement") 
        if(isBadgeIcon) {
            element.style["width"] = value + "px";
            element.style["height"] = value + "px";
        } else {
            element.style["height"] = value + "px";
        }
        placeHolderElement && (placeHolderElement.style.width = `${value}px`, placeHolderElement.style.height = `${value}px`)
        this.positionSpotlight(spotlightContainerEl)
        this.setInlineBadgePosition();;
    }

    handleTemplateToolbarPosition() {
        super.handleTemplateToolbarPosition();

        const toolbar = this.iframeUserpilotContainer.querySelector("#template-controller-toolbar");
        if(!toolbar) return;
        
        const showon = this.themeManager.getCurrentSettingValue(["individual", "behavior", "beacon"]);
        const badgeWidth = this.badgeEl.offsetWidth;
        const margin = -55 - badgeWidth;
        
        toolbar.style.right = "";
        toolbar.style.left = "";
        if(showon == "1" && toolbar.classList.contains("toolbar-on-beacon")) toolbar.style[toolbar.getAttribute("position")] = margin;
    }
}