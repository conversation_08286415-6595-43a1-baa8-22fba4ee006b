import Views from "../../views/templateViews.js";
import Element from "../../element/element.js";
import ElementTreeConstructor from "../../element/make.js";
import JsonOperations from "../../../generic-utils/jsonOperations.js";
import Find from "../../element/find.js";
import ManualDetection from "../../element/manual";
import SelectMenu from "../../../generic-components/selectMenu.js";
import Positioning from "../../element/positioning.js";
import Common from "../../../index.js";
import { ELEMENT_DETECTION_VERSION } from "../../../store/constants/experiences/constants.js";
import { canReadTextFromElement, cleanText, getBodyElement } from "../../element/utils.js";
import ExperienceBuilderIcons from "../../../views/experienceBuilderIcons.js";
import { generateTextIncludeRegex } from "../../../components/menus/labeledEvents/helpers.js";

export const AUTO_DETECT_TEXT_TAGS = ["h1", "h2", "h3", "h4", "h5", "h6", "p", "label", "button", "a"];

// Regular expression to select all whitespace that isn't in quotes
const MANUAL_SELECTOR_SPLIT_REGEX = /\s+(?=((\\[\\"]|[^\\"])*"(\\[\\"]|[^\\"])*")*(\\[\\"]|[^\\"])*$)/g;

export default class TargetElementManager {
    constructor(targetElementSettings, settingsNode, callbacks = {}, options = {}) {
        this.targetElementSettings = targetElementSettings;
        this.detectionType;
        this.manualQueryTimeout = 0;
        this.settingsNode = settingsNode;

        this.setManualTargetElementSettings = callbacks.setManualTargetElementSettings;
        this.setAutoTargetElementSettings = callbacks.setAutoTargetElementSettings;
        this.getAutoTargetElementSettings = callbacks.getAutoTargetElementSettings;
        this.getManualTargetElementSettings = callbacks.getManualTargetElementSettings;
        this.setTargetElementCallback = callbacks.setTargetElementCallback;
        this.getTargetElementCallback = callbacks.getTargetElementCallback;
        this.findTargetCallback = callbacks.findTargetCallback;
        this.initDisplayOptions = callbacks.initDisplayOptions || (() => {});
        this.reInitPlacementSliders = callbacks.reInitPlacementSliders || (() => {});

        this.isLabeledEvent = options.isLabeledEvent;

        this.manualDetectionObj = {
            attrSelectorsMap: {},
            selectorTextInput: settingsNode.querySelector("#selector-text"),
            elementOrderInput: settingsNode.querySelector("#element-order"),
            includesTextInput: settingsNode.querySelector("#text-include-text"),
            includesTextToggleSection: settingsNode.querySelector("#text-include-settings"),
            detectionStatusNode: settingsNode.querySelector("#element-finder-status"),
            selectorsNode: settingsNode.querySelector("#manual-selectors"),
            multiTextIncludeContainer: settingsNode.querySelector("#multi-text-include-container"),
            multiTextIncludeFields: settingsNode.querySelector("#multi-text-include-fields"),
            multiTextIncludeOperand: settingsNode.querySelector("#multi-text-include-operand"),
            allFoundElements: [],
            mainSelector: "",
        }
        this.findTarget(false, true);
        this.initSettings();
    }

    findTarget = (newQuery=false, firstRun = false) => {
        Positioning.iframePositioning = { top: 0, left: 0 };
        const isAutoDetection = JsonOperations.isJsonEmpty(this.getManualTargetElementSettings());
        const autoTargetTree = this.getAutoTargetElementSettings();
        const result = isAutoDetection ? Find.automatedLookup(autoTargetTree, getBodyElement()) : this.findElementManual(newQuery);

        let target = undefined;
        if(result === -1) this.targetElementSettings.templateEditor.targetNotFound = true;
        else {
            target = result;
            this.targetElementSettings.templateEditor.targetNotFound = false;
        }

        if (target) this.setTargetElementCallback(target, firstRun);
        this.findTargetCallback(target, this.manualDetectionObj.allFoundElements);
    }

    setDetectionType(value, firstRun = false) {
        const detectionTypeRadioBtns = Array.from(this.settingsNode.querySelectorAll("input[name$='detection-radio']"));
        const autoSettingsNode = this.settingsNode.querySelector("#auto-detection-settings");
        const manualSettingsNode = this.settingsNode.querySelector("#manual-detection-settings");

        this.detectionType = value;
        autoSettingsNode.style.display = this.isLabeledEvent ? "" : "none";
        manualSettingsNode.style.display = this.isLabeledEvent ? "" : "none";
        detectionTypeRadioBtns.forEach(element => element.checked = (element.getAttribute("value") == value));


        if (value == "auto") {
            !firstRun && this.setManualTargetElementSettings(null, {})
            autoSettingsNode.style.display = "";
        }
        else {
            this.fillManualDetectionInputs(true);
            manualSettingsNode.style.display = "";
        }
    }

    resetManualDetectionTextIncludeFields() {
        this.manualDetectionObj.includesTextInput.innerHTML = "";
        this.manualDetectionObj.includesTextToggleSection.classList.remove("expanded");
        this.manualDetectionObj.includesTextToggleSection.querySelector("input").checked = false;
    }

    initSettings() {
        const detectionTypeRadioBtns = Array.from(this.settingsNode.querySelectorAll("input[name$='detection-radio']"));
        const manualDetectionSettings = this.getManualTargetElementSettings();

        const changeDetectionType = (value) => {
            this.setDetectionType(value);
            this.findTarget(true);
        }
        JsonOperations.isJsonEmpty(manualDetectionSettings) ? this.setDetectionType("auto", true) : this.setDetectionType("manual", true);

        detectionTypeRadioBtns.forEach(radio => {
            radio.addEventListener("change", changeDetectionType.bind(this, radio.getAttribute("value")));
        });
        
        if(this.isLabeledEvent) this.initMultiElementsSettings();
        this.initAutoDetectionEvents(this.settingsNode.querySelector("#auto-detection-settings"));
        this.initManualDetectionEvents(this.settingsNode.querySelector("#manual-detection-settings"));
    }

    hasSelectorNthChild() {
        const manualSettings = this.getManualTargetElementSettings();
        const targetElementSelector = manualSettings.element.split(MANUAL_SELECTOR_SPLIT_REGEX).pop();

        return targetElementSelector.includes(":nth-child");
    }

    initMultiElementsSettings() {
        const autoSettingsEl = this.settingsNode.querySelector("#auto-detection-settings");
        const multiElementsCheckboxContainer = this.settingsNode.querySelector("#include-all-matching-checkbox-container");
        const multiElementsCheckbox = multiElementsCheckboxContainer.querySelector("input");

        const hideElements = () => {
            const manualSettings = this.getManualTargetElementSettings();
            const targetElementSelector = manualSettings.element.split(MANUAL_SELECTOR_SPLIT_REGEX).pop();
            const hasNthChild = targetElementSelector.match(/nth-child\((\d+)\)(?!.*nth-child\(\d+\))/);
            
            if(multiElementsCheckbox.checked) {
                autoSettingsEl.classList.add("display-none");
                this.manualDetectionObj.selectorsNode.classList.add("display-none");
                this.manualDetectionObj.selectorTextInput.textContent = this.manualDetectionObj.mainSelector + " " + targetElementSelector.replace(/(:nth-child\(.*\))/, "");
            } else {
                autoSettingsEl.classList.remove("display-none");
                this.manualDetectionObj.selectorsNode.classList.remove("display-none");
                if(!hasNthChild) {
                    const originalTargetElement = this.getTargetElementCallback();
                    const selector = this.manualDetectionObj.mainSelector + " " + targetElementSelector;
                    const nthChild = Array.from(originalTargetElement.parentNode.children).indexOf(originalTargetElement) + 1;
                    this.manualDetectionObj.selectorTextInput.textContent = `${selector}:nth-child(${nthChild})`;
                }
            }

            this.manualDetectionObj.selectorTextInput.dispatchEvent(new Event("change"));
        }

        if(!this.hasSelectorNthChild()) {
            multiElementsCheckbox.checked = true;
            hideElements(true);
        }
        multiElementsCheckbox.addEventListener("click", (event) => {
            if (multiElementsCheckbox.classList.contains("disabled")) {
                event.preventDefault();
                return false;
            }
        })
        multiElementsCheckbox.addEventListener("change", async (e) => { 
            multiElementsCheckbox.classList.add("disabled");
            // match the timeout when manually running the query.
            this.releaseSelectionController = () => {
                multiElementsCheckbox.classList.remove("disabled");
            };


            const target = this.getTargetElementCallback();
            if (target !== -1 && target) {
                const css_selector = ManualDetection.generateManualDetectionSelector(target,  {labeled_event: true, include_child: false, with_iframe: false, with_shadowdom: target.getRootNode() instanceof DocumentFragment ? true : false, all_selection: e.currentTarget.checked });
                this.manualDetectionObj.mainSelector = css_selector;
            }
            hideElements()
        })

    }

    initAutoDetectionEvents(autoSettings) {
        autoSettings = autoSettings || this.targetElementSettings.autoDetectionNodeEl.querySelector("#auto-detection-settings");
        autoSettings.innerHTML = autoSettings.innerHTML; // To reset event listeners

        const elementParentSelector = autoSettings.querySelector("#element-parent-selector");
        const selectElements = [];
        let originalTargetElement = this.getTargetElementCallback();
        if(originalTargetElement === -1 || originalTargetElement?.id === "virtual-target") originalTargetElement = this.autoDetectElement();

        let currentTarget = originalTargetElement;
        let parentLevel = 0;
        let selectedElement;

        while (currentTarget && currentTarget.tagName != "HTML") {
            const targetSelect = document.createElement("div");

            if (currentTarget == originalTargetElement) {
                targetSelect.textContent = currentTarget.tagName ? "Element " + currentTarget.tagName : "";
                targetSelect.classList.add("selected");
                targetSelect.setAttribute("value", "default");
                selectedElement = targetSelect;
            }
            else {
                targetSelect.textContent = "Parent Level: " + parentLevel + ", TAG: " + currentTarget.tagName;
            }

            // save the current target so it is registered to select events
            const targetElement = currentTarget;
            const elementHighlighter = document.getElementById("userpilot-hovered-mask");
            targetSelect.addEventListener("mouseenter", (event) => {
                Element.highlightElement(targetElement);
                elementHighlighter.style.display = "block";
            });

            targetSelect.addEventListener("mouseleave", (event) => {
                Element.unhighlightElement();
                elementHighlighter.style.display = "";
            });

            targetSelect.addEventListener("click", (event) => {
                const newElementTree = ElementTreeConstructor.getElementAttrs(targetElement);
                const newElementTreeStringified = JSON.stringify(newElementTree);
                this.setTargetElementCallback(targetElement);
                this.setAutoTargetElementSettings(newElementTreeStringified);
                this.findTarget();

                // Reset manual settings
                if(this.isLabeledEvent) {
                    const nthChild = Array.from(targetElement.parentNode.children).indexOf(targetElement) + 1;
                    const css_selector = ManualDetection.generate(targetElement, "child") + `:nth-child(${nthChild})`;
                    this.manualDetectionObj.selectorTextInput.textContent = css_selector;
                    this.setManualTargetElementSettings("all_fields", { element: css_selector, text: null })
                    this.manualDetectionObj.selectorTextInput.dispatchEvent(new Event("change"));
                }
                else this.setManualTargetElementSettings(null, {})

                Element.unhighlightElement();
                selectedElement.classList.remove("selected");
                targetSelect.classList.add("selected");
                selectedElement = targetSelect;
                this.initDisplayOptions(null)
                this.reInitPlacementSliders()
            });

            currentTarget = currentTarget.parentElement;
            selectElements.push(targetSelect);
            parentLevel++;
        }

        new SelectMenu(elementParentSelector, selectElements, null, "default", "", false, {container: this.targetElementSettings.autoDetectMenuContainer, fitParent: true});
    }

    fillManualDetectionInputs(firstRun = false) {
        const manualDetectionSettings = this.getManualTargetElementSettings();
        const isCleanState = JsonOperations.isJsonEmpty(manualDetectionSettings);

        const attrSelectorsMap = this.manualDetectionObj.attrSelectorsMap;
        const selectorTextInput = this.manualDetectionObj.selectorTextInput;
        const selectorsNode = this.manualDetectionObj.selectorsNode;
        const includesTextInput = this.manualDetectionObj.includesTextInput;

        let target = null;
        if(this.isLabeledEvent) {
            target = this.getTargetElementCallback();
        } else if(this.targetElement === this.virtualTarget) {
            const autoTargetTree = this.getAutoTargetElementSettings();
            target = Find.automatedLookup(autoTargetTree, getBodyElement());
        } else {
            target = this.getTargetElementCallback();
        }

        const selectors = Views.generateElementSelectors(target);

        selectorsNode.innerHTML = "";
        JsonOperations.clear(this.manualDetectionObj.attrSelectorsMap);
        const buildTargetElementSelector = () => selectors.reduce((acc, selector) => {
            const selectorCheckbox = selector.querySelector("input");
            const attribute = selectorCheckbox.getAttribute("attr");
            const value = selectorCheckbox.getAttribute("value");
            const querySelectorString = this.buildSelectorFromAttribute(attribute, value);

            if(!selectorCheckbox.checked) return acc;
            return attribute === "TAG" ? (querySelectorString + acc) : (acc + querySelectorString);
        }, "")

        selectors.forEach(selector => {
            const selectorCheckbox = selector.querySelector("input");
            const attribute = selectorCheckbox.getAttribute("attr");
            const value = selectorCheckbox.getAttribute("value");
            const querySelectorString = this.buildSelectorFromAttribute(attribute, value);

            selectorCheckbox.checked = false;
            selectorsNode.appendChild(selector);
            attrSelectorsMap[attribute] = { value: querySelectorString, checkbox: selectorCheckbox };

            selectorCheckbox.addEventListener("change", () => {

                const endsWithNthChild = selectorTextInput.textContent.match(/nth-child\((\d+)\)(?!.*nth-child\(\d+\))/);
                selectorTextInput.textContent = this.manualDetectionObj.mainSelector + " " + buildTargetElementSelector() + (endsWithNthChild ? `:nth-child(${endsWithNthChild[1]})` : "");
                this.setManualTargetElementSettings("element", selectorTextInput.textContent);
                this.findTarget(true);
            });
        });

        if (isCleanState) {
            let parentCombination = "";
            try { parentCombination = ManualDetection.generateManualDetectionSelector(target, {}) }
            catch(e) { console.log("e >>", e) }

            selectorTextInput.textContent = "";
            Object.keys(attrSelectorsMap).forEach(key => {
                attrSelectorsMap[key].checkbox.checked = true;
                key == "TAG" ? selectorTextInput.textContent = attrSelectorsMap[key].value + selectorTextInput.textContent
                    : selectorTextInput.textContent += attrSelectorsMap[key].value;
            });

            selectorTextInput.textContent = parentCombination + " " + selectorTextInput.textContent;

            this.setManualTargetElementSettings("element", selectorTextInput.textContent);
            this.setManualTargetElementSettings("text", includesTextInput.textContent);
            this.setManualTargetElementSettings("version", ELEMENT_DETECTION_VERSION);
            this.setManualTargetElementSettings("order", "");
        } else {
            selectorTextInput.textContent = this.getManualTargetElementSettings("element");

            const targetElementSelector = selectorTextInput.textContent.split(MANUAL_SELECTOR_SPLIT_REGEX).pop();
            this.updateSelectorCheckboxesStates(attrSelectorsMap, targetElementSelector)
        }

        if(firstRun) {
            const targetElementSelector = selectorTextInput.textContent.split(MANUAL_SELECTOR_SPLIT_REGEX).pop().replace(/[-\/\\^$.*+?()[\]{}|]/g, '\\$&');

            let pattern = `${targetElementSelector}$`;
            let text = selectorTextInput.textContent;
            let regex = new RegExp(pattern, 'g');

            this.manualDetectionObj.mainSelector = text.replace(regex, "");
        }
    }

    runManualQuery(event) {
        const queryInput = event.currentTarget;
        const detectionStatusNode = this.manualDetectionObj.detectionStatusNode;
        const attrSelectorsMap = this.manualDetectionObj.attrSelectorsMap;

        detectionStatusNode.setAttribute("status", "loading");
        clearTimeout(this.manualQueryTimeout);

        this.manualQueryTimeout = setTimeout(() => {
            this.setManualTargetElementSettings("element", queryInput.textContent);
            this.findTarget(true);
            this.fillManualDetectionInputs();
            this.releaseSelectionController?.();
        }, 2000);

        const targetElementSelector = queryInput.textContent.split(MANUAL_SELECTOR_SPLIT_REGEX).pop();
        this.updateSelectorCheckboxesStates(attrSelectorsMap, targetElementSelector)
    }

    updateSelectorCheckboxesStates(attrSelectorsMap, targetElementSelector) {
        Object.keys(attrSelectorsMap).forEach(key => {
            const value = attrSelectorsMap[key].value;
            attrSelectorsMap[key].checkbox.checked = (key === "TAG") ? (targetElementSelector.startsWith(value.toLowerCase()) || targetElementSelector.startsWith(value.toUpperCase()))
                : targetElementSelector.includes(value);
        });
    }

    initManualDetectionEvents() {
        const manualDetectionSettings = this.getManualTargetElementSettings();
        const selectorTextInput = this.manualDetectionObj.selectorTextInput;
        const elementOrderInput = this.manualDetectionObj.elementOrderInput;
        const includesTextInput = this.manualDetectionObj.includesTextInput;
        const detectionStatusNode = this.manualDetectionObj.detectionStatusNode;
        const includesTextToggleSection = this.manualDetectionObj.includesTextToggleSection;

        selectorTextInput.addEventListener("input", (event) => this.runManualQuery(event, selectorTextInput.textContent));
        if(this.isLabeledEvent) selectorTextInput.addEventListener("change", (event) => this.runManualQuery(event, selectorTextInput.textContent));

        elementOrderInput.addEventListener("input", () => {
            if (!elementOrderInput.value) return;
            const inputValue = elementOrderInput.value;
            detectionStatusNode.setAttribute("status", "loading");
            setTimeout(() => {
                if (elementOrderInput.value == inputValue) {
                    this.setManualTargetElementSettings("order", elementOrderInput.value);
                    detectionStatusNode.setAttribute("status", "valid");
                    this.fillManualDetectionInputs();
                    this.changeTargetElementOrder(inputValue);
                }
            }, 1000);
        });

        if(this.isLabeledEvent) this.initTextIncludesForLabeledEvents(manualDetectionSettings);
        else {
            const toggleInput = includesTextToggleSection.querySelector("input");
            toggleInput.addEventListener("change", () => {
                if (toggleInput.checked == true) {
                    if(
                        !includesTextInput.textContent.trim() &&
                        canReadTextFromElement(this.getTargetElementCallback()) &&
                        this.getTargetElementCallback() &&
                        !Common.shadowRoot.contains(this.getTargetElementCallback())
                    ) includesTextInput.textContent = this.getTargetElementCallback()?.textContent || "";

                    includesTextToggleSection.classList.add("expanded");
                    this.setManualTargetElementSettings("text", includesTextInput.innerText);
                }
                else {
                    includesTextToggleSection.classList.remove("expanded");
                    this.setManualTargetElementSettings("text", "");
                }
                this.findTarget(true);
                this.fillManualDetectionInputs();
            });

            includesTextInput.addEventListener("input", () => {
                const inputValue = includesTextInput.innerText;
                detectionStatusNode.setAttribute("status", "loading");
                setTimeout(() => {
                    if (includesTextInput.innerText == inputValue) {
                        this.setManualTargetElementSettings("text", includesTextInput.innerText);
                        this.findTarget(true);
                        this.fillManualDetectionInputs();
                    }
                }, 1000);
            });
    
            if (manualDetectionSettings.text) {
                includesTextToggleSection.classList.add("expanded");
                toggleInput.checked = true;
                includesTextInput.innerText = manualDetectionSettings.text;
            }
            else {
                includesTextToggleSection.classList.remove("expanded");
                toggleInput.checked = false;
                includesTextInput.textContent = "";
            }
            this.setOrderInputValues();
        }

    }

    initTextIncludesForLabeledEvents(manualDetectionSettings) {
        const multiTextIncludeContainer = this.manualDetectionObj.multiTextIncludeContainer;
        const multiTextIncludeFields = this.manualDetectionObj.multiTextIncludeFields;
        const includesTextToggleSection = this.manualDetectionObj.includesTextToggleSection;
        const includesTextInput = this.manualDetectionObj.includesTextInput;
        const detectionStatusNode = this.manualDetectionObj.detectionStatusNode;
        const multiTextIncludeOperand = this.manualDetectionObj.multiTextIncludeOperand;
        let textIncludeItems = this.getManualTargetElementSettings("text") || [];
        const textIncludeOperand = this.getManualTargetElementSettings("operand");

        includesTextInput.style.display = "none";
        multiTextIncludeContainer.style.display = "";

        const onSelectOperand = async (value) => {
            await this.setManualTargetElementSettings("operand", value.getAttribute("value"));
            this.findTarget(true);
            this.fillManualDetectionInputs();
        }
        new SelectMenu(multiTextIncludeOperand, null, onSelectOperand, textIncludeOperand, "", false, { container: Common.shadowRoot.querySelector("#window-container"), fitParent: true, });

        let onInputTimeout = null;
        const onInput = (e) => {
            detectionStatusNode.setAttribute("status", "loading");
            clearTimeout(onInputTimeout);
            onInputTimeout = setTimeout(() => {
                this.setManualTargetElementSettings("text", this.getTextIncludeValues());
                this.findTarget(true);
                this.fillManualDetectionInputs();
            }, 1000);
        }

        const handleAddClick = () => {
            multiTextIncludeFields.appendChild(this.generateTextIncludeItem({textValue: "", onInput, handleAddClick, handleDeleteClick}));
            checkDeletePermission();
        }

        const checkDeletePermission = () => {
            (multiTextIncludeFields.children.length > 1) ? multiTextIncludeFields.classList.remove("disable-delete") : multiTextIncludeFields.classList.add("disable-delete");
        }

        const handleDeleteClick = (textIncludeElement) => {
            if(multiTextIncludeFields.children.length === 1) return;
            textIncludeElement.remove();
            checkDeletePermission();
            onInput();
        }

        const toggleInput = includesTextToggleSection.querySelector("input");
        toggleInput.addEventListener("change", () => {
            if (toggleInput.checked == true) {
                if(
                    textIncludeItems.length === 0 &&
                    canReadTextFromElement(this.getTargetElementCallback()) &&
                    this.getTargetElementCallback() &&
                    !Common.shadowRoot.contains(this.getTargetElementCallback())
                ) textIncludeItems = [cleanText(this.getTargetElementCallback()?.textContent.trim())];
                else textIncludeItems.push("");

                includesTextToggleSection.classList.add("expanded");
                this.setManualTargetElementSettings("text", textIncludeItems);
            }
            else {
                includesTextToggleSection.classList.remove("expanded");
                this.setManualTargetElementSettings("text", null);
                textIncludeItems = []
                multiTextIncludeFields.innerHTML = "";
            }
            this.findTarget(true);
            this.fillManualDetectionInputs();
            renderTextIncludeItems();
        });

        const renderTextIncludeItems = () => {
            multiTextIncludeFields.append(
                ...textIncludeItems.map(item => this.generateTextIncludeItem({ textValue: item, onInput, handleAddClick, handleDeleteClick }))
            );
        }
        renderTextIncludeItems();
        checkDeletePermission();

        if (manualDetectionSettings.text?.length > 0) {
            includesTextToggleSection.classList.add("expanded");
            toggleInput.checked = true;
        }
        else {
            includesTextToggleSection.classList.remove("expanded");
            toggleInput.checked = false;
        }
    }

    generateTextIncludeItem({ textValue, onInput, handleAddClick, handleDeleteClick }) {
        const textIncludeElement = Views.getViewElement("include-text-item", {
            addIcon: ExperienceBuilderIcons.getAddIcon(),
            deleteIcon: ExperienceBuilderIcons.getDeleteIconV2(),
            value: textValue,
        });
        
        textIncludeElement.querySelector(".userpilot-input").addEventListener("input", onInput);
        textIncludeElement.querySelector(".add-icon").addEventListener("click", handleAddClick);
        textIncludeElement.querySelector(".delete-btn").addEventListener("click", () => handleDeleteClick(textIncludeElement))

        return textIncludeElement;
    }

    getTextIncludeValues() {
        const multiTextIncludeFields = this.manualDetectionObj.multiTextIncludeFields;

        return Array.from(multiTextIncludeFields.querySelectorAll(".userpilot-input"))
            .map(input => input.value)
            .filter(value => value);
    }

    changeTargetElementOrder(value) {
        const target = this.manualDetectionObj.allFoundElements[value - 1];
        this.findTargetCallback(target);
        this.initDisplayOptions(target)
        this.reInitPlacementSliders()
    }

    getTargetFromFoundElements(allFoundElements) {
        const index = this.getManualTargetElementSettings("order");
        return allFoundElements[index - 1];
    }

    getTargetFromNewQuery(allFoundElements) {
        const currentTargetIndex = allFoundElements.findIndex(element => element === this.getTargetElementCallback());
        if (currentTargetIndex != -1) this.setManualTargetElementSettings("order", (currentTargetIndex + 1).toString())
        else this.setManualTargetElementSettings("order", "1")

        this.setOrderInputValues();
        return allFoundElements[currentTargetIndex == -1 ? 0 : currentTargetIndex];
    }

    findElementManual(newQuery = false) {
        const detectionStatusNode = this.manualDetectionObj.detectionStatusNode;
        const setDetectionStatusNode = (status) => detectionStatusNode.setAttribute("status", status);

        try {
            this.manualDetectionObj.allFoundElements = Find.manualLookup({
                ...this.getManualTargetElementSettings(),
                ...this.isLabeledEvent && {
                    text: generateTextIncludeRegex(this.getManualTargetElementSettings("text"), this.getManualTargetElementSettings("operand")),
                }
            });
        } catch (error) {
            setDetectionStatusNode("invalid");
            return -1;
        }

        const allFoundElements = this.manualDetectionObj.allFoundElements;
        if (!allFoundElements || allFoundElements.length == 0) {
            setDetectionStatusNode("invalid");
            return -1;
        }

        setDetectionStatusNode("valid");
        return newQuery ? this.getTargetFromNewQuery(allFoundElements) : this.getTargetFromFoundElements(allFoundElements);
    }

    setOrderInputValues() {
        const elementOrderInput = this.manualDetectionObj.elementOrderInput;
        const foundElementsSum = this.manualDetectionObj.allFoundElements.length;

        elementOrderInput.value = this.getManualTargetElementSettings("order");
        elementOrderInput.nextElementSibling.textContent = " / " + foundElementsSum;
        elementOrderInput.setAttribute("max", foundElementsSum);
    }

    buildSelectorFromAttribute(attribute, value) {
        switch (attribute) {
            case "TAG":
                return value.toLowerCase();
            case "ID":
                return "#" + value;
            case "class":
                return "." + value.trim().split(" ").join(".");
            default:
                return `[${attribute.toLowerCase()}="${value.replaceAll("\"", `\\\"`)}"]`;
        }
    }

    autoDetectElement = () => {
        const settings = {
            manual: 0,
            data: this.getAutoTargetElementSettings(),
        };
        return Find.lookup(settings);
    }
}
