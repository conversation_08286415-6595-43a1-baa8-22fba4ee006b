import TooltipsSharedSettings from './tooltipsSharedSettings.js';
import Views from "../../views/templateViews.js";
import Element from "../../element/element.js";
import Positioning from "../../element/positioning.js";
import Find from "../../element/find.js";
import SelectMenu from "../../../generic-components/selectMenu.js";
import { Store } from '../../../store/store.js';
import Actions from '../../../store/constants/actions.js';
import htmlTreeOperations from '../../../generic-utils/htmlTreeOperations.js';
import TargetElementManager from './targetElementManager.js';
import JsonOperations from '../../../generic-utils/jsonOperations.js';

export default class DrivenSettings extends TooltipsSharedSettings {
    constructor(targetElement, templateEditor) {
        super("driven", targetElement, templateEditor);
        this.actionOnElement;
        this.init();
    }

    init() {
        this.initIndividualSettings();
        this.switchIndividualGroupSettings("individual");

        this.initTippy();
    }

    initIndividualSettings() {
        this.individualSettingsNode = Views.getViewElement("driven-individual-settings");
        this.initSharedIndividualSettings(this.individualSettingsNode);
        this.initDrivenActionTypeEvents(this.individualSettingsNode.querySelector("#action-settings"));
        this.handleTemplateReposition();
    }

    initGroupSettingsEvents(settingsNode) {
        this.initGenericSettingEvents(settingsNode);
        this.initArrowExpandEvents(settingsNode);

        this.initProgressEvent(settingsNode);
        this.initSkippableEvent(settingsNode);
        this.initPointerSettings(settingsNode);
        this.initBackdropEvents(settingsNode);
        this.initBeaconEvents(settingsNode);
        this.initBoxEvents(settingsNode);
    }

    positionTooltip(target) {
        const driven_action_type = this.themeManager.getCurrentSettingValue(["individual", "driven_action_type"]);
        const direction = this.themeManager.getCurrentSettingValue(["individual", "placement", "direction"]);
        const isHotspot = driven_action_type == "5";

        this.positionElement(target);
        setTimeout(() => {
            if (isHotspot) {
                const pulseEl = this.templateIframe.querySelector("#userpilot-pulse");
                this.targetElementCoords = Element.getBoundingClientRect(this.targetElement, null , null, htmlTreeOperations.getBodyZoom());
                
                Positioning.positionDrivenActionHotspot({
                    element: pulseEl,
                    target: this.targetElement,
                    targetCoords: this.targetElementCoords,
                    direction: direction,
                    scaleOperand: htmlTreeOperations.getBodyScale()
                });
            }
        });
        setTimeout(() => this.setPointerTarget());
    }

    initDrivenActionTypeEvents = (settingsNode) => {
        const autoFocus = settingsNode.querySelector("#auto-focus");

        const actionTypeSelect = settingsNode.querySelector("#action-type");
        
        const individualSettings = this.themeManager.getCurrentSettingValue(["individual"]);

        const clickOptionsNode = settingsNode.querySelector("#click-type-options");
        const inputOptionsNode = settingsNode.querySelector("#input-type-options");

        const setActionOptions = (optionsNode, type) => {
            const behaviorSelect = optionsNode.querySelector(".action-behavior-select");
            this.themeManager.setJsonSetting(["individual", "driven_action_type"], type);
            this.themeManager.setJsonSetting(["individual", "exp_cont_type"], behaviorSelect.getAttribute("value"));
        }

        const changeActionType = (type, firstRun = false) => {
            const beaconTheme = this.themeManager.getCurrentSettingValue([this.type, "beacon"]);
            const pulseBeacon = this.iframeUserpilotContainer.querySelector("#userpilot-pulse") || Views.getViewElement("userpilot-pulse");
            clickOptionsNode.style.display = "none";
            inputOptionsNode.style.display = "none";

            pulseBeacon.style.marginLeft = "";
            pulseBeacon.style.marginTop = "";
            this.iframeUserpilotContainer.classList.remove("driven-action-hotspot");
            pulseBeacon.remove();

            switch (type) {
                case "1":
                    clickOptionsNode.style.display = "";
                    !firstRun && setActionOptions(clickOptionsNode, type);
                    break;
                    
                case "2":
                    inputOptionsNode.style.display = "";
                    !firstRun && setActionOptions(inputOptionsNode, type);
                    break;

                case "4":
                    if(!firstRun) {
                        this.themeManager.setJsonSetting(["individual", "driven_action_type"], type);
                        this.themeManager.setJsonSetting(["individual", "exp_cont_type"], "1");
                    }
                    break;

                case "5":
                    const placementSettings = this.themeManager.getCurrentSettingValue(["individual", "placement"]);
                    if(!firstRun) {
                        this.themeManager.setJsonSetting(["individual", "driven_action_type"], type);
                        this.themeManager.setJsonSetting(["individual", "exp_cont_type"], "1");
                    }

                    Array.from(pulseBeacon.children).forEach(el => el.style.background = beaconTheme.color );
                    pulseBeacon.style.marginLeft = placementSettings.margin.left + "px";
                    pulseBeacon.style.marginTop = placementSettings.margin.top + "px";

                    this.iframeUserpilotContainer.classList.add("driven-action-hotspot");
                    this.iframeUserpilotContainer.appendChild(pulseBeacon);
                    break;

                default:
                    console.error();
                    break;
            }

            if (!firstRun) this.positionTooltip(this.targetElement);
            this.initBeaconType(beaconTheme.icon, firstRun);
        }
        new SelectMenu(actionTypeSelect, null, (select) => changeActionType(select.getAttribute("value")), individualSettings.driven_action_type);

        this.initDrivenActionDetectionSettings(settingsNode);
        this.initClickActionEvents(clickOptionsNode);
        this.initTextInputActionEvents(inputOptionsNode);

        changeActionType(individualSettings.driven_action_type, true);
    }

    initClickActionEvents(settingsNode) {
        const behaviorSelect = settingsNode.querySelector(".action-behavior-select");
        const individualSettings = this.themeManager.getCurrentSettingValue(["individual"]);
        const clickActionElementDisplay = settingsNode.querySelector("#click-action-element-display");
        const reselectElementNode = settingsNode.querySelector("#reselect-element-target");
        let actionElement;

        const setClickActionType = (type) => this.themeManager.setJsonSetting(["individual", "exp_cont_type"], type);

        const isOnAnotherElement = (value = null) => (value || individualSettings.exp_cont_type) ==  "2";

        const renderAndSaveContent = (actionType) => {
            actionType && setClickActionType(actionType);
            Store.publishAction(Actions.SAVE_EXPERIENCE_CONTENT);
            renderState();
        }

        const onSelectChange = (actionType) => {
            const newTarget = isOnAnotherElement(actionType) && !individualSettings.exp_cont_element
            
            if (newTarget) {
                const revertCallback = () => {
                    setClickActionType("1");
                    renderState();
                };
                this.selectNewTarget(individualSettings, "exp_cont_element", () => {
                    this.initDrivenActionDetectionSettings(this.individualSettingsNode.querySelector("#action-settings"));
                    renderAndSaveContent(actionType);
                }, revertCallback);
            } else {
                renderAndSaveContent(actionType);
            }
        }

        const isClickAction = individualSettings.driven_action_type == "1";
        const actionType = isClickAction ? individualSettings.exp_cont_type : "1";
        const typeSelectMenu = new SelectMenu(behaviorSelect, null, (select) => onSelectChange(select.getAttribute("value")), actionType);

        const renderState = (type = individualSettings.exp_cont_type) => {
            clickActionElementDisplay.style.display = isOnAnotherElement() ? "" : "none";
            typeSelectMenu.setValue(type);

            if (isOnAnotherElement() && individualSettings.exp_cont_element) {
                actionElement = this.getActionElement();
                if (actionElement) this.setPointerTarget(actionElement);

            } else {
                this.setPointerTarget();
            }
        }
        if(isClickAction) renderState();

        const reTarget = () => {
            this.themeManager.setJsonSetting(["individual", "exp_cont_detection"], {});
    
            this.drivenActionElementDetectionManager.findTarget();
            this.drivenActionElementDetectionManager.initAutoDetectionEvents();
            this.drivenActionElementDetectionManager.setDetectionType("auto");
            this.reinitNodeSettings("action-settings", Views.getViewElement("driven-action-type"), this.initDrivenActionTypeEvents);
            renderAndSaveContent();
        }

        reselectElementNode.addEventListener("click", () => this.selectNewTarget(individualSettings, "exp_cont_element", reTarget));
    }

    getActionElement() {
        if(!this.stepSettings.individual.exp_cont_element) return null;

        const isAutoDetection = JsonOperations.isJsonEmpty(this.stepSettings.individual.exp_cont_detection);
        let drivenActionTargetElement = null;

        if (isAutoDetection) {
            const settings = {
                manual: 0,
                data: this.stepSettings.individual.exp_cont_element,
            };
            drivenActionTargetElement = Find.lookup(settings);
        } else {
            const foundTargets = Find.manualLookup(this.stepSettings.individual.exp_cont_detection) || [];
            const targetIndex = parseInt(this.stepSettings.individual.exp_cont_detection.order) - 1;
            drivenActionTargetElement = foundTargets[targetIndex] || -1;
        }

        return drivenActionTargetElement;
    }

    initDrivenActionDetectionSettings = (settingsNode) => {
        let drivenActionTargetElement = this.getActionElement();
        if(!drivenActionTargetElement) return;
        this.drivenActionElementDetectionManager = new TargetElementManager(this.getDrivenActionTargetElementSettings(settingsNode), settingsNode.querySelector("#click-type-options"), {
            setManualTargetElementSettings: (field, value) => this.setManualTargetElementSettings("exp_cont_detection", field, value),
            setAutoTargetElementSettings: (value) => this.themeManager.setJsonSetting(["individual", "exp_cont_element"], value),
            findTargetCallback: () => this.setPointerTarget(),
            getAutoTargetElementSettings: () => this.themeManager.getCurrentSettingValue(["individual", "exp_cont_element"]),
            getManualTargetElementSettings: (field) => this.getManualTargetElementSettings("exp_cont_detection", field),
            setTargetElementCallback: (target) =>  drivenActionTargetElement = target,
            getTargetElementCallback: () =>  drivenActionTargetElement,
        });
    }

    getDrivenActionTargetElementSettings = (settingsNode) => {
        return {
            templateEditor: {},
            autoDetectionNodeEl: settingsNode,
            autoDetectMenuContainer: null,
        }
    }

    GetDetectedElementAttrs(detectionJson) {
        const attributes = { tag: "", id: "", class: "" }
        detectionJson.forEach(object => {
            Object.keys(object).forEach(key => {
                switch (key) {
                    case "tag":
                        attributes[key] = object[key];
                        break;
                    case "id":
                        attributes[key] = "#" + object[key];
                        break;
                    case "class":
                        attributes[key] = "." + object[key].split(" ").join(".");
                        break;
                }
            });
        });
        return attributes;
    }

    initTextInputActionEvents(settingsNode) {
        const behaviorSelect = settingsNode.querySelector(".action-behavior-select");
        const individualSettings = this.themeManager.getCurrentSettingValue(["individual"]);

        const onChange = (type) => this.themeManager.setJsonSetting(["individual", "exp_cont_type"], type);
        new SelectMenu(behaviorSelect, null, (select) => onChange(select.getAttribute("value")), individualSettings.exp_cont_type);
    }

    initPointerSettings(settingsNode) {
        const cursor = this.iframeUserpilotContainer.querySelector("#userpilot-cursor-preview") || Views.getViewElement("userpilot-cursor-preview");

        this.groupSettingsNode.addEventListener("setPointerOn", () => addPointer());

        const addPointer = (type) => {
            const driven_action_type = this.themeManager.getCurrentSettingValue(["individual", "driven_action_type"]);
            const beaconTheme = this.themeManager.getCurrentSettingValue([this.type, "beacon"]);
            const isDrivenActionTypeHotspot = (driven_action_type == "5");

            cursor.remove();
            if (!beaconTheme.enabled) return;
            if (!type) type = beaconTheme.icon;

            const target = this.actionOnElement;
            const targetCoords = Element.getBoundingClientRect(target, null, null, htmlTreeOperations.getBodyZoom());

            if(isDrivenActionTypeHotspot) return;
            
            if(type == "5") {
                cursor.style.left = "";
                cursor.style.top = "";
                cursor.style.transform = "";
                if(target !== -1) {
                    this.iframeUserpilotContainer.appendChild(cursor);
                    Positioning.animateCursorMovement(targetCoords, cursor, this.userpilotSlide, target);
                }
            }
        }
    }

    setPointerTarget(actionOnElement = null) {
        const individualSettings = this.themeManager.getCurrentSettingValue(["individual"]);

        if (individualSettings.driven_action_type == "1" && individualSettings.exp_cont_type == "2") {
            if (actionOnElement) this.actionOnElement = actionOnElement
            else this.actionOnElement = this.getActionElement() || -1;
        } else {
            this.actionOnElement = this.targetElement;
        }
        this.groupSettingsNode.dispatchEvent(new CustomEvent("setPointerOn"));
    }

}

