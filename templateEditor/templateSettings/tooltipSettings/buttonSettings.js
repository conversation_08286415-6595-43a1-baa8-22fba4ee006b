import SpotlightSettings from "./spotlightSettings";
import Find from "../../element/find.js";
import Views from "../../views/templateViews.js";
import Common from "../../../index.js";
import SelectMenu from "../../../generic-components/selectMenu.js";
import UserCompnayProperties from "../../../generic-components/userProperties/userCompnayProperties.js";
import AdditionSelectMenu from "../../../generic-components/additionSelectMenu.js";
import URLInput from "../../../generic-components/urlInput.js";
import ToggleCheck from "../../../generic-components/toggleCheck.js";
import ExperienceBuilderViews from "../../../views/experienceBuilderViews.js";
// Constants
import { spotlightTypes } from '../../../store/constants/experiences/spotlightTypes';
import { ACTION_TYPES } from "../../wysiwyg-builder/components/Action/action.js";
import { PROPERTIES_TYPES } from "../../../generic-components/userProperties/constants.js";

//utils
import htmlTreeOperations from "../../../generic-utils/htmlTreeOperations";
import MessageHandler from "../../../routing/messageHandler.js";
import CssOperations from "../../../generic-utils/cssOperations.js";
import { COLOR_TYPES } from "../../../store/constants/experiences/constants.js";

export default class ButtonSettings extends SpotlightSettings {
    constructor(targetElement, templateEditor) {
        super(spotlightTypes.BUTTON, targetElement, templateEditor);

        this.buttonElement = Views.getViewElement("userpilot-button");
        this.contentContainer = this.iframeUserpilotContainer.querySelector("#content-container");
        this.iframeUserpilotContainer.querySelector(".userpilot-slide").remove();
        this.iframeUserpilotContainer.appendChild(this.buttonElement);
        this.userpilotSlide = this.iframeUserpilotContainer.querySelector(".userpilot-slide");

        templateEditor.prepareObjectVars();

        this.badgeEl = this.buttonElement;
        this.templateEditor = templateEditor;

        window.removeEventListener("resize", this.handleTooltipSharedSettingsWindowResize)

        const buttonObserver = new MutationObserver(() => this.setTemplateDirection(this.buttonElement));
        buttonObserver.observe(this.buttonElement, { attributeFilter: ["style"] });

        this.init();

        window.addEventListener("resize", this.handleButtonWindowResize.bind(this));
    }

    init() {

        if (this.templateEditor.languageCode !== "default") {
            this.buttonElement.classList.add("localization-active")
        }

        this.contentContainer.style.zIndex = "9";

        this.triggerSurveyProps = {};
        this.triggerFlowProps = {};

        const individual = this.themeManager.getCurrentSettingValue(["individual"]);
        
        !individual.action && this.migrateIndividualSettings();

        this.initGroupSettingsNode();

        this.positionTooltip(this.targetElement);
        this.initIndividualSettings();
        this.switchIndividualGroupSettings("individual");
        this.initTippy();
    }

    migrateIndividualSettings() {
        this.themeManager.setJsonSetting(["individual", "action"], {
            type: "nothing",
            url: "",
            new_tab: 0,
            survey: "",
            flow: "",
            jsCallback: "",
            properties: [],
            track: "",
            navigate_to_url: 0
        });

        this.themeManager.setJsonSetting(["individual", "label"], {
            text: "Button"
        });
    }
    
    handleButtonWindowResize() {
        if (!this.isUserpilotContentInDom()) return window.removeEventListener("resize", this.handleButtonWindowResize);
        this.setTemplateDirection(this.buttonElement);
    }

    initIndividualSettings() {
        const individualSettingsNode = Views.getViewElement("button-individual-settings");
        this.individualSettingsNode = individualSettingsNode;

        this.initActionEvents(individualSettingsNode);
        this.initUpdateUserProps(individualSettingsNode);
        this.initTrackEvents(individualSettingsNode);
        this.initSpotlightSharedIndividualSettings(individualSettingsNode, spotlightTypes.BUTTON);
    }

    initGroupSettingsNode() {
        this.groupSettingsNode = Views.getViewElement("button-grp-settings");
        this.groupSettingsNode.insertBefore(this.themeManager.themeSettingsNode, this.groupSettingsNode.firstElementChild)
        this.initGroupSettingsEvents(this.groupSettingsNode);
        this.initBoxEvents(this.groupSettingsNode);
        this.setColorEvents();
        this.setGeneralOptions();
        this.initButtonLabelEvents(this.groupSettingsNode);
    }

    reinitGroupSettings() {
        this.initGroupSettingsNode();
        this.appendGroupSettings();
    }

    reInitGroupSettingsNode() {
        const templateCurrentSettings = this.groupSettingsNode.parentNode;
        this.groupSettingsNode.remove();
        this.initGroupSettingsNode();
        if (templateCurrentSettings) {
            templateCurrentSettings.appendChild(this.groupSettingsNode);
        }
    }

    initBoxEvents(settingsNode) {

        const element = this.iframeUserpilotContainer.querySelector(".userpilot-slide .userpilot-btn");

        const solidColorPickerEl = settingsNode.querySelector("#solid-border-settings .color-display");
        const shadowColorPickerEl = settingsNode.querySelector("#shadow-border-settings .color-display");

        const boxTheme = this.themeManager.getCurrentSettingValue([this.type, "box_border"]);

        const renderBox = () => {
            switchBorderShadowOptions(boxTheme.type);
        }

        const removeBoxOutline = () => {
            element.style.boxShadow = "";
            element.style.border = "";
            element.style.borderWidth = "";
            element.style.boxShadow = "";
            element.style.display = "";
            element.style.zIndex = 0;
        }

        const addBorderOrShadow = () => {
            const type = boxTheme.type;
            removeBoxOutline();

            if (type === "solid") {
                const borderWidth = boxTheme.width;
                element.style.border = "solid";
                element.style.borderWidth = borderWidth + "px";
                element.style.borderColor = boxTheme.color;

                this.changeColorPickerColor(solidColorPickerEl, boxTheme.color);
            } else if (type === "shadow") {
                const shadowWidth = boxTheme.width;
                const shadowIntensity = boxTheme.shadow_intensity;
                element.style.boxShadow = boxTheme.color + " 0px 0px " + shadowIntensity + "px " + shadowWidth + "px";

                this.changeColorPickerColor(shadowColorPickerEl, boxTheme.color);
            }
        }

        const solidBorderOptions = settingsNode.querySelector("#solid-border-settings");
        const shadowBorderOptions = settingsNode.querySelector("#shadow-border-settings");
        const switchBorderShadowOptions = (type) => {
            if (type === "solid") {
                shadowBorderOptions.style.display = "none";
                solidBorderOptions.style.display = "";
            } else if (type === "shadow") {
                solidBorderOptions.style.display = "none";
                shadowBorderOptions.style.display = "";
            }
            addBorderOrShadow();
        }

        const onBorderTypeChange = (selectedOption) => {
            const borderType = selectedOption.getAttribute("value");
            this.themeManager.setJsonSetting([this.type, "box_border", "type"], borderType);
            switchBorderShadowOptions(borderType);
        }
        const borderTypeSelect = settingsNode.querySelector("#border-type");
        new SelectMenu(borderTypeSelect, null, (select) => onBorderTypeChange(select), boxTheme.type);

        renderBox();
    }


    initGroupSettingsEvents(settingsNode) {
        this.initGenericSettingEvents(settingsNode);
        this.initArrowExpandEvents(settingsNode);
    }

    initActionEvents(settingsNode) {
        const buttonActionSttings = this.themeManager.getCurrentSettingValue(["individual", "action"]) || {};
        const actionType = buttonActionSttings.type || "nothing";
        const actionTypeNodeContainer = settingsNode.querySelector("#action-type-container");
        const actionTypeNode = actionTypeNodeContainer.querySelector(".action-type")


        const URLOption = settingsNode.querySelector(".go-to-url-options");
        const surveyOption = settingsNode.querySelector(".survey-options");
        const flowOption = settingsNode.querySelector(".flow-options");
        const jsCallbackOption = settingsNode.querySelector(".jsCallback-options");

        this.initTriggerSurveyEvents(surveyOption);
        this.initTriggerFlowEvents(flowOption);
        this.initGoToURLEvents(URLOption);
        this.initTriggerJsFunctionEvents(jsCallbackOption);

        this.handleActionType(actionType, settingsNode);
        new SelectMenu(actionTypeNode, null, (select) => {
            const actionType = select.getAttribute("value");
            this.themeManager.setJsonSetting(["individual", "action", "type"], actionType);
            this.handleActionType(actionType, settingsNode);
        }, buttonActionSttings.type || "nothing");
    }

    initGoToURLEvents(optionsContainer) {
        const actionSettings = this.themeManager.getCurrentSettingValue(["individual", "action"]);

        const onProtocolChange = () => this.urlInstance.getURL() && this.themeManager.setJsonSetting(["individual", "action", "url"], this.urlInstance.getFullURL());
        const onChange = () => this.themeManager.setJsonSetting(["individual", "action", "url"], this.urlInstance.getFullURL());
        const onNewTabChangeCallback = (event) => this.themeManager.setJsonSetting(["individual", "action", "new_tab"], event.target.checked ? 1 : 0);

        this.urlInstance = new URLInput(optionsContainer, {
            protocol: URLInput.protocols.HTTPS,
            showNewTabOption: true,
            onUrlChangeCallback: onChange,
            onProtocolChangeCallback: onProtocolChange,
            onNewTabChangeCallback: onNewTabChangeCallback,
            propertiesContainer: this.containerNode,
        })

        actionSettings.type === ACTION_TYPES.GO_TO_URL.value && this.urlInstance.setUrl(actionSettings.url, actionSettings.new_tab);
    }

    initTriggerSurveyEvents(optionsContainer) {
        const actionSettings = this.themeManager.getCurrentSettingValue(["individual", "action"]);
        const btnAction = actionSettings.type;

        const onProtocolChange = () => this.triggerSurveyProps.urlInstance.getURL() && this.themeManager.setJsonSetting(["individual", "action", "url"], this.triggerSurveyProps.urlInstance.getFullURL());
        const onChange = () => this.themeManager.setJsonSetting(["individual", "action", "url"], this.triggerSurveyProps.urlInstance.getFullURL());
        const urlSection = optionsContainer.querySelector(".url-section-v2");
        this.triggerSurveyProps.urlInstance = new URLInput(urlSection, {
            protocol: URLInput.protocols.HTTPS,
            showNewTabOption: false,
            onUrlChangeCallback: onChange,
            onProtocolChangeCallback: onProtocolChange,
            propertiesContainer: this.containerNode,
        });
        btnAction === ACTION_TYPES.SURVEY.value && this.triggerSurveyProps.urlInstance.setUrl(actionSettings.url, false);

        const toggleCheckInput = optionsContainer.querySelector("#trigger-survey-navigate-to-url");
        this.triggerSurveyProps.navigateUrlToggle = this.initNavigateUrlToggle(ACTION_TYPES.SURVEY.value, toggleCheckInput, optionsContainer, this.triggerSurveyProps.urlInstance);

        const surveySelectBtn = optionsContainer.querySelector("#surveys-list");
        const surveysSelectElements = ExperienceBuilderViews.getSurveysSelectOptions(true);
        if (surveysSelectElements.length == 0) surveySelectBtn.textContent = "No Surveys Available";
        else {
            const currentSelectedSurvey = actionSettings.survey;
            const onSurveySelect = () => this.themeManager.setJsonSetting(["individual", "action", "survey"], surveySelectBtn.getAttribute("value"));
            this.triggerSurveyProps.surveySelectMenu = new SelectMenu(surveySelectBtn, surveysSelectElements, onSurveySelect, currentSelectedSurvey,
                "", false, { container: this.container }, { searchable: true });
        }
    }

    initTriggerFlowEvents(optionsContainer) {
        const actionSettings = this.themeManager.getCurrentSettingValue(["individual", "action"]);
        const btnAction = actionSettings.type;

        const onProtocolChange = () => this.triggerFlowProps.urlInstance.getURL() && this.themeManager.setJsonSetting(["individual", "action", "url"], this.triggerFlowProps.urlInstance.getFullURL());
        const onChange = () => this.themeManager.setJsonSetting(["individual", "action", "url"], this.triggerFlowProps.urlInstance.getFullURL());

        const urlSection = optionsContainer.querySelector(".url-section-v2");
        this.triggerFlowProps.urlInstance = new URLInput(urlSection, {
            protocol: URLInput.protocols.HTTPS,
            showNewTabOption: false,
            onUrlChangeCallback: onChange,
            onProtocolChangeCallback: onProtocolChange,
            propertiesContainer: this.containerNode,
        });

        btnAction === ACTION_TYPES.FLOW.value && this.triggerFlowProps.urlInstance.setUrl(actionSettings.url, false);

        const toggleCheckInput = optionsContainer.querySelector("#navigate-to-url");
        this.triggerFlowProps.navigateUrlToggle = this.initNavigateUrlToggle(ACTION_TYPES.FLOW.value, toggleCheckInput, optionsContainer, this.triggerFlowProps.urlInstance);

        const flowSelectBtn = optionsContainer.querySelector("#flows-list");
        const flowsSelectElements = ExperienceBuilderViews.getFlowsSelectOptions(true);
        if (flowsSelectElements.length == 0) flowSelectBtn.textContent = "No Flows Available";
        else {
            const currentSelectedFlow = actionSettings.flow;
            const onFlowSelect = () => this.themeManager.setJsonSetting(["individual", "action", "flow"], flowSelectBtn.getAttribute("value"));
            this.triggerFlowProps.flowSelectMenu = new SelectMenu(flowSelectBtn, flowsSelectElements, onFlowSelect, currentSelectedFlow,
                "", false, { container: this.container }, { searchable: true });
        }
    }

    handleActionType(actionType, settingsNode) {
        Array.from(settingsNode.querySelectorAll(".action-option")).forEach(action => action.classList.remove("active"))

        const action = `${actionType}-options`;
        const actionNode = settingsNode.querySelector(`.${action}`);
        actionNode?.classList?.add("active");
    }

    initNavigateUrlToggle(actionType, toggleInput, optionsContainer, urlInstance) {
        const btnAction = this.themeManager.getCurrentSettingValue(["individual", "action", "type"]);
        const navigate_to_url = parseInt(this.themeManager.getCurrentSettingValue(["individual", "action", "navigate_to_url"]));
        const url = this.themeManager.getCurrentSettingValue(["individual", "action", "url"]);

        const isFlowSelected = () => {
            return btnAction == ACTION_TYPES.FLOW.value;
        }
        const isSurveySelected = () => {
            return btnAction === ACTION_TYPES.SURVEY.value;
        }
        const setInitialState = () => {
            return (isFlowSelected() || isSurveySelected()) && parseInt(this.themeManager.getCurrentSettingValue(["individual", "action", "navigate_to_url"]));
        }
        const checkedCallback = () => {
            optionsContainer.setAttribute("navigate-url", "on");
            !parseInt(navigate_to_url) && this.themeManager.setJsonSetting(["individual", "action", "navigate_to_url"], 1);
        }
        const uncheckedCallback = () => {
            optionsContainer.removeAttribute("navigate-url", "on");
            if (actionType == btnAction) {
                url && this.themeManager.setJsonSetting(["individual", "action", "url"], "");
                parseInt(navigate_to_url) && this.themeManager.setJsonSetting(["individual", "action", "navigate_to_url"], 0);
            }
        }
        return new ToggleCheck(toggleInput, setInitialState, checkedCallback, uncheckedCallback);
    }

    initTriggerJsFunctionEvents(optionsContainer) {
        const textArea = optionsContainer.querySelector(".userpilot-text-area");
        textArea.addEventListener("input", (e) => {
            this.themeManager.setJsonSetting(["individual", "action", "jsCallback"], e.target.value);
        });

        textArea.textContent = this.themeManager.getCurrentSettingValue(["individual", "action", "jsCallback"]) || "";

    }

    initButtonLabelEvents(optionsContainer) {
        const input = optionsContainer.querySelector("#label-badge-input");
        const setLabel = () => {
            this.buttonElement.querySelector(".userpilot-btn").textContent = input.value.trim();
        }
        input.addEventListener("input", (e) => {
            if (this.templateEditor.languageCode === "default") {
                this.themeManager.setJsonSetting(["individual", "label", "text"], e.target.value);
            } else {
                this.themeManager.setJsonSetting(["locale", "individual", "label", "text"], e.target.value);
            }

            input.setAttribute("status", "valid");
            if (!input.value.trim()) {
                input.setAttribute("status", "invalid");
                return false;
            }
            setLabel();
        });

        input.value = this.localizedStepSettings.individual?.label?.text || this.themeManager.getCurrentSettingValue(["individual", "label", "text"]);
        setLabel();
    }


    initUpdateUserProps(container) {
        const buttonActionSttings = this.themeManager.getCurrentSettingValue(["individual", "action"]) || {};

        const userPropsContainer = container.querySelector("#action-setting #user-properties");
        const popupContainer = container.querySelector("#action-setting");
        const userPropsOptionsContainer = userPropsContainer.querySelector("#user-company-properties-options");

        const userPropertiesList = UserCompnayProperties.getUserProperties({ withAutoProperties: false, withUserId: false, });
        const companyPropertiesList = UserCompnayProperties.getCompanyProperties({ withAutoProperties: false });

        const onToggleUnchecked = () => {
            const userProps = userPropsOptionsContainer.querySelectorAll(".select-set-user-property");
            if (userProps.length) {
                Array.from(userProps).forEach(entry => entry.remove());
                this.themeManager.setJsonSetting(["individual", "action", "properties"], []);
            }
        }

        const properties = buttonActionSttings.properties || [];
        properties.forEach(entry => {
            const propertyList = entry.type === PROPERTIES_TYPES.USER ? userPropertiesList : companyPropertiesList;
            const propertyValue = entry.property;
            entry.title = propertyList.find(property => property.legacy_key === propertyValue)?.display_name
        });

        userPropsContainer.updatedCallback = () => {
            const propertiesList = Array.from(userPropsContainer.querySelectorAll(".select-set-user-property"))
                .map(el => {
                    return {
                        property: el.querySelector(".userpilot-select").getAttribute("value")?.trim(),
                        value: el.querySelector("input[type='text']").value,
                        type: el.getAttribute("type"),
                    }
                }).filter(entry => entry.property && entry.value);

            this.themeManager.setJsonSetting(["individual", "action", "properties"], propertiesList);
        }

        new UserCompnayProperties(userPropsContainer, popupContainer, {
            elements: buttonActionSttings.properties || [],
            selectMenuOptions: {
                container: this.containerNode,
            },
            onToggleUncheckedCallback: onToggleUnchecked
        });
    }

    initTrackEvents(container) {
        const popupContainer = container.querySelector("#action-setting");

        this.initTrackEventSelectMenu(popupContainer);

        const trackEventCheckbox = popupContainer.querySelector("#track-event");
        const trackEventInput = popupContainer.querySelector("#event-input-container input");
        const trackEventSelect = popupContainer.querySelector("#event-input-container");

        trackEventCheckbox.addEventListener("change", () => {
            if (trackEventCheckbox.checked) {
                popupContainer.setAttribute("track-event", "on");
                trackEventSelect.style.display = "";
            } else {
                popupContainer.removeAttribute("track-event");
                trackEventInput.value = "";
                trackEventSelect.style.display = "none";
                this.themeManager.setJsonSetting(["individual", "action", "track"], "");
            }
        });

        trackEventInput.addEventListener("change", () => {
            this.themeManager.setJsonSetting(["individual", "action", "track"], trackEventInput.value);
        });

        // // Set track event state
        const trackEventValue = this.themeManager.getCurrentSettingValue(["individual", "action", "track"]);

        if (trackEventValue) {
            trackEventCheckbox.checked = true;
            container.setAttribute("track-event", "on");
            trackEventSelect.style.display = "";
            const displayName = Common.userData.trackableEvents.find(item => item.key === trackEventValue)?.display_name || '';
            trackEventInput.value = displayName || trackEventValue;
        }
    }


    initTrackEventSelectMenu(container) {
        const eventsSelectButton = container.querySelector('#track-event-select');

        const elements = Common.userData.trackableEvents
            .filter(item => item.status !== 'archived')
            .map(item => {
                const itemEl = document.createElement('div');
                itemEl.setAttribute('value', item.key);
                itemEl.innerHTML = item.display_name;
                return itemEl;
            });

        const onSelectCallback = (elementSelected) => {
            const trackableEventValue = elementSelected.getAttribute('value');
            const trackableEventItem = Common.userData.trackableEvents.find(trackableEvent => trackableEvent.key === trackableEventValue);
            if (!trackableEventItem) {
                MessageHandler.postTrackableEvent({ title: trackableEventValue, display_name: trackableEventValue, source: "userpilot", type: "trackable_event" })
                    .then(response => {
                        if (!response?.errors) return Common.userData.trackableEvents.push(response);
                        if (response?.errors && !response?.errors?.[0]?.changeset_details?.title?.includes("has already been taken")) {
                            return Alert({
                                severity: "error",
                                content: "Oops something went wrong here!"
                            });
                        }
                    });
            }
            this.themeManager.setJsonSetting(["individual", "action", "track"], trackableEventValue);
        };

        new AdditionSelectMenu(
            eventsSelectButton,
            elements,
            onSelectCallback,
            "",
            { container: container.querySelector("#track-event-container").parentNode, defaultPosition: "bottom", },
            { inputPlaceholder: "Select or Add Custom event...", classes: ["track-event-menu"] },
            true
        );
    }

    positionTooltip(target) {
        if (!this.templateEditor.targetNotFound && target) {
            this.userpilotContent.classList.remove("invalid");
        } else {
            target = this.virtualTarget;
            this.userpilotContent.classList.add("invalid");
        }

        const buttonPosition = this.themeManager.getCurrentSettingValue(["individual", "placement", "position"]);
        const spotlightPosition = this.getSpotlightPosition(target, buttonPosition, this.buttonElement, htmlTreeOperations.getBodyScale());
        this.buttonElement.style.left = spotlightPosition.left + "px";
        this.buttonElement.style.top = spotlightPosition.top + "px";

        this.setTemplateDirection(this.buttonElement);
    }

    setColorEvents() {
        const manualColorPicker = this.groupSettingsNode.querySelector(".manual-color-block");
        const colorTypeSelect = this.groupSettingsNode.querySelector("#color-type");
        const currentType = this.themeManager.getCurrentSettingValue([this.type, "font", "color_type"]);

        const changeColorType = (value, save = true) => {
            if (save) this.themeManager.setJsonSetting([this.type, "font", "color_type"], value);
            switch (value) {
                case COLOR_TYPES.AUTOMATIC:
                    manualColorPicker.style.display = "none";
                    break;
                case COLOR_TYPES.MANUAL:
                    manualColorPicker.style.display = "";
                    break;
                default:
                    break;
            }

            this.setColorOptions();
        }

        changeColorType(currentType, false);

        new SelectMenu(colorTypeSelect, null, (select) => changeColorType(select.getAttribute("value")), currentType);
    }

    setColorOptions() {
        const currentType = this.themeManager.getCurrentSettingValue([this.type, "font", "color_type"]);

        const colors = this.themeManager.getCurrentSettingValue([this.type]);
        const element = this.iframeUserpilotContainer.querySelector(".userpilot-slide .userpilot-btn");

        const font_color = this.determineColor(colors.background_color, colors.font.color, currentType);
        const font_color_hover = this.determineColor(colors.background_color_hover, colors.font.color_hover, currentType);

        element.style.background = colors.background_color;
        element.style.color = font_color;

        element.style.setProperty("--badge-background-hover", colors.background_color_hover);
        element.style.setProperty("--badge-font-hover", font_color_hover);

        element.setAttribute("font-orignal-font-color", colors.font.color);
        element.setAttribute("font-orignal-font-color-hover", colors.font.color_hover);

        element.setAttribute("font-hover-color", font_color_hover);
        element.setAttribute("bg-hover-color", colors.background_color_hover);
        element.setAttribute("color-type", currentType);
    }

    setGeneralOptions() {
        const settings = this.themeManager.getCurrentSettingValue([this.type]);
        const element = this.iframeUserpilotContainer.querySelector(".userpilot-slide .userpilot-btn");

        element.style.borderRadius = settings.box_border.corner_radius + "px";
        element.style.padding = settings.box_border.padding;
        element.style.fontSize = settings.font.size + "px";
        element.style.fontWeight = settings.font.weight;

    }

    determineColor(bg_color, font_color, type) {
        if (type == COLOR_TYPES.AUTOMATIC) {
            return CssOperations.lightOrDark(CssOperations.hexToRgb(bg_color, true));
        } else return font_color;
    }

}