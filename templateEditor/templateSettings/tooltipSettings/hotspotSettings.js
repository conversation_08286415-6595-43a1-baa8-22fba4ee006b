import SpotlightSettings from "./spotlightSettings";
import Find from "../../element/find.js";
import Views from "../../views/templateViews.js";
import SelectMenu from "../../../generic-components/selectMenu.js";

// Constants
import { spotlightTypes } from '../../../store/constants/experiences/spotlightTypes';

//utils
import htmlTreeOperations from "../../../generic-utils/htmlTreeOperations";

export default class HotspotSettings extends SpotlightSettings {
    constructor(targetElement, templateEditor) {
        super(spotlightTypes.HOTSPOT, targetElement, templateEditor);
        this.hotspotPulseBeacon = Views.getViewElement("userpilot-pulse");

        const hotspotObserver = new MutationObserver(() => this.setTemplateDirection(this.hotspotPulseBeacon));

        this.init();
        hotspotObserver.observe(this.hotspotPulseBeacon, { attributeFilter: ["style"] });

        window.addEventListener("resize", this.handleHotspotWindowResize);
    }

    init() {
        this.initIndividualSettings();
        this.switchIndividualGroupSettings("individual");
        this.initGroupSettingsNode();
        this.positionTooltip(this.targetElement);
        this.initTippy();
    }

    initIndividualSettings() {
        this.individualSettingsNode = Views.getViewElement("hotspot-individual-settings");
        this.initSpotlightSharedIndividualSettings(this.individualSettingsNode, spotlightTypes.HOTSPOT);
    }

    initGroupSettingsNode() {
        this.groupSettingsNode = Views.getViewElement("hotspot-grp-settings");
        this.groupSettingsNode.insertBefore(this.themeManager.themeSettingsNode, this.groupSettingsNode.firstElementChild)
        this.initGroupSettingsEvents(this.groupSettingsNode);
    }

    initGroupSettingsEvents(settingsNode) {
        this.initGenericSettingEvents(settingsNode);
        this.initArrowExpandEvents(settingsNode);

        this.initIconEvents(settingsNode);
    }

    handleHotspotWindowResize = () => {
        if(!this.isUserpilotContentInDom()) return window.removeEventListener("resize", this.handleHotspotWindowResize);
        this.positionSpotlight(this.hotspotPulseBeacon);
    }

    positionTooltip(target) {
        if (!this.templateEditor.targetNotFound && target) {
            this.userpilotContent.classList.remove("invalid");
        } else {
            target = this.virtualTarget;
            this.userpilotContent.classList.add("invalid");
        }

        const hotspotPosition = this.themeManager.getCurrentSettingValue(["individual", "placement", "position"]);
        const spotlightPosition = this.getSpotlightPosition(target, hotspotPosition, this.hotspotPulseBeacon, htmlTreeOperations.getBodyScale());
        this.hotspotPulseBeacon.style.left = spotlightPosition.left + "px";
        this.hotspotPulseBeacon.style.top = spotlightPosition.top + "px";

        this.setTemplateDirection(this.hotspotPulseBeacon);
    }

    initIcon() {
        const hotspotTheme = this.themeManager.getCurrentSettingValue(["spotlight", this.type]);
        const hotspotMargin = this.themeManager.getCurrentSettingValue(["individual", "placement", "margin"]);
        const togglePulseContainer = this.groupSettingsNode.querySelector(".pulse-toggle-container"); 

        Array.from(this.hotspotPulseBeacon.children).forEach(el => {
            el.style.color = hotspotTheme.color;
            el.style.background = hotspotTheme.color;
            el.style.borderColor = hotspotTheme.color;
        });
        this.handlePulseAnimation(hotspotTheme.pulse);

        this.handleAnimationType(hotspotTheme.animation_type || "default", togglePulseContainer);

        this.iframeUserpilotContainer.appendChild(this.hotspotPulseBeacon);
        this.handleHotspotSize(this.hotspotPulseBeacon, hotspotTheme.size);

        this.hotspotPulseBeacon.style.marginLeft = hotspotMargin.left + "px";
        this.hotspotPulseBeacon.style.marginTop = hotspotMargin.top + "px";
    }

    initIconEvents(settingsNode) {
        this.initIcon();

        const togglePulse = settingsNode.querySelector("#pulse-toggle");
        const togglePulseContainer = settingsNode.querySelector(".pulse-toggle-container"); 
        const hotSpotAnimationsNode = settingsNode.querySelector("#hot-spot-animations");
        const hotspotTheme = this.themeManager.getCurrentSettingValue(["spotlight", this.type]);

        new SelectMenu(hotSpotAnimationsNode, null, (select) => {
            const animationType = select.getAttribute("value");
            this.themeManager.setJsonSetting(["spotlight", this.type, "animation_type"], animationType);
            this.handleAnimationType(animationType, togglePulseContainer);
        }, hotspotTheme.animation_type || "default");

        togglePulse.addEventListener('change', (event) => {
            const value = event.target.checked;
            this.themeManager.setJsonSetting(["spotlight", this.type, "pulse"], value);
            this.handlePulseAnimation(value);
        })
    }

    handleAnimationType(type = "default", togglePulseContainer) {
        this.hotspotPulseBeacon.setAttribute('animation', type);
        type !== "default" ? togglePulseContainer.classList.add("hidden") : togglePulseContainer.classList.remove("hidden");
    }

    handlePulseAnimation(animate) {
        (animate) ? this.hotspotPulseBeacon.classList.remove("no-pulse") : this.hotspotPulseBeacon.classList.add("no-pulse");
    }

    handleHotspotSize(element, value) {
        this.setHotspotSize(element, value);
        this.positionSpotlight(element)
    }

    handleInlinePlaceholder(element, value, badge) {
        element.style.width = `${value}px`;
        element.style.height = `${value}px`;
        this.setInlineBadgePosition(badge);
    }

    setHotspotSize(element, value) {
        let scaledValue = this.calculateScaledValue(value)

        const placeHolderElement = this.targetElement.querySelector("userpilotinlineelement");
        placeHolderElement && this.handleInlinePlaceholder(placeHolderElement, scaledValue, element);

        element.style.width = `${scaledValue}px`;
        element.style.height = `${scaledValue}px`;

        const pulseOuterEl = element.querySelector(".userpilot-pulse--outer");
        pulseOuterEl.style["width"] = value + "px";
        pulseOuterEl.style["height"] = value + "px";

        const pulseInnerEl = element.querySelector(".userpilot-pulse--inner");
        pulseInnerEl.style["width"] = (value / 2) + "px";
        pulseInnerEl.style["height"] = (value / 2) + "px";

        try {
            if(value%2 !== 0) {
                pulseOuterEl.classList.add('odd-pulse');
            } else {
                pulseOuterEl.classList.remove('odd-pulse');
            }
        } catch (error) {
            pulseOuterEl.classList.remove('odd-pulse');
        }
    }
}