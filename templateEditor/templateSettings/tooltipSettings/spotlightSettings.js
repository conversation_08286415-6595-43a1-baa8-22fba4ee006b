import TooltipsSharedSettings from "./tooltipsSharedSettings";
import TargetElementManager from '../tooltipSettings/targetElementManager';
import SelectMenu from "../../../generic-components/selectMenu.js";
import JsonOperations from "../../../generic-utils/jsonOperations.js";
import Find from "../../element/find";
import Common from "../../..";

// Store
import Actions from "../../../store/constants/actions.js";
import { Store } from "../../../store/store.js";

// constants
import { spotlightTypes } from "../../../store/constants/experiences/spotlightTypes";
import Element from "../../element/element";
import Positioning from "../../element/positioning";
import { SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS, SPOTLIGHT_BEHAVIOR_SHOW_ON_OPTIONS, } from "../../../store/constants/experiences/individualSettings";

//utils
import htmlTreeOperations from "../../../generic-utils/htmlTreeOperations";
import TemplateSettings from "../templateSettings";
import { sendSpotlightTrackEvent } from "../../../components/appBar/editExperience/spotlight/spotlight.js";

export default class SpotlightSettings extends TooltipsSharedSettings {
    constructor(type, targetElement, templateEditor) {
        super(type, targetElement, templateEditor);
    }

    initSpotlightSharedIndividualSettings(settingsNode, type) {

        const isButtonType = type === spotlightTypes.BUTTON;

        this.initGenericSettingEvents(settingsNode);
        this.initArrowExpandEvents(settingsNode);
        this.initSpotlightsDisplayBehaviorEvents(settingsNode, true);
        this.elementDetectionManager = new TargetElementManager(this.getTargetElementSettings(), settingsNode, {
            setManualTargetElementSettings: (field, value) => this.setManualTargetElementSettings("detection", field, value),
            setAutoTargetElementSettings: this.setAutoTargetElementSettings,
            findTargetCallback: this.findTargetCallback,
            getAutoTargetElementSettings: this.getAutoTargetElementSettings,
            getManualTargetElementSettings: (field) => this.getManualTargetElementSettings("detection", field),
            setTargetElementCallback: (target) =>  this.targetElement = target,
            getTargetElementCallback: () =>  this.targetElement,
            initDisplayOptions: (target) =>  this.initDisplayOptions(target)
        });

        this.initSpotlightUiPatternSelectEvent(this.containerNode);

        if (!isButtonType) {
            this.initSpotlightBehaviorEvents(settingsNode.querySelector("#spotlight-behavior-settings"));
        } 
        
        this.initReselectElementEvents(settingsNode.querySelector("#element-settings"));
        this.initPlacementEvents(settingsNode.querySelector("#placement-settings"), true);
        this.initFrequencyEvents(settingsNode.querySelector("#frequency-settings"));
    }

    setInlineBadgePosition(badgeEl) {
        const getDimensions = () => {
            const width = isButton ? badgeEl.clientWidth : this.themeManager.getCurrentSettingValue(["spotlight", this.getInnerFieldKey(), "size"]);
            const height = isButton ? badgeEl.clientHeight : width;
            return {
                width: width,
                height: height
            }
        }

        const badge = badgeEl || this.badgeEl;
        const isButton = this.type === spotlightTypes.BUTTON;
        const displayBehavior = this.themeManager.getCurrentSettingValue(["individual", "behavior", "display", "position"]);
        if (SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS.INLINE == displayBehavior) {
            const size = getDimensions()
            const scaledValue = (this.type == spotlightTypes.HOTSPOT) ? {width: this.calculateScaledValue(size.width), height: this.calculateScaledValue(size.height)} : size;
            const placeholderMarginTop = scaledValue.height / 2;
            const placeholderMarginLeft = scaledValue.width / 2;
            badge.style.transform = `translate(-${placeholderMarginLeft}px, -${placeholderMarginTop}px)`    
        } else {
            badge.style.transform = "";
        }
    }

    calculateScaledValue = (value) => {
        const animation = this.themeManager.getCurrentSettingValue(["spotlight", this.type, "animation_type"]);
        return TemplateSettings.getScaledValue(value, animation);
    }

    initSpotlightsDisplayBehaviorEvents(settingsNode, firstRun = false) {
        const displaySelect = settingsNode.querySelector("#embedding-display-select");
        const displayDirectionSelect = settingsNode.querySelector("#embedding-display-direction-select");
        const displayDirectionSelectContainer = settingsNode.querySelector("#embedding-display-direction-container");
        const positionToggleContainer = settingsNode.querySelector(".absolute-position-container");
        const positionToggle = settingsNode.querySelector("#absolute-position");
        const display = this.themeManager.getCurrentSettingValue(["individual", "behavior", "display", "position"]);
        const absolute = this.themeManager.getCurrentSettingValue(["individual", "behavior", "display", "absolute"]);
        const displayDirection = this.themeManager.getCurrentSettingValue(["individual", "behavior", "display", "direction"]);
        const isButton = this.type === spotlightTypes.BUTTON;
        const badgeEl = this.badgeEl;

        const getDimensions = () => {
            const width = isButton ? badgeEl.clientWidth : this.themeManager.getCurrentSettingValue(["spotlight", this.getInnerFieldKey(), "size"]);
            const height = isButton ? badgeEl.clientHeight : width;
            return {
                width: width,
                height: height
            }
        }

        let placeHolderElement = null;
        const createPlaceHolder = (target) => {
            const position = this.themeManager.getCurrentSettingValue(["individual", "behavior", "display", "absolute"]);
            const displayDirection = this.themeManager.getCurrentSettingValue(["individual", "behavior", "display", "direction"]);
            
            const size = getDimensions();
            placeHolderElement = SpotlightSettings.createPlaceHolder({target, position, displayDirection, width: size.width, height: size.height});
            return placeHolderElement;
        }

        const getPlaceHolder = (targetElement) => placeHolderElement || createPlaceHolder(targetElement);
        const removePlaceHolder = () => {
            placeHolderElement?.remove();
            placeHolderElement = null;
        };
        const isPulse = () => (this.type === spotlightTypes.HOTSPOT && this.hotspotPulseBeacon);

        const displayOnchange = (select, target, event = null, firstRun = false) => {
            const value = select.getAttribute("value");
            let targetElement = target || this.targetElement;
            const spotlightSize = isButton ? 0 : this.themeManager.getCurrentSettingValue(["spotlight", this.getInnerFieldKey(), "size"])
            const absolute = this.themeManager.getCurrentSettingValue(["individual", "behavior", "display", "absolute"]);
            !firstRun && this.themeManager.setJsonSetting(["individual", "behavior", "display", "position"], value);

            const getSliderValues = (element, event) => {
                if (element === this.virtualTarget) {
                    const position = this.themeManager.getCurrentSettingValue(["individual", "placement", "position"]);    
                    return {
                        top: position.height,
                        left: position.width,
                        marginTop: 0,
                        marginLeft: 0
                    }
                }

                const size = getDimensions()
                const scaledValue = (this.type == spotlightTypes.HOTSPOT) ? {width: this.calculateScaledValue(size.width), height: this.calculateScaledValue(size.height)} : size;
                let clientWidth = element.clientWidth || element.offsetWidth,
                    clientHeight = element.clientHeight || element.offsetHeight,
                    boundingRect = getPlaceHolder(element).getBoundingClientRect();

                const { top, left } = event ? TemplateSettings.calculateInlinePosition(event, boundingRect, scaledValue) : {top: 0, left: 0};
                return { top: clientHeight, left: clientWidth, marginTop: top, marginLeft: left }
            }

            let placementSliderValues = { top: 30, left: 30, marginTop: 0, marginLeft: 0 };

            switch (value) {
                case SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS.OVERLAY:
                    positionToggleContainer.style.display = "none";
                    displayDirectionSelectContainer.style.display = "none";
                    removePlaceHolder();
                    break
                case SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS.INLINE:
                    placementSliderValues = getSliderValues(this.targetElement, event);
                    targetElement = getPlaceHolder(targetElement)
                    positionToggleContainer.style.display = "";
                    displayDirectionSelectContainer.style.display = "";
                    directionOnChange(displayDirectionSelect, this.targetElement, firstRun)
                    break
            }
            isPulse() && this.setHotspotSize(this.hotspotPulseBeacon, spotlightSize);
            
            this.positionTooltip(targetElement);
            this.reInitPlacementSliders(placementSliderValues, firstRun);
            (value == SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS.INLINE) && handlePosition(absolute, firstRun);
            this.setInlineBadgePosition(this.badgeEl || this.hotspotPulseBeacon);

            Common.ELEMENT_PICKER_EVENT_REF = null;
        }

        const directionOnChange = (select, target, firstRun = false) => {
            if (displaySelect.getAttribute("value") == SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS.OVERLAY) {
                return false;
            }

            const direction = select.getAttribute("value");
            const targetElement = getPlaceHolder(target);
            SpotlightSettings.setPlaceholderDirection(targetElement.parentNode, targetElement, direction);
            this.positionTooltip(targetElement);

            !firstRun && this.themeManager.setJsonSetting(["individual", "behavior", "display", "direction"], direction);

        };

        const positionOnChange = async (event) => handlePosition(event.target.checked ? "1" : "0");

        const handlePosition = (absolutePosition, firstRun = false) => {
            const targetElement = getPlaceHolder(this.targetElement);
            const spotlightSize = isButton ? 0 : this.themeManager.getCurrentSettingValue(["spotlight", this.getInnerFieldKey(), "size"]);

            if (absolutePosition == "1") {
                !firstRun && this.themeManager.setJsonSetting(["individual", "behavior", "display", "absolute"], "1");
                targetElement.style.position = "absolute"
            } else {
                !firstRun && this.themeManager.setJsonSetting(["individual", "behavior", "display", "absolute"], "0");
                targetElement.style.position = "relative"
            }
            isPulse() && this.setHotspotSize(this.hotspotPulseBeacon, spotlightSize);
            this.positionTooltip(targetElement);
        }

        const initDisplayOptions = (target, event = null, firstRun = false) => { // `firstRun` => initSpotlightsDisplayBehaviorEvents method call
            const showonValue = this.themeManager.getCurrentSettingValue(["individual", "behavior", "beacon"]);
            if(showonValue != SPOTLIGHT_BEHAVIOR_SHOW_ON_OPTIONS.BADGE) return;
            const display = this.themeManager.getCurrentSettingValue(["individual", "behavior", "display", "position"]);
            const absolute = this.themeManager.getCurrentSettingValue(["individual", "behavior", "display", "absolute"]);

            removePlaceHolder();
            createPlaceHolder(target || this.targetElement);

            (display == SPOTLIGHT_BEHAVIOR_DISPLAY_OPTIONS.INLINE) && handlePosition(absolute, firstRun);
            
            displayOnchange(displaySelect, target, event, firstRun);
            directionOnChange(displayDirectionSelect, target, firstRun);
        }

        this.initDisplayOptions = initDisplayOptions;

        positionToggle.addEventListener("change", positionOnChange);
        
        new SelectMenu(displaySelect, null, displayOnchange, display);
        new SelectMenu(displayDirectionSelect, null, directionOnChange, displayDirection)
        
        positionToggle.checked = absolute == "1" ? true : false;

        // if new spotlight is created, use the event ref to define the placement of the spotlight.
        const event = Common.ELEMENT_PICKER_EVENT_REF || null;

        initDisplayOptions(this.targetElement, event, firstRun);

        // reset to null after init
        Common.ELEMENT_PICKER_EVENT_REF = null;

    }


    initSpotlightUiPatternSelectEvent(settingsNode) {
        const uiType = this.stepSettings.type;
        const uiTypeSelect = settingsNode.querySelector("#spotlight-type-select");

        const changeUiPattern = async (select) => {
            const value = select.getAttribute("value");

            if(value === spotlightTypes.NATIVE && !this.stepSettings.content) {
                await Store.publishAction(Actions.CLEAR_WINDOW_COMPONENTS);
                Store.publishAction(Actions.SPAWN_TEMPLATE_PICKER, {
                    type: value,
                    new_group: false,
                    step_settings: this.stepSettings,
                    addition_callback: async (step) => {
                        if(step.content) {
                            JsonOperations.setObjectValue(step, ["type"], value);
                            await Store.publishAction(Actions.REPLACE_STEP, { step: step });
                            await Store.publishAction(Actions.SPAWN_TEMPLATE_EDITOR, { step_id: step.action_id });
                        }
                    }
                });
            } else {
                JsonOperations.setObjectValue(this.stepSettings, ["type"], value);
                await Store.publishAction(Actions.SAVE_STEP, { step: this.stepSettings });
                await Store.publishAction(Actions.STEP_TYPE_CHANGED);
                this.templateEditor.refresh();
            }
            if (value == spotlightTypes.BUTTON) {
                this.themeManager.setJsonSetting(["individual", "behavior", "beacon"], 1);
            }
            Store.publishAction(Actions.REMOVE_INLINE_PLACEHOLDER);
            sendSpotlightTrackEvent(value);
        }
        new SelectMenu(uiTypeSelect, null, changeUiPattern, uiType);
    }

    initSpotlightBehaviorEvents(settingsNode) {
        const triggerSelect = settingsNode.querySelector("#trigger-select");
        const triggerValue = this.themeManager.getCurrentSettingValue(["individual", "behavior", "event"]) || "1";
        const triggerOnchange = async (select) => {
            const value = select.getAttribute("value");
            this.themeManager.setJsonSetting(["individual", "behavior", "event"], value);
        }
        new SelectMenu(triggerSelect, null, triggerOnchange, triggerValue);
    }

    getSpotlightPosition(targetElement, spotlightPosition, spotlightElement, scaleOperand = 1) {

        /**
         * To better match the SDK behavior, we add a placeholder the targeted element. This placeholder would simulate the design change the spotlight
         * would cause on the DOM tree.
         */
        let coordinateElement = this.targetElement.querySelector("userpilotinlineelement"),
        adjustedPosition = {left: 0, top: 0};
        if (!coordinateElement) {
            coordinateElement = targetElement;
            adjustedPosition = this.elementResizeChange(targetElement, spotlightElement, this.groupSettings.version, spotlightPosition);
        }
        const targetElementCoords = Element.getBoundingClientRect(coordinateElement, null, null, 1);

        return {
            left: (targetElementCoords.left + Positioning.iframePositioning.left + adjustedPosition.left * scaleOperand),
            top: (targetElementCoords.bounding.top + Positioning.iframePositioning.top + adjustedPosition.top * scaleOperand),
        }
    }

    elementResizeChange(targetElement, badge, version, spotlightPosition) {
        let clientHeight;
        let clientWidth;

        if (targetElement.ownerSVGElement) {
            if (targetElement.attributes.width) {
                clientHeight = parseInt(targetElement.attributes.width.value);
                clientWidth = parseInt(targetElement.attributes.height.value);
            } else if (targetElement.viewportElement.scrollWidth) {
                clientHeight = parseInt(targetElement.viewportElement.scrollWidth);
                clientWidth = parseInt(targetElement.viewportElement.scrollHeight);
            }
        } else {
            clientHeight = (targetElement.clientHeight) ? targetElement.clientHeight : targetElement.offsetHeight;
            clientWidth = targetElement.clientWidth ? targetElement.clientWidth : targetElement.offsetWidth;
        }

        let left = spotlightPosition.left;
        let top = spotlightPosition.top;
        if (parseFloat(version) >= 0.2 && badge) {
            left -= badge.clientWidth / 2;
            top -= badge.clientHeight / 2;
        }

        const scaleY = clientHeight / spotlightPosition.height;
        const scaleX = clientWidth / spotlightPosition.width;
        return {
            left: left * scaleX,
            top: top * scaleY
        }
    }

    positionSpotlight = (spotlightContainerEl) => {
        const placementSettings = this.themeManager.getCurrentSettingValue(["individual", "placement"]);
        let target = this.findTargetElement();
        
        const spotlightPosition = this.getSpotlightPosition(target, placementSettings.position, spotlightContainerEl, htmlTreeOperations.getBodyScale());
        spotlightContainerEl.style["left"] = spotlightPosition.left + "px";
        spotlightContainerEl.style["top"] = spotlightPosition.top + "px";
    }

    findTargetElement = () => {
        const elementSettings = this.themeManager.getCurrentSettingValue(["individual", "detection"]);
        const isAutoDetection = JsonOperations.isJsonEmpty(elementSettings);

        if (isAutoDetection) return Find.lookup({ manual: 0, data: this.stepSettings.ea });
        
        const foundTargets = Find.manualLookup(this.stepSettings.individual.detection) || [];
        const targetIndex = parseInt(this.stepSettings.individual.detection.order) - 1;
        return foundTargets[targetIndex] || -1;
    }

    getInnerFieldKey = () => {
        return (this.type === spotlightTypes.HOTSPOT) ? spotlightTypes.HOTSPOT : "badge";
    }

}