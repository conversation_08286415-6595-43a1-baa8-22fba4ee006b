import App from "../../components/app";
import SelectMenu from "../../generic-components/selectMenu";
import ExperienceBuilderViews from "../../views/experienceBuilderViews";
import LogicElementDetectionSettings from "./helpers/logicElementDetection";
import WindowConfirmation from "../../generic-components/confirmation/windowConfirmation";
import ExperienceBuilderIcons from "../../views/experienceBuilderIcons";
import Preview from "../../components/appBar/editExperience/menus/preview";
import tippy from "tippy.js";
import Common from "../..";
import ThemeManager from "./themeManager";

import { Store } from "../../store/store";
import { destroyStepPreview, findStepGroup, initStepPreview, reInitStepSelect } from "./helpers/logic";
import { patternTypes } from "../../store/constants/experiences/uiPatternTypes";
import { getTheme } from "../../common/user-data/themes";

export default class LogicSettings {

    templateSettings;
    templateEditor;
    defaults;

    constructor(templateSettings, templateEditor) {
        this.templateSettings = templateSettings;
        this.templateEditor = templateEditor;
        this.logicSwitch = this.templateSettings.userpilotWindowContainer.querySelector("#individual-grp-switch > div#logic-switch");
        this.defaults = [patternTypes.TOOLTIP, patternTypes.DRIVEN_ACTION].includes(this.templateSettings.type) ? { type: "element-doesnt-exist", condition: "skip" } : {};    

        this.initThemeSettings();

    }

    initThemeSettings() {
        const groupTheme = getTheme(this.templateSettings.groupSettings.theme_id);
        ThemeManager.setGeneralThemeStyle(this.templateSettings.userpilotWindowContainer, groupTheme, this.templateSettings.type, false);    
    }

    appendLogicSettings() {
        let settingsNode = this.templateSettings.containerNode.querySelector("#template-current-settings");
        settingsNode.innerHTML = "";
        settingsNode.appendChild(this.templateSettings.logicSettingsNode);
    }


    disableDeleteButton() {

        // little bit of timeout to make sure the templateEditor is loaded. 
        setTimeout(() => {
            const deleteBtn = this.templateEditor.iframeContainer?.querySelector("#delete-template");
            if (!deleteBtn) {
                this.templateEditor.on("show", (event) => {
                    this.disableDeleteButton();
                })
                return false;
            }
            deleteBtn.classList.add("disabled");
            const instance = deleteBtn._tippy;
            instance.setProps({
                allowHTML: true,
            })
            instance.setContent("You can't delete a step that contains logic, as it may impact the <br> flow's functionality. Remove the logic first to proceed");
            
        }, 10)
    }

    enableDeleteButton() {
        const deleteBtn = this.templateEditor.iframeContainer?.querySelector("#delete-template");

        if (!deleteBtn) {
            return false;
        }

        deleteBtn.classList.remove("disabled");
        const instance = deleteBtn._tippy;
        instance.setContent("Delete Step");
    }

    initLogicSettingsNodeEvents(settingsNode) {
        this.LogicElementDetection = new LogicElementDetectionSettings(settingsNode, this.templateSettings);

        this.initConditionsEvents(settingsNode);
        this.initLogicActions(settingsNode);

        if (this.templateSettings.stepSettings.logic.enabled) {
            this.enableLogicSettings(settingsNode);
            this.disableDeleteButton();
            this.logicSwitch.classList.add("active-logic");
        }

    }

    initLogicActions(settingsNode) {
        const addLogic = settingsNode.querySelector(".add-logic");
        const logicContainer = settingsNode.querySelector(".logic-container");
        const deleteLogic = logicContainer.querySelector(".delete-logic");
        const reselectElementButton = settingsNode.querySelector("#reselect-element-target");

        const targetElement = (_event) => {
            this.enableLogicSettings(settingsNode);
            this.disableDeleteButton()
            this.logicSwitch.classList.add("active-logic");
            this.LogicElementDetection.showTargetElementSettings(settingsNode);
            this.LogicElementDetection.elementDetectionManager.resetManualDetectionTextIncludeFields();
            this.LogicElementDetection.elementDetectionManager.findTarget();
            this.LogicElementDetection.elementDetectionManager.initAutoDetectionEvents();
            this.LogicElementDetection.elementDetectionManager.setDetectionType("auto");

            this.saveLogicSettings(settingsNode);
        }

        deleteLogic.addEventListener("click", () => {
            WindowConfirmation({
                icon: ExperienceBuilderIcons.getDeleteIconV2(13, 16, "#A3B0B8"),
                title: "Delete logic",
                description: "Are you sure you want to delete added logic?",
                cancel_text: "Cancel",
                confirm_text: "Delete",
                confirm_callback: () => {
                    this.enableDeleteButton();
                    this.logicSwitch.classList.remove("active-logic");
                    logicContainer.style.display = "none";
                    addLogic.style.display = "";

                    this.templateSettings.stepSettings.logic = {
                        enabled: false,
                        element: {},
                        condition: {}
                    }

                    this.templateEditor.saveStepContent();
                }
            });

        });

        addLogic.addEventListener("click", () => {

            if ([patternTypes.TOOLTIP, patternTypes.DRIVEN_ACTION].includes(this.templateSettings.type)) {
                this.autoFillForTooltips(settingsNode);
            } else {
                this.LogicElementDetection.selectNewTarget(this.templateSettings.stepSettings.logic.element, "auto", targetElement, null, "logic");
            }

        });

        reselectElementButton.addEventListener("click", () => {
            this.LogicElementDetection.selectNewTarget(this.templateSettings.stepSettings.logic.element, "auto", targetElement)
        });
    }

    autoFillForTooltips(settingsNode) {
        const manual = _.clone(this.templateSettings.stepSettings.individual.detection || {});

        this.templateSettings.stepSettings.logic.enabled = true;
        this.templateSettings.stepSettings.logic.element = {
            auto: this.templateSettings.stepSettings.ea,
            manual: manual
        }
        this.templateSettings.stepSettings.logic.condition = {
            "type": this.defaults.type,
            "action": this.defaults.condition
        }

        this.enableLogicSettings(settingsNode);
        this.disableDeleteButton()
        this.logicSwitch.classList.add("active-logic");

        this.LogicElementDetection.elementDetectionManager.findTarget();
        this.LogicElementDetection.showTargetElementSettings(settingsNode);
        this.LogicElementDetection.elementDetectionManager.initAutoDetectionEvents();
        this.LogicElementDetection.elementDetectionManager.initManualDetectionEvents();
        
        if (Object.keys(manual).length){
            this.LogicElementDetection.elementDetectionManager.setDetectionType("manual");
        }

        this.saveLogicSettings(settingsNode);
    }

    enableLogicSettings(settingsNode) {
        const logicContainer = settingsNode.querySelector(".logic-container");
        const addLogicAction = settingsNode.querySelector(".add-logic");
        logicContainer.style.display = "";
        addLogicAction.style.display = "none";
    }

    saveLogicSettings(settingsNode) {
        this.templateSettings.stepSettings.logic.enabled = true;
        this.templateSettings.stepSettings.logic.condition = {
            "type": settingsNode.querySelector("#logic-condition-controller").getAttribute("value"),
            "action": settingsNode.querySelector("#logic-step-controller").getAttribute("value"),
        };

        this.templateEditor.saveStepContent()
    }

    disableLogicPanel() {
        tippy(this.logicSwitch, {
            content: `<div style="text-align: center;"> The flow must include<br>multiple steps to add step<br>logic</div>`,
            appendTo: Common.shadowRoot,
            allowHTML: true,
            placement: "bottom"
        });

        this.logicSwitch.classList.add("disabled")
    }

    initConditionsEvents(settingsNode) {
        let stepSelect = settingsNode.querySelector("#logic-step-controller"),
            stepSelectOptions = stepSelect.querySelector(".options");

        const conditionSelect = settingsNode.querySelector("#logic-condition-controller");
        const headerTitle = settingsNode.querySelector(".logic-conditions .header p:last-child");
        const testLogicController = settingsNode.querySelector(".test-logic");
        const appState = Store.getComponentState(App);
        const experienceContent = appState.experience.content;
        const stepId = this.templateSettings.stepSettings.action_id;
        const logicCondition = this.templateSettings.stepSettings.logic.condition;
        const group = findStepGroup(stepId, experienceContent);

        let steps = group.map((step, index) => (step.action_id !== stepId && { index, step })).filter(Boolean)

        if (!steps[0] && !this.templateSettings.stepSettings.logic.enabled) {
            this.disableLogicPanel();
        }

        const testLogic = () => {
            const preview = Preview({
                previewFrom: "step",
                stepId: stepId
            })

            Common.previewState = {
                stepId: stepId,
                previewText: "Testing flow logic ended"
            }

            preview(experienceContent, "step")
        }

        testLogicController.addEventListener("click", testLogic);

        const updateHeaderTitle = (value) => {
            if (value === "end") {
                headerTitle.textContent = "Then";
            } else {
                headerTitle.textContent = "Go to step";
            }
        }

        const populateStepController = () => {
            stepSelectOptions.innerHTML = "";
            let options = [];
            steps.forEach(({ index, step }) => {
                const option = document.createElement("div");
                option.setAttribute("value", step.action_id);
                option.textContent = `Step ${++index}`;

                option.addEventListener("mouseenter", (event) => initStepPreview(event.target, step.action_id, this.templateSettings.groupSettings, steps, this.templateSettings.userpilotWindowContainer, ExperienceBuilderViews));
                option.addEventListener("mouseleave", () => destroyStepPreview());
                option.addEventListener("click", () => destroyStepPreview());
                options.push(option);
                stepSelectOptions.appendChild(option);
            })

            const endFlow = document.createElement("div");
            endFlow.setAttribute("value", "end");
            endFlow.textContent = "End flow";
            stepSelectOptions.appendChild(endFlow);

            options.push(endFlow)
            const value = logicCondition?.action || steps[0]?.step?.action_id || "end";

            updateHeaderTitle(value);

            new SelectMenu(stepSelect, options, (select) => {
                updateHeaderTitle(select.getAttribute("value"));
                this.saveLogicSettings(settingsNode);
            }, value);
        }

        const populateThenController = () => {
            stepSelectOptions.innerHTML = `
                <div value = "skip">Skip Step</div>
                <div value = "end">End flow</div>
            `;

            new SelectMenu(stepSelect, null, (select) => {
                this.saveLogicSettings(settingsNode);
            }, logicCondition?.action || this.defaults.condition || "skip");
        }

        const type = logicCondition?.type || this.defaults.type;
        if (type == "element-doesnt-exist") {
            populateThenController();
        } else {
            populateStepController();
        }

        new SelectMenu(conditionSelect, null, (select) => {
            logicCondition.action = null;
            if (select.getAttribute("value") === "element-doesnt-exist") {
                headerTitle.textContent = "Then";
                ({ stepSelect, stepSelectOptions } = reInitStepSelect(stepSelect));
                populateThenController();
            } else {
                headerTitle.textContent = "Go to step";
                ({ stepSelect, stepSelectOptions } = reInitStepSelect(stepSelect));
                populateStepController();
            }
            this.saveLogicSettings(settingsNode);
        }, logicCondition?.type || this.defaults.type || "element-exist");

    }
}