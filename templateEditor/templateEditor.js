"use strict";
import tippy from "tippy.js";
import { enableScroll, disableScroll, disableIframesPointerEvents } from "../generic-utils/events.js"

import Common from "../index.js";
import WysiwigBuilder from "./wysiwyg-builder/wysiwigBuilder.js";
import SectionDragManager from "./sectionManager/sectionDragManager.js";
import SectionAdditionManager from "./sectionManager/sectionAddition.js";
import ModalSlideoutSettings from "./templateSettings/modalSlideoutSettings.js";
import TooltipSettings from "./templateSettings/tooltipSettings/tooltipSettings.js";
import DrivenSettings from "./templateSettings/tooltipSettings/drivenSettings.js";
import NativeTooltipSettings from "./templateSettings/tooltipSettings/nativeTooltipSettings.js";
import HotspotSettings from "./templateSettings/tooltipSettings/hotspotSettings.js";
import ButtonSettings from "./templateSettings/tooltipSettings/buttonSettings.js";
import BannerV2Settings from "./templateSettings/bannerV2Settings.js";
import Views from "./views/templateViews.js";
import Find from "./element/find.js";
import TemplateIcons from "./views/templateIcons.js";
import { addOpenSansFonts } from "../app-initializer/initializeCssFiles.js";
import BannerSettings from "./templateSettings/bannerSettings.js";
import ExperienceBuilderIcons from "../views/experienceBuilderIcons.js";
import { injectFont } from "../common/user-data/themes.js";

// Components
import App from "../components/app.js";
import EmptyTemplate from "./empty-template/emptyTemplate.js";
import WindowConfirmation from "../generic-components/confirmation/windowConfirmation.js";
import DropdownConfirmation from "../generic-components/confirmation/dropdownConfirmation.js";
import SaveTemplateForm from "./forms/saveTemplate.js";

// Utils
import _ from "lodash";
import JsonOperations from "../generic-utils/jsonOperations.js";
import { createID } from "../common/ids.js";
import { generateSectionIds } from "../userpilot-utils/userpilotHtml.js";
import scrollIntoView from "scroll-into-view";
import htmlTreeOperations from "../generic-utils/htmlTreeOperations.js";

// Store
import Actions from "../store/constants/actions.js";
import { Store } from "../store/store.js";

import { spotlightTypes, spotlightTypesArray } from '../store/constants/experiences/spotlightTypes';
import { patternTypes } from "../store/constants/experiences/uiPatternTypes.js";
import { RTL_LOCALE_CODES, experienceTypes } from "../store/constants/experiences/constants.js";
import { SECTIONS_IDS } from "./sectionManager/constants.js";
import { getBodyElement } from "./element/utils.js";

const EventEmitter = require('events');

const modes = {
    EDIT: "EDIT",
    PREVIEW: "PREVIEW"
}

export default class TemplateEditor {
    static iframe;

    constructor(appContainer, userPermissions) {
        this.userpilotWindowContainer = Common.shadowRoot.querySelector(".userpilot #window-container");
        TemplateEditor.iframe = Views.getViewElement("tool-editor");

        this.editorContainer = appContainer.querySelector(".userpilot #template-editor");
        this.active = false;
        this.hoveredSection = null;

        this.showPromise = null;
        this.hidePromise = null;
        this.editPermissions = userPermissions.edit_permissions;
        this.targetNotFound = false;
        this.disableScrollTimeout = null;
        TemplateEditor.eventEmitter = new EventEmitter();

        this.init();
    }

    on(event, listener) {
        TemplateEditor.eventEmitter.on(event, listener);
    }

    async init() {
        [this.iframe, this.iframeContainer, this.innerIframeContainer] = await this.prepareIframe();
    }

    getInnerContainerElement() {
        const div = document.createElement("div");
        div.classList.add("inner-container");

        return div;
    }

    /* INITIALIZATION */

    async prepareIframe() {
        this.editorContainer.innerHTML = "";
        const iframe = TemplateEditor.iframe;
        this.editorContainer.appendChild(iframe);
        const iframeContentType = iframe.contentDocument || iframe.contentWindow.document;
        return await new Promise(resolve => {
            const iframeOnload = async () => {
                const iframeHead = iframe.contentDocument.head;
                const iframeBody = iframe.contentDocument.body;
                const cssSources = [
                    Common.baseURL + "/templateEditor/wysiwyg-builder/toolbar.css",
                    Common.baseURL + "/templateEditor/wysiwyg-builder/picker.css",
                    Common.baseURL + "/templateEditor/wysiwyg-builder/gpickr.min.css",
                    Common.baseURL + "/templateEditor/wysiwyg-builder/sample.css",
                    Common.baseURL + "/templateEditor/wysiwyg-builder/emojiPicker.css",
                    Common.baseURL + "/templateEditor/views/css/sections.css",
                    Common.baseURL + '/commonCSS/responsiveMode.css',
                    Common.baseURL + "/commonCSS/theme.css",
                    Common.baseURL + "/commonCSS/animations.css",
                    Common.baseURL + "/commonCSS/userpilotTemplates.css",
                    Common.baseURL + "/commonCSS/tooltips.css",
                    Common.baseURL + "/templateEditor/views/css/iframe.css",
                    Common.baseURL + "/commonCSS/userpilotGenerics.css",
                    Common.baseURL + "/commonCSS/readModeOverrides.css"
                ];

                await Promise.all(cssSources.map(source => {
                    let cssLink = document.createElement("link");
                    cssLink.href = source;
                    cssLink.rel = "stylesheet";
                    iframeHead.appendChild(cssLink);
                    return new Promise((resolve, reject) => {
                        cssLink.addEventListener("load", resolve);
                        setTimeout(reject, 1000);
                    });
                })).catch(reject => console.warn("Userpilot: Some stylesheets failed to load", reject));

                this.prepareFontImports(iframeHead);
                addOpenSansFonts(iframeHead);

                const viewport = document.createElement("meta");
                viewport.setAttribute("name", "viewport");
                viewport.setAttribute("content", "width=device-width, initial-scale=1, minimum-scale=1")
                iframeHead.appendChild(viewport);

                let iframeContainer = document.createElement("div");
                iframeContainer.classList.add("userpilot");
                iframeContainer.classList.add(Common.isDarkThemeApplied() ? "theme-dark" : "theme-light");

                if (!this.editPermissions) iframeContainer.classList.add("read-only");

                let innerContainer = null;
                if(Common.EXPERIENCE_TYPE === experienceTypes.FLOW) {
                    innerContainer = this.getInnerContainerElement();
                    iframeContainer.appendChild(innerContainer);
                }
                iframeBody.appendChild(iframeContainer);
                disableIframesPointerEvents(iframe.contentDocument);
                resolve([iframe, iframeContainer, innerContainer]);
            }

            //run the iframeOnload if the iframe.onload event is already fired
            if (iframeContentType && iframeContentType.readyState === 'complete') {
                iframeOnload();
            } else {
                iframe.contentWindow.addEventListener('load', iframeOnload)
            }
        });
    }

    async prepareFontImports(iframeHead) {
        await Promise.all(Common.userData.fonts.map(font => injectFont(font, iframeHead)));
    }

    /* END */

    async show(step, group, localizedContent, languageCode = "default", previewMode = false) {
        await this.showPromise;
        await this.hidePromise;
        this.showTimeout = clearTimeout(this.showTimeout);
        if (!this.iframe) {
            this.showTimeout = setTimeout(() => {
                this.show(step, group, localizedContent, languageCode, previewMode);
            }, 300);
            return;
        }
        this.showPromise = new Promise(async (resolve) => {
            // The timeout fix an issue where two step can enter this code at the same time which can override the settings of each other
            setTimeout(async () => {
                const isSameStep = this.stepSettings?.action_id === step.action_id;
                this.stepSettings = step;
                this.stepLocalizedContent = localizedContent;
                this.group = group;
                this.groupSettings = group.theme_v2;
                this.stepIndex = group.steps.indexOf(step);
                this.stepType = step.type;
                this.languageCode = languageCode;
                if (!this.stepLocalizedContent?.data && ![spotlightTypes.HOTSPOT, spotlightTypes.BUTTON,].includes(this.stepType)) {
                    resolve();
                    (languageCode === "default") && EmptyTemplate(this.stepSettings, this.stepLocalizedContent);
                    return;
                }


                // if (this.hidePromise) await this.hidePromise;
                this.iframe.style.display = "block";

                // set body scale styles to match the SDK
                this.iframe.style.transform = `scale(${1 / htmlTreeOperations.getBodyScale()})`;
                this.iframe.style.transformOrigin = window.getComputedStyle(document.body).transformOrigin;

                /**
                 * This condition "!isSameStep && !previewMode && this.mode == modes.PREVIEW" is important when a click event is fired and a preview (mouseenter event) kicks in
                 * Without this condition, there's a case that happens which result in of the following:
                 * - The template settings sidebar renders the settings of the clicked step
                 * - The template that is rendered is the step template that was being previewed
                 *
                 * This case happens when trying to preview a step then trying to preview another step then clicking on it
                 */
                if (previewMode || this.mode == modes.EDIT || this.mode == null || (!isSameStep && !previewMode && this.mode == modes.PREVIEW)) {
                    if (previewMode) this.mode = modes.PREVIEW;
                    await this.prepareTemplate(this.iframeContainer, this.innerIframeContainer || this.iframeContainer);
                    this.innerIframeContainer = this.iframeContainer.querySelector(".inner-container");
                    this.prepareObjectVars();
                    Views.template = this.userpilotSlide;
                    this.templateSettings = this.initialiseSettingEditor();
                }

                if (!previewMode) {
                    if (this.mode != modes.EDIT && step.type !== experienceTypes.BANNER_v2) Store.publishAction(Actions.HIDE_BAR);
                    this.mode = modes.EDIT;
                    this.wysiwig = new WysiwigBuilder(this);
                    this.sectionAdditionManager = new SectionAdditionManager(this);
                    this.sectionDragManager = new SectionDragManager(this);
                    this.stepType !== experienceTypes.BANNER_v2 && this.initNavigationButton();
                    this.initStepController();
                    this.initTemplateController();
                    this.initSectionControllers();
                    this.initResizeListeners(this.iframeDocument);
                    tippy(this.iframeContainer.querySelectorAll("[label-on-hover]"), { appendTo: this.iframeContainer, zIndex: 1000, onTrigger: () => { } });

                    const structureConfig = {
                        subtree: true,
                        childList: true,
                        attributes: false,
                        characterData: false
                    }
                    const templateStructureObserver = new MutationObserver(() => {
                        SectionAdditionManager.readjustColumnSections(this.userpilotSlide);
                    });
                    templateStructureObserver.observe(this.userpilotSlide, structureConfig);
                }

                this.iframeContainer.removeAttribute("rtl");
                if (RTL_LOCALE_CODES.includes(languageCode)) {
                    this.userpilotSlide.querySelector(".userpilot-builder").style.direction = "RTL"
                    this.iframeContainer.setAttribute("rtl", "true");
                }
                this.userpilotWindowContainer.style.opacity = 1;
                TemplateEditor.eventEmitter.emit("show", {
                    previewMode: previewMode,
                })

                resolve();
            });
        });
        await this.showPromise;
        clearTimeout(this.disableScrollTimeout);
        !previewMode && (this.disableScrollTimeout = setTimeout(() => {
            this.disableScroll();
        }, 500));
    }

    async hide() {
        if (!this.mode) return;
        TemplateEditor.eventEmitter.removeAllListeners('show');
        if (this.mode == modes.EDIT && this.stepType !== experienceTypes.BANNER_v2) {
            const navigationButton = this.iframeContainer.querySelector("#back-to-navigation");
            navigationButton.style.transform = "translate(-50%, -100%)";
            Store.publishAction(Actions.SET_UNDER_EDIT_STEP, { step_id: null });
            Store.publishAction(Actions.UNSHIFT_RESPONSIVE_TOOLBAR);
        }

        // Hide settings sidebar
        this.templateSettings.hideSettingsWidget();

        await this.hideTemplateContainer();
        this.enableScroll();
        this.mode = null;
    }

    async hideTemplateContainer() {
        this.iframeContainer.classList.remove("editor-active");
        this.iframeContainer.style.opacity = 0;

        await new Promise(resolve => {
            setTimeout(() => {
                const clone = this.iframeContainer.cloneNode(false);
                this.wysiwig?.activeBuilder?.dismissAITooltip?.();

                if(this.innerIframeContainer) {
                    const innerContainer = this.innerIframeContainer.cloneNode(false);
                    innerContainer.style.cssText = "";
                    clone.appendChild(innerContainer);
                }

                this.iframeContainer.remove();
                this.iframeContainer = clone;
                this.iframeContainer.style.opacity = 0;
                this.iframe.contentDocument.body.appendChild(this.iframeContainer);
                this.iframe.style.display = "";
                setTimeout(resolve);
            }, 300);
        });
    }

    async refresh() {
        if (this.mode != modes.EDIT || this.mode == modes.PREVIEW) return;
        await Store.publishAction(Actions.SPAWN_TEMPLATE_EDITOR, { step_id: this.stepSettings.action_id });
    }

    async unpreview() {
        if (this.mode == modes.EDIT) console.error("Unpreview should not be called in edit mode");
        await this.hidePromise;
        await this.showPromise;

        this.hidePromise = new Promise(async (resolve) => {
            await this.scrollPromise;
            await this.fadeInPromise;

            if (this.templateSettings) {
                this.templateSettings.containerNode.style.display = "none";
                this.templateSettings.stepSectionsSidebarNode.style.display = "none";
                this.templateSettings.settingsToggle.style.display = "none";
            }

            await this.hide();
            Store.publishAction(Actions.REMOVE_INLINE_PLACEHOLDER);
            this.iframeContainer.style.background = "";
            resolve();
        });
        await this.hidePromise;
    }

    async prepareTemplate(iframeContainer, innerIframeContainer) {
        this.resetElementIframe(iframeContainer);

        if (["tooltip", "driven", "hotspot", "native", "button"].includes(this.stepType)) {
            const isAutoDetection = JsonOperations.isJsonEmpty(this.stepSettings.individual.detection);

            if (isAutoDetection) {
                const settings = {
                    manual: 0,
                    data: this.stepSettings.ea
                };
                this.targetElement = Find.lookup(settings, getBodyElement());
            } else {
                const foundTargets = Find.manualLookup(this.stepSettings.individual.detection) || [];
                const targetIndex = parseInt(this.stepSettings.individual.detection.order) - 1;
                this.targetElement = foundTargets[targetIndex] || -1;
            }
            (this.targetElement && this.targetElement !== -1) ? await this.scrollToElement() : this.targetNotFound = true;
        }

        this.buildElementIframeContent(iframeContainer, innerIframeContainer);
    }

    async scrollToElement() {
        const isEditBarHidden = Common.shadowRoot.querySelector('#experience-builder-bar').classList.contains('bar-hidden');
        const htmlElement = window.document.querySelector('html');
        const isHtmlScrollable = isEditBarHidden && (htmlElement.scrollHeight - htmlElement.offsetHeight) < 60 && 0 < (htmlElement.scrollHeight - htmlElement.offsetHeight);
        const scaledPage = htmlTreeOperations.getBodyScale() !== 1;
        this.scrollPromise = new Promise(resolve => scrollIntoView(this.targetElement, {
            time: 500,
            isScrollable: (target, defaultIsScrollable) => {
                if (scaledPage && target === window) {
                    return !isHtmlScrollable && defaultIsScrollable(target);
                } else {
                    return defaultIsScrollable(target);
                }
            }
        }, resolve));
        await this.scrollPromise;
    }

    resetElementIframe(iframeContainer) {
        this.targetNotFound = false;
        iframeContainer.classList.remove("userpilot-node-modal");
        iframeContainer.classList.remove("userpilot-node-slideout");
        iframeContainer.classList.remove("userpilot-node-tooltip");
        iframeContainer.classList.remove("userpilot-node-native");
        iframeContainer.classList.remove("userpilot-node-hotspot");
        iframeContainer.classList.remove("driven-action-hotspot");
        iframeContainer.classList.remove("userpilot-node-banner");
        iframeContainer.classList.remove("userpilot-node-banner_v2");
        iframeContainer.classList.remove("userpilot-node-button");
        iframeContainer.innerHTML = "";

        if(this.innerIframeContainer) {
            const innerContainer = this.getInnerContainerElement();
            iframeContainer.appendChild(innerContainer);
            this.innerIframeContainer = innerContainer;
        }

        this.enableScroll();
        switch (this.stepType) {
            case patternTypes.TOOLTIP:
            case patternTypes.DRIVEN_ACTION:
                iframeContainer.classList.add("userpilot-node-tooltip");
                break;

            case experienceTypes.BANNER_v2:
                iframeContainer.classList.add("userpilot-node-banner");
                iframeContainer.classList.add("userpilot-node-banner_v2");
                break;

            default:
                iframeContainer.classList.add(`userpilot-node-${this.stepType}`);
        }
    }

    buildElementIframeContent(iframeContainer) {
        const innerIframeContainer = iframeContainer.querySelector(".inner-container") || this.iframeContainer;
        const appState = Store.getComponentState(App);

        this.virtualTarget = Views.getViewElement("virtual-target", {
            icon: TemplateIcons.getTargetIcon()
        });
        const invalidMessage = Views.getView("invalid-message", {
            icon: TemplateIcons.getWarningIcon()
        });

        if (this.targetNotFound) this.targetElement = this.virtualTarget;
        const fakeSpaceEl = document.createElement("div");
        fakeSpaceEl.id = "fake-space-el";
        fakeSpaceEl.style = "visibility: hidden; height: 70px;";

        innerIframeContainer.append(
            Views.getViewElement("userpilot-slide", {
                invalidMessage: invalidMessage,
                content: this.stepLocalizedContent.data,
                classes: (appState.current_localization !== appState.default_locale) ? "localization-active" : "",
            }),
            this.virtualTarget,
        );
        [spotlightTypes.BANNER, experienceTypes.BANNER_v2].includes(this.stepType) && innerIframeContainer.append(fakeSpaceEl);

        iframeContainer.style.opacity = 1;
        this.fadeInPromise = new Promise(resolve => setTimeout(() => {
            resolve();
        }, 300));
    }

    prepareObjectVars() {
        this.iframeDocument = this.iframe.contentDocument;
        this.iframeWindow = this.iframe.contentWindow;
        this.userpilotContent = this.iframeContainer.querySelector("#content-container");
        this.userpilotSlide = this.iframeContainer.querySelector(".userpilot-slide");
        this.userpilotSlideContainer = this.iframeContainer.querySelector(".userpilot-slide-container") || this.iframeContainer.querySelector(".userpilot-slide-contaienr");
        this.userpilotBuilderContainer = this.iframeContainer.querySelector(".userpilot-builder");
        this.sections = Array.from(this.iframeContainer.querySelectorAll(".userpilot-builder-section"));
        this.reselectElement = this.iframeContainer.querySelector('#reselect-element');
    }

    setupNewSectionEvents(section) {
        this.sections.push(section);
        this.wysiwig.attachSectionEvents(section);
        this.initSectionController(section);
        this.sectionAdditionManager.attachSectionEvents(section);
        this.sectionDragManager.attachSectionEvents(section);
    }

    initialiseSettingEditor() {
        switch (this.stepType) {
            case "modal":
            case "slideout":
                return new ModalSlideoutSettings(this.stepType, this);
            case "tooltip":
                return new TooltipSettings(this.targetElement, this);
            case "driven":
                return new DrivenSettings(this.targetElement, this);
            case experienceTypes.BANNER_v2:
                return new BannerV2Settings(this.stepType, this);
            case spotlightTypes.NATIVE:
                return new NativeTooltipSettings(this.targetElement, this);
            case spotlightTypes.HOTSPOT:
                return new HotspotSettings(this.targetElement, this);
            case spotlightTypes.BUTTON:
                return new ButtonSettings(this.targetElement, this);
            case spotlightTypes.BANNER:
                return new BannerSettings(this.stepType, this);
            default:
                console.error("DevError: unsupported type of userpilot template");
        }
    }

    initStepController() {
        const controllerContainer = this.templateSettings.containerNode.querySelector("#template-controller");

        if (spotlightTypesArray.includes(this.stepType) || this.stepType === experienceTypes.BANNER_v2) return controllerContainer.classList.add("display-none");

        const groupStepSum = this.group.steps.length;
        if (groupStepSum === 1) return controllerContainer.classList.add("display-none");

        controllerContainer.querySelector("#step-index").textContent = this.stepIndex + 1;
        controllerContainer.querySelector("#step-sum").textContent = groupStepSum;
        controllerContainer.querySelector("#step-word").textContent = "Steps";

        const prevController = controllerContainer.querySelector("[value='prev']");
        const nextController = controllerContainer.querySelector("[value='next']");
        if (this.stepIndex === 0) prevController.classList.add("disabled");
        if (this.stepIndex === groupStepSum - 1) nextController.classList.add("disabled");

        const stepControllers = Array.from(controllerContainer.querySelectorAll(".step-controller"));
        stepControllers.forEach(controller => {
            controller.addEventListener("click", () => {
                switch (controller.getAttribute("value")) {
                    case "prev":
                        if (this.stepIndex > 0) {
                            Store.publishAction(Actions.SPAWN_TEMPLATE_EDITOR, {
                                step_id: this.group.steps[this.stepIndex + -1].action_id
                            });
                        }
                        break;
                    case "next":
                        if (this.stepIndex + 1 < groupStepSum) {
                            Store.publishAction(Actions.SPAWN_TEMPLATE_EDITOR, {
                                step_id: this.group.steps[this.stepIndex + 1].action_id
                            });
                        }
                }
            });
        });
    }

    /* Template controller sidebar */

    initTemplateController() {
        const controllerSidebar = Views.getViewElement("template-controller-toolbar", {
            deleteIcon: ExperienceBuilderIcons.getDeleteIconV2(13, 16, "#A3B0B8"),
            cloneIcon: TemplateIcons.getCloneIcon(),
            settingsIcon: TemplateIcons.getTemplateSettingsIcon(),
            resetIcon: TemplateIcons.getResetTemplateIcon(),
            saveIcon: TemplateIcons.getTemplateSaveIcon(),
            retargetIcon: TemplateIcons.getRetargetIcon(),
            dir: ([spotlightTypes.BANNER, experienceTypes.BANNER_v2].includes(this.stepType)) ? "hor" : "ver"
        });
        this.userpilotContent.appendChild(controllerSidebar);

        const deleteBtn = controllerSidebar.querySelector("#delete-template");
        deleteBtn.addEventListener("click", () => {
            if (this.templateSettings.stepSettings.logic.enabled) {
                return false;
            }
            WindowConfirmation({
                icon: ExperienceBuilderIcons.getDeleteIconV2(13, 16, "#A3B0B8"),
                title: "Do you want to delete this?",
                description: "This action can’t be undone. UI patterns once deleted are permanently gone",
                cancel_text: "Cancel",
                confirm_text: "Delete",
                confirm_callback: () => {
                    Store.publishAction(Actions.REMOVE_STEP, { step: this.stepSettings });
                    Store.publishAction(Actions.CLEAR_WINDOW_COMPONENTS);
                    Store.publishAction(Actions.SHOW_BAR);
                }
            });
        });

        const cloneBtn = controllerSidebar.querySelector("#clone-step");
        cloneBtn.addEventListener("click", async () => {
            const cloneStep = _.cloneDeep(this.stepSettings);
            if (cloneStep.type !== patternTypes.DRIVEN_ACTION) {
                cloneStep.content = SectionAdditionManager.addNextBackSections({
                    template: cloneStep.content,
                    isNewGroup: false,
                    stepType: cloneStep.type,
                    addNext: false,
                    addBack: true
                });
            }
            cloneStep.action_id = createID(5);
            cloneStep.content = generateSectionIds(cloneStep.action_id, cloneStep.content);
            await Store.publishAction(Actions.ADD_STEP_AFTER_STEP, { step: cloneStep, targetStep: this.stepSettings });
            Store.publishAction(Actions.SPAWN_TEMPLATE_EDITOR, { step_id: cloneStep.action_id });
        });

        const settingsBtn = controllerSidebar.querySelector("#template-settings");
        settingsBtn.addEventListener("click", () => this.templateSettings.toggleWidget());

        const resetTemplateBtn = controllerSidebar.querySelector("#reset-template");
        resetTemplateBtn.addEventListener("click", async () => {
            await Store.publishAction(Actions.CLEAR_WINDOW_COMPONENTS);
            Store.publishAction(Actions.SPAWN_TEMPLATE_PICKER, {
                type: this.stepType,
                new_group: false,
                step_settings: this.stepSettings,
                addition_callback: async (step, type, event, stepTheme = {}) => {
                    await Store.publishAction(Actions.REPLACE_STEP, { step: step, targetStep: this.stepSettings, stepTheme });
                    await Store.publishAction(Actions.SPAWN_TEMPLATE_EDITOR, { step_id: step.action_id });
                },
                close_callback: () => Store.publishAction(Actions.SPAWN_TEMPLATE_EDITOR, { step_id: this.stepSettings.action_id }),
            });
        });

        const reselectElementBtn = controllerSidebar.querySelector("#reselect-element");
        reselectElementBtn.addEventListener("click", async () => {
            this.templateSettings.selectNewTarget(this.stepSettings, "ea", this.templateSettings.retarget);
        });

        const saveTemplateBtn = controllerSidebar.querySelector("#save-template");
        SaveTemplateForm({
            parent_btn: saveTemplateBtn,
            step_settings: this.stepSettings,
            groupSettings: this.groupSettings
        });
    }

    /* Sections controllers */
    sectionControllerProps = {
        hoveredSection: null,
        editBtn: null,
        deleteBtn: null
    }
    initSectionControllers() {
        this.sectionControllerProps.editBtn = Views.getViewElement("edit-section-btn");
        this.sectionControllerProps.deleteBtn = Views.getViewElement("delete-section-btn");

        this.userpilotContent.appendChild(this.sectionControllerProps.editBtn);
        this.userpilotContent.appendChild(this.sectionControllerProps.deleteBtn);

        this.sections.forEach(section => this.initSectionController(section));
        this.disbaleEditDeleteButtonsPointerEvents();

        this.sectionControllerProps.editBtn.addEventListener("click", () => {
            if (this.hoveredSection) this.hoveredSection.dispatchEvent(new Event("click"));
        });

        DropdownConfirmation({
            parent_btn: this.sectionControllerProps.deleteBtn,
            description: "Delete this section?",
            confirm_text: "DELETE",
            spawn_callback: () => this.userpilotSlideContainer.style.pointerEvents = "none",
            hide_callback: () => this.userpilotSlideContainer.style.pointerEvents = "",
            confirm_callback: () => this.wysiwig.removeSection(this.hoveredSection)
        });
    }

    initSectionController(section) {
        section.addEventListener("mouseenter", () => this.positionEditDeleteButtons(section));
        section.addEventListener("mouseleave", this.hideEditDeleteButtons);
    }

    positionEditDeleteButtons = (section) => {
        if (section.id === SECTIONS_IDS.NEXT_BUTTON_SECTION || section.id === SECTIONS_IDS.BACK_BUTTON_SECTION) return this.disbaleEditDeleteButtonsPointerEvents();

        this.enableEditDeleteButtonsPointerEvents();
        const borderWidth = (this.userpilotSlideContainer.offsetWidth - this.userpilotSlideContainer.clientWidth) / 2;
        const borderHeight = (this.userpilotSlideContainer.offsetHeight - this.userpilotSlideContainer.clientHeight) / 2;
        this.hoveredSection = section;

        this.sectionControllerProps.editBtn.style.opacity = 1;
        this.sectionControllerProps.editBtn.style.top = ((section.offsetTop - this.userpilotSlideContainer.scrollTop) + section.offsetHeight / 2) + borderHeight;
        this.sectionControllerProps.editBtn.style.left = (section.offsetLeft + section.offsetWidth / 2) + borderWidth;

        this.sectionControllerProps.deleteBtn.style.opacity = 1;
        this.sectionControllerProps.deleteBtn.style.top = (section.offsetTop - this.userpilotSlideContainer.scrollTop) + section.offsetHeight - this.sectionControllerProps.deleteBtn.offsetHeight;
        this.sectionControllerProps.deleteBtn.style.left = section.offsetLeft + section.offsetWidth - this.sectionControllerProps.deleteBtn.offsetWidth;
    }

    hideEditDeleteButtons = () => {
        this.sectionControllerProps.editBtn.style.opacity = 0;
        this.sectionControllerProps.deleteBtn.style.opacity = 0;
    }

    disbaleEditDeleteButtonsPointerEvents = () => {
        this.sectionControllerProps.editBtn.style.pointerEvents = "none";
        this.sectionControllerProps.deleteBtn.style.pointerEvents = "none";
    }

    enableEditDeleteButtonsPointerEvents = () => {
        this.sectionControllerProps.editBtn.style.pointerEvents = "";
        this.sectionControllerProps.deleteBtn.style.pointerEvents = "";
    }

    initNavigationButton() {
        const navigationButton = Views.getViewElement("back-to-navigation");
        (this.innerIframeContainer || this.iframeContainer).appendChild(navigationButton);
        Store.publishAction(Actions.SHIFT_RESPONSIVE_TOOLBAR);

        navigationButton.addEventListener("click", () => {
            Store.publishAction(Actions.CLEAR_WINDOW_COMPONENTS);
            Store.publishAction(Actions.SHOW_BAR);
            this.iframeContainer.style.background = "";
        });
    }

    initResizeListeners(Document) {

        if (!Document.querySelector("#resize-section-btn")) {
            return false;
        }

        let isDragging = false;
        let initialX = 0;
        let initialWidth = 0;
        let initialNextWidth = 0;

        const releaseResize = () => {
            isDragging = false;
            Common.currentSection.classList.remove("dragging");
            this.iframeDocument.body.classList.remove("under-resize")
            this.sectionAdditionManager.repositionControllers(Common.currentSection)
            this.saveStepContent()
        }

        Document.querySelector("#resize-section-btn")?.addEventListener("mousedown", (_event) => {
            if (!Common.currentSection) {
                return;
            }
            Common.currentSection.classList.add('dragging');
            isDragging = true;
            this.iframeDocument.body.classList.add("under-resize")
        });

        Document.querySelector("#resize-section-btn")?.addEventListener("mouseenter", (_event) => {
            this.iframeDocument.body.classList.add("under-resize")
        });

        Document.querySelector("#resize-section-btn")?.addEventListener("mouseleave", (_event) => {
            if (isDragging) return false;
            this.iframeDocument.body.classList.remove("under-resize")
        });

        Document.addEventListener("mouseup", (_event) => {
            if (!isDragging) {
                return false;
            }

            releaseResize();
        });

        Document.addEventListener('mousemove', (event) => {

            if (isDragging) return;

            let section = event.target;

            if (!section.parentElement.classList.contains('userpilot-cols-section')) return;

            if (section === section.parentElement.lastElementChild) {
                section = section.previousElementSibling;
            }

            event.preventDefault();

            initialX = event.clientX;

            initialWidth = section.offsetWidth;
            initialNextWidth = section.nextElementSibling.offsetWidth;
        });

        Document.addEventListener('mousemove', (event) => {

            if (!isDragging) return;

            const deltaX = event.clientX - initialX;
            const nextSection = Common.currentSection.nextElementSibling;

            const newWidth = initialWidth + deltaX;
            const newNextWidth = initialNextWidth - deltaX;

            const newWidthPercentage = newWidth/this.userpilotSlideContainer.offsetWidth * 100;
            const newNextWidthPercentage = newNextWidth/this.userpilotSlideContainer.offsetWidth * 100;

            if (newWidth >= 100 && newNextWidth >= 100) {
                Common.currentSection.style.width = `${newWidthPercentage}%`;
                nextSection.style.width = `${newNextWidthPercentage}%`;
            }

            this.sectionAdditionManager.positionResizeBtn({
                currentTarget: Common.currentSection,
            })
        });

        Document.addEventListener('mouseup', () => {
            if (!isDragging) return;
            releaseResize()
        });
    }

    async saveStepContent(localizationData) {

        if (this.stepType === spotlightTypes.BUTTON) {
            return false;
        }

        this.stepLocalizedContent.edited = true;
        let tmpStepSettings = _.cloneDeep(this.stepSettings);
        if (this.userpilotBuilderContainer.innerHTML.trim()) {
            const isDefaultLocale = this.languageCode === "default";
            const content = SectionDragManager.removeDraggableAttributeOnSections(this.userpilotBuilderContainer.outerHTML);

            //avoid changing the step ref if localization content is being edited
            if (isDefaultLocale) this.stepSettings.content = content;
            this.stepLocalizedContent.data = content;

            if (isDefaultLocale) this.stepSettings.forms = Array.from(this.userpilotSlide.querySelectorAll(".userpilot-builder .userpilot-form-section #userpilot-form > .userpilot-form-label")).map(label => label.innerText);

            tmpStepSettings = {
                ...this.stepSettings,
                //persist localized content for the step reducer when localization content is being edited
                content: this.languageCode === "default" ? this.stepSettings.content : this.stepLocalizedContent.data
            }
        } else {
            this.stepSettings.content = "";
            this.stepLocalizedContent.data = "";
            if (this.languageCode === "default") this.stepSettings.forms = [];

            this.refresh();
        }
        Store.publishAction(Actions.SAVE_STEP, {
            step: tmpStepSettings,
            ...localizationData && { localizationData },
        });
    }

    enableScroll() {
        enableScroll(this.iframeWindow);
    }

    disableScroll() {
        disableScroll(this.iframeWindow);
    }

    updateHideOnMobileAttribute(sectionId) {
        const appState = Store.getComponentState(App);
        const localizationData = appState.experience.localizationData;
        const currentSection = this.sections.find(section => section.getAttribute("unit_id") === sectionId);
        const hasHideOnMobileAttribute = currentSection.hasAttribute("hide-on-mobile");

        localizationData.forEach(localizationItem => {
            const localizedStep = localizationItem.steps[this.stepSettings.action_id];
            const localizedStepContent = document.createElement("div");
            localizedStepContent.innerHTML = localizedStep.data;

            const localizedCurrentSection = localizedStepContent.querySelector(`[unit_id="${sectionId}"]`);
            hasHideOnMobileAttribute ? localizedCurrentSection?.setAttribute("hide-on-mobile", "") : localizedCurrentSection?.removeAttribute("hide-on-mobile");

            localizedStep.data = localizedStepContent.innerHTML;
        });

        return localizationData;
    }

    static spawnTemplateEditor(experienceToken, experience) {
        experienceToken &&
            experience?.settings.type === experienceTypes.BANNER_v2 &&
            Common.backgroundState.appBarMode !== "NEW_EXPERIENCE" &&
            !Common.backgroundState?.preview &&
            Store.publishAction(Actions.SPAWN_TEMPLATE_EDITOR, { step_id: experience.content.steps[0].action_id });
    }
}