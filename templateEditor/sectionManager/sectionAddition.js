import Views from "../views/templateViews.js";
import htmlTreeOperations from "../../generic-utils/htmlTreeOperations.js";
import Popup from "../../generic-utils/popup.js";
import TemplateIcons from "../views/templateIcons.js";
import { Alert } from "../../generic-components/alert.js";
import WysiwigBuilder from "../wysiwyg-builder/wysiwigBuilder.js";
import TemplateSettings from "../templateSettings/templateSettings.js";
import { patternTypes } from "../../store/constants/experiences/uiPatternTypes.js";
import { spotlightTypes } from "../../store/constants/experiences/spotlightTypes.js";
import Embeds from "../wysiwyg-builder/components/embeds/embeds.js";
import { SECTIONS_IDS } from "./constants.js";
import ExperienceBuilderViews from "../../views/experienceBuilderViews.js";
import SuggestionDialog, { SUGGESTION_TYPES } from "../../generic-components/suggestionDialog/suggestionDialog.js";
import { experienceTypes } from "../../store/constants/experiences/constants.js";
import { Store } from "../../store/store.js";
import App from "../../components/app.js";
import Common from "../../index.js";

export default class SectionAdditionManager {
    constructor(templateEditor) {
        this.templateEditor = templateEditor;
        this.sections = this.templateEditor.sections;
        this.iframeDocument = this.templateEditor.iframeDocument;
        this.userpilotContent = this.templateEditor.userpilotContent;
        this.userpilotContainer = this.templateEditor.userpilotContainer;
        this.userpilotSlide = this.templateEditor.userpilotSlide;
        this.userpilotBuilderContainer = this.templateEditor.userpilotBuilderContainer;

        this.currentHorizontalSection;
        this.currentVerticalSection;

        this.horizontalAddSection;
        this.verticalAddSection;
        this.sectionAddPopup;
        this.activeAddition;

        this.embedsPopupObj;
        this.suggestBlockDialogEl;
        this.suggestEmbedDialogEl;

        this.isBannerV2 = (this.templateEditor.stepType === experienceTypes.BANNER_v2);

        this.buildAdditionSections();
        this.sections.forEach(section => {
            this.attachSectionEvents(section);
        });
        this.attachAdditionEvents();
        this.setupAdditionPopupEvents();
    }

    buildAdditionSections() {
        this.horizontalAddSection = Views.getViewElement("add-section-btn", {
            direction: "horizontal",
            addIcon: TemplateIcons.getAddSectionIcon()
        });

        this.verticalAddSection = Views.getViewElement("add-section-btn", {
            direction: "vertical",
            addIcon: TemplateIcons.getAddSectionIcon()
        });

        this.resizeSection = Views.getViewElement("resize-section-btn", {
            direction: "vertical",
            addIcon: TemplateIcons.getResizeIcon()
        });

        this.userpilotContent.appendChild(this.horizontalAddSection);
        !this.isBannerV2 && this.userpilotContent.appendChild(this.verticalAddSection);
        !this.isBannerV2 && this.userpilotContent.appendChild(this.resizeSection);

        // Now retreive the section addition menu view
        this.sectionAddPopup = Views.getViewElement("section-addition-menu", { stepType: this.templateEditor.stepType });
    }

    setupAdditionPopupEvents() {
        var addOptions = this.sectionAddPopup.querySelectorAll(".section-icon");
        addOptions.forEach(icon => {
            if(this.templateEditor.stepType === experienceTypes.BANNER_v2 && !["text-section", "button-section"].includes(icon.id)) return;

            switch(icon.id) {
                case "embed-section":
                    const embeds = new Embeds({ onSelect: (e) => this.addNewSection(icon, {embedType: e.currentTarget.getAttribute("type")}), onBackIconClick: () => this.embedsPopupObj.closePopup() });
                    this.embedsPopupObj = new Popup(embeds.componentEl, icon, null, null, { container: this.sectionAddPopup, autoCloseOnOutsideClick: false });
                    
                    const suggestEmbedDialog =this.initSuggestDialog(embeds.componentEl.querySelector(".suggest-embed-section"), SUGGESTION_TYPES.EMBED);
                    this.suggestEmbedDialogEl = suggestEmbedDialog.componentEl;
                    break;
    
                default:
                    icon.addEventListener("click", () => this.addNewSection(icon));
            }
        });

        const suggestBlockDialog = this.initSuggestDialog(this.sectionAddPopup.querySelector(".suggest-block-btn"), SUGGESTION_TYPES.BLOCK);
        this.suggestBlockDialogEl = suggestBlockDialog.componentEl;

    }

    initSuggestDialog = (triggerEl, type) => {

        const onShow = () => {
            ExperienceBuilderViews.replaceUsingOpacity(this.sectionAddPopup, suggestionDialog.componentEl, false, () => {
                setTimeout(() => {
                    if(!this.sectionAddPopup.parentElement.contains(suggestionDialog.componentEl)) this.sectionAddPopup.parentElement.appendChild(suggestionDialog.componentEl);
                    Popup.positionPopup({
                        positionTo: (this.activeAddition == "bottom") ? this.verticalAddSection.firstElementChild : this.horizontalAddSection.firstElementChild,
                        popup: suggestionDialog.componentEl,
                        options: {
                            ...Popup.defaultOptions,
                            container: (this.activeAddition == "bottom") ? this.verticalAddSection.firstElementChild : this.horizontalAddSection.firstElementChild,
                            setDirectionPriorityCallback: () => (this.activeAddition == "bottom") ? this.verticalPopup.currentPosition : this.horizontalPopup.currentPosition,
                        },
                        directionPriority: [...Popup.directionPriority],
                    });
                    suggestionDialog.textarea.focus(); 
                });
            });
        }
        const onDismiss = (e) => ExperienceBuilderViews.replaceUsingOpacity(suggestionDialog.componentEl, this.sectionAddPopup, false);
        const suggestionDialog = new SuggestionDialog({
            triggerEl,
            type,
            onShow,
            onDismiss,
            onSend: onDismiss,
        })
        suggestionDialog.componentEl.addEventListener("click", (e) => {e.stopPropagation()});
        return suggestionDialog;
    }

    addNewSection(target, sectionVars = {}) {
        if(this.isBannerV2 && !this.checkBannerV2SectionAddition(target)) return;

        const appState = Store.getComponentState(App);
        const localizationData = (appState.experience.localizationData);
        let section;
        var temp = document.createElement("template");
        temp.innerHTML = Views.getView(SectionAdditionManager.getTemplateSectionName(target.id, this.templateEditor.stepType), sectionVars);
        section = temp.content.firstElementChild.cloneNode(true);
        //set unit_id attribute for the section to use in the localization modules
        section.setAttribute("unit_id", `${this.templateEditor.stepSettings.action_id}-${Math.floor(Math.random(1,1000) * 10000)}`);

        let success = false;
        if(this.isNextBackButtonSection) {
            success = SectionAdditionManager.insertVerticalSection(section, this.currentVerticalSection, "top");
            this.addNewSectionToTranslations({
                localizationData,
                newSection: section,
                siblingSection: this.currentVerticalSection,
                direction: "top",
            });
            this.verticalPopup.closePopup();
        } else if (this.activeAddition == "bottom") {
            success = SectionAdditionManager.insertVerticalSection(section, this.currentVerticalSection, this.activeAddition);
            this.addNewSectionToTranslations({
                localizationData,
                newSection: section,
                siblingSection: this.currentVerticalSection,
                direction: this.activeAddition,
            });
            this.verticalPopup.closePopup();
        } else {
            const buttonSection = this.isBannerV2 && this.userpilotBuilderContainer.querySelector(".userpilot-button-section");
            success = SectionAdditionManager.insertHorizontalSection(section, buttonSection || this.currentHorizontalSection, this.activeAddition, this.userpilotBuilderContainer);
            this.addNewSectionToTranslations({
                localizationData,
                newSection: section,
                siblingSection: buttonSection || this.currentHorizontalSection,
                direction: this.activeAddition,
            });
            this.horizontalPopup.closePopup();
        }

        if(success) {
            this.templateEditor.setupNewSectionEvents(section);
            this.templateEditor.saveStepContent(localizationData);
            section.click();
            SectionAdditionManager.spawnEmojiPicker(section);
        }
    }

    addNewSectionToTranslations({ localizationData, direction, newSection, siblingSection} = {}) {
        localizationData.forEach(localizationItem => {
            if(localizationItem.isDefault) return;

            const sectionCloned = newSection.cloneNode(true);
            const localizedStep = localizationItem.steps[this.templateEditor.stepSettings.action_id];
            const siblingSectionId = siblingSection.getAttribute("unit_id");
            const stepContent = document.createElement("div");
            stepContent.innerHTML = localizedStep.data;

            const localizedSiblingSection = stepContent.querySelector(`[unit_id="${siblingSectionId}"]`);
            if(!localizedSiblingSection) return console.warn(`Unable to add new section to translation [${localizationItem.code}]`);

            if(["top", "bottom"].includes(direction)) SectionAdditionManager.insertVerticalSection(sectionCloned, localizedSiblingSection, direction);
            else SectionAdditionManager.insertHorizontalSection(sectionCloned, localizedSiblingSection, direction, stepContent.querySelector(".userpilot-builder"));

            localizedStep.data = stepContent.innerHTML;
        });
    }

    checkBannerV2SectionAddition = (target) => {
        switch(target.id) {
            case "text-section":
                if(this.userpilotSlide.querySelector(".userpilot-text-section")) {
                    Alert({
                        severity: "error",
                        content: "No more than one text section can be added",
                    });
                    this.horizontalPopup.closePopup();
                    return false;
                }
                break;

            case "button-section":
                if(this.userpilotSlide.querySelectorAll(".userpilot-button-section").length === 2) {
                    Alert({
                        severity: "error",
                        content: "No more than two button sections can be added",
                    });
                    this.horizontalPopup.closePopup();
                    return false;
                }
                break;
        }

        return true;
    }

    attachSectionEvents(section) {
        section.addEventListener("mouseenter", this.positionVerticalAddBtn.bind(this));
        section.addEventListener("mouseleave", this.hideSectionAddBtns.bind(this));
        section.addEventListener("click", this.hideSectionAddBtns.bind(this));

        if(section.id === SECTIONS_IDS.NEXT_BUTTON_SECTION || section.id === SECTIONS_IDS.BACK_BUTTON_SECTION) return;
        section.addEventListener("mouseenter", this.positionHorizontalAddBtn.bind(this));
        section.addEventListener("mouseenter", this.positionResizeBtn.bind(this));
    }

    attachAdditionEvents() {
        this.horizontalAddSection.firstElementChild.addEventListener("mouseenter", this.expandSection.bind(this, this.horizontalAddSection));
        this.horizontalAddSection.firstElementChild.addEventListener("mouseleave", this.collapseSection.bind(this, this.horizontalAddSection));

        this.verticalAddSection.firstElementChild.addEventListener("mouseenter", this.expandSection.bind(this, this.verticalAddSection));
        this.verticalAddSection.firstElementChild.addEventListener("mouseleave", this.collapseSection.bind(this, this.verticalAddSection));

        this.horizontalPopup = new Popup(this.sectionAddPopup, this.horizontalAddSection.firstElementChild, this.popupSpawnCallback.bind(this, this.horizontalAddSection), this.popupCloseCallback.bind(this));
        this.verticalPopup = new Popup(this.sectionAddPopup, this.verticalAddSection.firstElementChild, this.popupSpawnCallback.bind(this, this.verticalAddSection), this.popupCloseCallback.bind(this));
    }

    positionHorizontalAddBtn(event) {
        var sliderContainer = this.userpilotSlide.querySelector('.userpilot-slide-contaienr');

        var borderWidth = (sliderContainer.offsetWidth - sliderContainer.clientWidth)/2;
        var borderHeight = (sliderContainer.offsetHeight - sliderContainer.clientHeight)/2;

        var currentSection = event.currentTarget;
        var currentSectionTop = currentSection.offsetTop - sliderContainer.scrollTop;
        var currentSectionLeft = currentSection.offsetLeft;

        if (currentSection.classList.contains("under-edit") || this.activeAddition) {
            return;
        }

        this.horizontalAddSection.style.top = currentSectionTop + borderHeight;
        this.horizontalAddSection.style.left = (currentSectionLeft + currentSection.offsetWidth) - (this.horizontalAddSection.firstElementChild.offsetWidth / 2) + borderWidth;
        this.horizontalAddSection.style.height = currentSection.offsetHeight;
        this.horizontalAddSection.style.width = this.horizontalAddSection.firstElementChild.offsetWidth;

        this.horizontalAddSection.style.visibility = "visible";
        this.horizontalAddSection.style.opacity = 1;

        this.currentHorizontalSection = currentSection;
    }

    repositionControllers(currentSection) {
        this.positionVerticalAddBtn({
            currentTarget: currentSection
        });

        this.positionHorizontalAddBtn({
            currentTarget: currentSection
        });

        this.positionResizeBtn({
            currentTarget: currentSection
        });
    }

    positionResizeBtn(event) {
        const sliderContainer = this.userpilotSlide.querySelector('.userpilot-slide-contaienr');

        const borderWidth = (sliderContainer.offsetWidth - sliderContainer.clientWidth)/2;
        const borderHeight = (sliderContainer.offsetHeight - sliderContainer.clientHeight)/2;

        const currentSection = event.currentTarget;
        const currentSectionTop = currentSection.offsetTop - sliderContainer.scrollTop;
        const currentSectionLeft = currentSection.offsetLeft;

        Common.currentSection = currentSection;


        let alignToRight = false;

        if (currentSection.parentElement.classList.contains("userpilot-cols-section") && currentSection === currentSection.parentElement.lastElementChild) {
            alignToRight = true;
            Common.currentSection = currentSection.previousElementSibling;
        }


        if (currentSection.classList.contains("under-edit") || this.activeAddition || !currentSection.parentElement.classList.contains("userpilot-cols-section")) {
            this.hideResizeBtn();
            return;
        }

        this.resizeSection.style.top = currentSectionTop + borderHeight;
        if (alignToRight) {
            this.resizeSection.style.left = (currentSectionLeft) - (this.resizeSection.firstElementChild.offsetWidth / 2) - borderWidth + 2;
        } else {
            this.resizeSection.style.left = (currentSectionLeft + currentSection.offsetWidth) - (this.resizeSection.firstElementChild.offsetWidth / 2) + borderWidth;
        }

        this.resizeSection.style.visibility = "visible";
        this.resizeSection.style.opacity = 1;
    }

    positionVerticalAddBtn(event) {
        var sliderContainer = this.userpilotSlide.querySelector('.userpilot-slide-contaienr');

        var borderWidth = (sliderContainer.offsetWidth - sliderContainer.clientWidth)/2;
        var borderHeight = (sliderContainer.offsetHeight - sliderContainer.clientHeight)/2;

        var currentSection = event.currentTarget;
        this.isNextBackButtonSection = false;

        if([SECTIONS_IDS.NEXT_BUTTON_SECTION, SECTIONS_IDS.BACK_BUTTON_SECTION].includes(currentSection.id)) this.isNextBackButtonSection = true;
        if (currentSection.classList.contains("userpilot-col-section")) {
            currentSection = currentSection.parentElement;
        }
        var currentSectionTop = (currentSection.offsetTop - sliderContainer.scrollTop) + borderHeight;
        var currentSectionLeft = currentSection.offsetLeft + borderWidth;

        if (currentSection.classList.contains("under-edit") || currentSection.querySelector(".under-edit") || this.activeAddition) {
            return;
        }

        const addButtonOffset = (this.verticalAddSection.firstElementChild.offsetHeight / 2);
        this.verticalAddSection.style.top = this.isNextBackButtonSection ? (currentSectionTop - addButtonOffset) : (currentSectionTop + currentSection.offsetHeight - addButtonOffset);
        this.verticalAddSection.style.left = currentSectionLeft;
        this.verticalAddSection.style.width = currentSection.offsetWidth;
        this.verticalAddSection.style.height = this.horizontalAddSection.firstElementChild.offsetHeight;

        this.verticalAddSection.style.visibility = "visible";
        this.verticalAddSection.style.opacity = 1;

        this.currentVerticalSection = currentSection;
    }

    expandSection(section) {
        if (this.activeAddition) {
            return;
        }
        const addIcon = section.querySelector("svg");
        addIcon.style.transform = "scale(1.2)";

        section.style.visibility = "visible";
        section.style.opacity = 1;
        section.style.background = "rgba(153, 151, 255, 0.1)";
        section.style.outline = "1px dashed #9997FF";


        const type = section.getAttribute("direction");
        (type == "horizontal") ? section.style.width = "50px": section.style.height = "50px";
    }

    collapseSection(section) {
        const addIcon = section.querySelector("svg");

        if (this.activeAddition != "right" && this.activeAddition != "bottom") {
            addIcon.style.transform = "";
            section.style.visibility = "hidden";
            section.style.opacity = 0;
        }
        section.style.background = "";
        section.style.outline = "";

        const type = section.getAttribute("direction");
        (type == "horizontal") ? section.style.width = addIcon.offsetWidth: section.style.height = addIcon.offsetHeight;
    }

    hideSectionAddBtns() {
        if (this.activeAddition == "right") {
            this.verticalAddSection.style.visibility = "hidden";
            this.verticalAddSection.style.opacity = 0;
        } else if (this.activeAddition == "bottom") {
            this.horizontalAddSection.style.visibility = "hidden";
            this.horizontalAddSection.style.opacity = 0;
            this.hideResizeBtn();
        } else {
            this.hideResizeBtn();
            this.verticalAddSection.style.visibility = "hidden";
            this.verticalAddSection.style.opacity = 0;
            this.horizontalAddSection.style.visibility = "hidden";
            this.horizontalAddSection.style.opacity = 0;
        }
    }

    hideResizeBtn() {
        this.resizeSection.style.visibility = "hidden";
        this.resizeSection.style.opacity = 0;
    }

    showResizeBtn() {
        this.resizeSection.style.visibility = "visible";
        this.resizeSection.style.opacity = 1;
    }

    popupSpawnCallback(section, popupObject) {
        const addIcon = section.querySelector("svg");
        addIcon.style.transform = "rotate(45deg) scale(1.2)";

        const type = section.getAttribute("direction");
        if (type == "horizontal") {
            this.activeAddition = "right";
        } else {
            this.activeAddition = "bottom";
        }
        this.collapseSection(section);
    }

    popupCloseCallback(popupObject) {
        this.activeAddition = null;
        this.collapseSection(this.horizontalAddSection);
        this.collapseSection(this.verticalAddSection);
        setTimeout(() => {
            ExperienceBuilderViews.replaceUsingOpacity(this.suggestBlockDialogEl, this.sectionAddPopup, false);
            this.suggestEmbedDialogEl && ExperienceBuilderViews.replaceUsingOpacity(this.suggestEmbedDialogEl, this.sectionAddPopup, false);
            this.embedsPopupObj?.popupActive && this.embedsPopupObj.closePopup();
            if(this.suggestEmbedDialogEl) this.suggestEmbedDialogEl.querySelector("textarea").value = "";
            this.suggestBlockDialogEl.querySelector("textarea").value = "";
        }, 400);
    }

    static insertVerticalSection(section, sibling, direction) {
        if (sibling.parentNode.classList.contains("userpilot-cols-section")) {
            // The horizontal section as a whole should be the drop target
            sibling = sibling.parentElement;
        }

        if (direction == "top") {
            sibling.parentNode.insertBefore(section, sibling);
        } else if (direction == "bottom") {
            htmlTreeOperations.insertAfter(sibling, section);
        }

        if (section.classList.contains("userpilot-col-section")) {
            section.style.width = "";
        }

        section.classList.remove("userpilot-col-section");
        return true;
    }

    static insertHorizontalSection(section, sibling, direction, userpilotBuilderContainer) {
        if (!sibling.parentElement.classList.contains("userpilot-cols-section")) {
            const prev = sibling.previousSibling;
            const next = sibling.nextSibling;

            const columnsSection = document.createElement("div");
            columnsSection.classList.add("userpilot-cols-section");
            columnsSection.appendChild(sibling);

            if(prev) htmlTreeOperations.insertAfter(prev, columnsSection);
            else if(next) userpilotBuilderContainer.insertBefore(columnsSection, next);
            else userpilotBuilderContainer.appendChild(columnsSection);

            sibling.classList.add("userpilot-col-section");
        } else if(!sibling.parentElement.contains(section)) {
            const rowSectionsSum = sibling.parentElement.children.length;
            if (rowSectionsSum >= 3) {
                Alert({
                    severity: "error",
                    content: "No more than three sections can be aligned"
                });
                return false;
            }
        }

        if (direction == "left") {
            sibling.parentElement.insertBefore(section, sibling);
        } else if (direction == "right") {
            htmlTreeOperations.insertAfter(sibling, section);
        }
        section.classList.add("userpilot-col-section");
        return true;
    }

    static removeEmptyColumnSections(userpilotSlide) {
        const allColumnSections = userpilotSlide.querySelectorAll(".userpilot-cols-section .userpilot-builder-section");

        allColumnSections.forEach(columnSection => {
            const columnType = WysiwigBuilder.getSectionType(columnSection);
            if(!columnType) columnSection.remove();
        })
    }

    static readjustColumnSections(userpilotSlide) {
        const allColumnSections = userpilotSlide.querySelectorAll(".userpilot-cols-section");

        allColumnSections.forEach(columnsSection => {
            if (columnsSection.children.length == 0) {
                columnsSection.remove(); 
            } else if (columnsSection.children.length == 1) {
                const onlyChild = columnsSection.children[0];
                htmlTreeOperations.insertAfter(columnsSection, onlyChild);
                columnsSection.remove();
                onlyChild.classList.remove("userpilot-col-section");
                onlyChild.style.width = "";
            }
        });
    }

    static addClassesToFormInputs(userpilotSlide) {
        const allInputSections = userpilotSlide.querySelectorAll(".userpilot-builder-section.userpilot-form-section.userpilot-input-text");
        const allLargeInputSections = userpilotSlide.querySelectorAll(".userpilot-builder-section.userpilot-form-section.userpilot-input-text-large");
        const allRadioInputSections = userpilotSlide.querySelectorAll(".userpilot-builder-section.userpilot-form-section.userpilot-input-radio");

        allInputSections.forEach(inputSection => {
            const input = inputSection.querySelector("input");
            input.classList.add("form-input");
        });

        allLargeInputSections.forEach(largeInputSection => {
            const textarea = largeInputSection.querySelector("textarea");
            textarea.classList.add("form-input");
        });

        allRadioInputSections.forEach(radioInputSection => {
            const radioInputs = radioInputSection.querySelectorAll("#radioButton");
            radioInputs.forEach(radioInput => radioInput.classList.add("form-radio-container"));
        });
    }

    static transformUserProperties(rootEl) {
        rootEl.querySelectorAll("up-container").forEach(container => {
            container.setAttribute("contenteditable", false);
    
            const personalizationNode = container.querySelector(".userpilot-personalization-node");
            if(personalizationNode && personalizationNode.querySelector("span")) {
                personalizationNode.setAttribute("type", "user");
                personalizationNode.setAttribute("active", "1");
                personalizationNode.innerHTML = `{{${personalizationNode.querySelector("span").textContent}}}`;
            }
        });
    }

    static getTemplateSectionName(sectionName, stepType) {
        switch(sectionName) {
            case "button-section":
                return [patternTypes.DRIVEN_ACTION, spotlightTypes.NATIVE].includes(stepType) ? "tooltip-button-section" : `${stepType}-${sectionName}`;

            default:
                return sectionName;
        }
    }

    static addNextBackSections({ template, stepType, isNewGroup = true, addNext = false, addBack = false }) {
        if(!template) return "";

        const div = document.createElement("div");
        div.innerHTML = template;

        const userpilotBuilder = div.querySelector(".userpilot-builder");
        const nextButton = div.querySelector(`#${SECTIONS_IDS.NEXT_BUTTON_SECTION}`);
        const backButton = div.querySelector(`#${SECTIONS_IDS.BACK_BUTTON_SECTION}`);

        if(addNext && !nextButton ) {
            const buttonEl = Views.getNextBackButtonsViewElement("next", stepType);
            TemplateSettings.addProgressionButton("mext", buttonEl, userpilotBuilder);
        }

        if(addBack && !backButton && !isNewGroup) {
            const buttonEl = Views.getNextBackButtonsViewElement("back", stepType);
            TemplateSettings.addProgressionButton("back", buttonEl, userpilotBuilder);
        }

        return div.innerHTML;
    }

    static spawnEmojiPicker(section) {
        section?.classList.contains("userpilot-emoji-section") && setTimeout(() => {
            section.dispatchEvent(new CustomEvent("open-emoji-picker"));
        }, 200);
    }
}