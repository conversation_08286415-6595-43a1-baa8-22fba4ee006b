import { Alert } from "../../generic-components/alert.js";
import htmlTreeOperations from "../../generic-utils/htmlTreeOperations.js";
import { experienceTypes } from "../../store/constants/experiences/constants.js";
import SectionAdditionManager from "./sectionAddition.js";

export default class SectionDragManager {
    constructor(templateEditor) {
        this.templateEditor = templateEditor
        this.contentWindow = templateEditor.iframeWindow;
        this.sections = this.templateEditor.sections;
        this.userpilotBuilderContainer = this.templateEditor.userpilotBuilderContainer;
        this.userpilotContainer = this.templateEditor.userpilotContainer;
        this.isBannerV2 = (this.templateEditor.stepType === experienceTypes.BANNER_v2);

        this.dragMode;
        this.currentDraggableSection;
        this.dropTarget;
        this.dropDirection;

        this.sections.forEach(section => {
            this.attachSectionEvents(section);
        });
    }

    attachSectionEvents(section) {
        if (section.id === "userpilot-next-button" || section.id === "userpilot-back-button") {
            section.setAttribute("draggable", false);
            return;
        }

        if (section.classList.contains("userpilot-text-section") && this.isBannerV2) {
            return;
        }

        section.setAttribute("draggable", true);
        section.addEventListener("dragstart", this.setDragSection.bind(this));
        section.addEventListener("dragover", this.dragOverSection.bind(this));
        section.addEventListener("dragleave", this.resetSectionBorders.bind(this));
        section.addEventListener("dragend", SectionDragManager.clearSectionsBorders.bind(this));
        section.addEventListener("drop", this.dropSection.bind(this));
    }

    static removeDraggableAttributeOnSections(template) {
        const div  = document.createElement("div");
        div.innerHTML = template;

        div.querySelectorAll(".userpilot-builder-section").forEach(section => section.removeAttribute("draggable"));
        return div.innerHTML;
    }

    setDragSection(event) {
        this.currentDraggableSection = event.currentTarget;
        this.dragMode = "all";
        this.previousElementSibling = this.currentDraggableSection.previousElementSibling;
        this.nextElementSibling = this.currentDraggableSection.nextElementSibling;
        //use setTimeout as a workaround for chrome bug that causing the dragend to be fired automatically when dom manipulation is happening in the dragstart
        setTimeout(() => {
            // Set borders for all sections except the section we are currently dragging
            this.sections.forEach(section => {
                if(section === this.currentDraggableSection || (this.isBannerV2 && section.classList.contains("userpilot-text-section"))) {
                    return;
                }
                section.style.borderStyle = "solid";
                section.style.borderWidth = this.isBannerV2 ? "0 10px 0 10px" : "10px";
                section.style.borderColor = "rgba(127, 127, 228, 0.16)";
            });
        });
    }

    static setDragHorizontalSection(event) {
        // Get the columns section, which comes after the drag group button
        this.currentDraggableSection = event.currentTarget.parentNode;
        this.dragMode = "vertical";
        event.dataTransfer.setDragImage(this.currentDraggableSection, 0 , 0);

        if(this.currentDraggableSection.classList.contains("userpilot-cols-section")) {
            console.error("DevError: The drag group element must be succeeded by a column section")
        }

        // Set  top and bottom borders for all sections except the section we are currently dragging
        this.sections.forEach(section => {
            if(section === this.currentDraggableSection || this.isBannerV2) {
                return;
            }
            section.style.borderTopStyle = "solid";
            section.style.borderBottomStyle = "solid";
            section.style.borderTopWidth = "10px";
            section.style.borderBottomWidth = "10px";
            section.style.borderColor = "rgba(127, 127, 228, 0.16)";
        });
    }
   
    dragOverSection(event) {
        event.preventDefault();
        // Make sure we skip firing this event if the event's target is the section we are dragging
        if(event.currentTarget !== this.currentDraggableSection) {
            this.dropTarget = event.currentTarget;
            var direction;
            var boundingRect = this.dropTarget.getBoundingClientRect();
            var target = {
                top: this.contentWindow.scrollY + boundingRect.top,
                bottom: this.contentWindow.scrollY + boundingRect.bottom,
                left: this.contentWindow.scrollX + boundingRect.left,
                right:this.contentWindow.scrollX + boundingRect.right,
            }

            target.centerY = (target.top + target.bottom) / 2;
            target.centerX = (target.left + target.right) / 2;

            var mouseY = event.pageY;
            var mouseX = event.pageX;
            
            if(mouseY < (target.centerY + target.top) * 0.50
            && mouseY >= target.top)
            {
                direction = "top";
            }
            else if(mouseY > (target.centerY + target.bottom) * 0.50
            && mouseY < target.bottom)
            {
                direction = "bottom";
            }
            else if(mouseX > (target.centerX + target.right) * 0.50
            && mouseX <= target.right)
            {
                direction = "right";
            }
            else if(mouseX < (target.centerX + target.left) * 0.50
                && mouseX >= target.left)
            {
                direction = "left";
            }
            else
            {
                direction = "center";
            }

            if(!this.dropDirection || this.dropDirection != direction) {
                this.dropDirection = direction;
                this.setSectionDirectionBorders(this.dropTarget, this.dropDirection);
            }
        }
        else {
            this.dropTarget = null;
        }
    }

    dropSection(event) {
        event.preventDefault();
        if(event.currentTarget === this.currentDraggableSection) {
            return;
        }
        
        if(event.currentTarget !== this.dropTarget) {
            console.error("DevError: the drop event target is not the same as the dropover target");
        }

        if(this.dragMode == "vertical" && (this.dropDirection == "right" || this.dropDirection == "left")) {
            return;
        }

        let success = false;
        switch(this.dropDirection) {
            case "top":
            case "bottom":
                success = SectionAdditionManager.insertVerticalSection(this.currentDraggableSection, this.dropTarget, this.dropDirection);
                break;
            case "left":
            case "right":
                success = this.isBannerV2 ? this.addSectionForBannerV2() : SectionAdditionManager.insertHorizontalSection(this.currentDraggableSection, this.dropTarget, this.dropDirection, this.userpilotBuilderContainer);
                break;
            default:
                break;
        }

        setTimeout(() => success && this.templateEditor.saveStepContent(), 100);
    }

    addSectionForBannerV2 = () => {
        const isTextSectionDragged = this.currentDraggableSection.classList.contains("userpilot-text-section");
        const isButtonSectionTarget = this.dropTarget.classList.contains("userpilot-button-section");
        const nextToDropSection = (this.dropDirection === "left") ? this.dropTarget.previousElementSibling : this.dropTarget.nextElementSibling;

        if(isTextSectionDragged && isButtonSectionTarget && nextToDropSection?.classList.contains("userpilot-button-section")) {
            Alert({
                severity: "error",
                content: "The text section can't be aligned between two buttons sections",
            });
            return false;
        }   

        const success = SectionAdditionManager.insertHorizontalSection(this.currentDraggableSection, this.dropTarget, this.dropDirection, this.userpilotBuilderContainer);
        if(!success) return false;

        const isPreviousSectionButton = this.currentDraggableSection.previousElementSibling?.classList.contains("userpilot-button-section");
        const isNextSectionButton =this.currentDraggableSection.nextElementSibling?.classList.contains("userpilot-button-section");
        const numberOfButtonSection = this.userpilotBuilderContainer.querySelectorAll(".userpilot-button-section").length;

        if(numberOfButtonSection > 1 && !isPreviousSectionButton && !isNextSectionButton) {
            if (this.previousElementSibling?.classList.contains("userpilot-button-section")) {
                this.currentDraggableSection.parentElement.insertBefore(this.previousElementSibling, this.currentDraggableSection);
            } else if (this.nextElementSibling?.classList.contains("userpilot-button-section")) {
                htmlTreeOperations.insertAfter(this.currentDraggableSection, this.nextElementSibling);
            }
        }

        return true;
    }

    setSectionDirectionBorders(section, direction) {

        this.resetBorderColors(section);

        if(this.isBannerV2 && (direction == "top" || direction == "bottom")) return;
        if(this.dragMode == "vertical" && (direction == "right" || direction == "left")) {
            return;
        }

        switch(direction) {
            case "top":
                section.style.borderTopWidth = "20px";
                section.style.borderTopColor = "blue";
                break;
            case "bottom":
                section.style.borderBottomWidth = "20px";
                section.style.borderBottomColor = "blue";
                break;
            case "left":
                section.style.borderLeftWidth = "20px";
                section.style.borderLeftColor = "blue";
                break;
            case "right":
                section.style.borderRightWidth = "20px";
                section.style.borderRightColor = "blue";
                break;
            default:
                break;
        }
    }

    resetSectionBorders(event) {
        event.preventDefault();
        var section = event.currentTarget;
        this.resetBorderColors(section);
        this.dropDirection = "";
    }

    resetBorderColors(section) {
        if(!this.isBannerV2) {
            section.style.borderBottomColor = "rgba(127, 127, 228, 0.16)";
            section.style.borderTopColor = "rgba(127, 127, 228, 0.16)";
            section.style.borderTopWidth = "10px";
            section.style.borderBottomWidth = "10px";
        }

        if(this.dragMode != "vertical") {
            section.style.borderRightColor = "rgba(127, 127, 228, 0.16)";
            section.style.borderLeftColor = "rgba(127, 127, 228, 0.16)";
            section.style.borderLeftWidth = "10px";
            section.style.borderRightWidth = "10px";
        }
    }

    static clearSectionsBorders() {
        this.sections.forEach(section => {
            if(section === this.currentDraggableSection) {
                return;
            }
            section.style.border = "";
        });
    }
}