import ElementTreeConstructor from "./element/make.js";
import Element from "./element/element.js";
import Common from "../index.js";
import JsonOperations from "../generic-utils/jsonOperations.js";
import ExperienceBuilderViews from "../views/experienceBuilderViews.js";
import { Alert } from "../generic-components/alert.js";
import { findFirstMeaningfullElement, changeTargetIfNeedBe, removeCursor, initCursor } from "../components/menus/labeledEvents/helpers.js";
export default class ElementPicker {
    Documents = [];
    shiftKeyDown = 0;
    constructor(saveToObject, key, callbackClassObject, callbackOnFinish, callbackOnFinishParams, callbackOnCancel = null, options = {}) {
        this.elementHighlighter = document.getElementById("userpilot-hovered-mask");
        this.saveToObject = saveToObject;
        this.key = key;
        this.callbackOnFinish = callbackOnFinish;
        this.callbackOnFinishParams = callbackOnFinishParams;
        this.callbackClassObject = callbackClassObject;
        this.callbackOnCancel = callbackOnCancel;
        this.isLabeledEvent = options?.isLabeledEvent;
        this.currentTarget;
        this.highlightTimeout;

        this.showHighlightArea();
        // Make references to event handlers so the references are used when the event listeners are removed
        this.highlightElementRef = this.highlightElement.bind(this);
        this.setElementRef = this.setElement.bind(this);
        this.handleKeyupEventsRef = this.handleKeyupEvents.bind(this);
        this.handleKeydownEventsRef = this.handleKeydownEvents.bind(this);

        //blur the shadow root container in case elementPicker triggered form inside the iframe
        document.querySelector('up-cont#userpilot-root').blur()
        this.attachSelectorEvents();
        this.attachIframeListeners();
    }
    attachIframeListeners(Document = document) {
        const userpilotDocumentStyles = ExperienceBuilderViews.getViewElement("userpilot-chrome-extension-css");
        Array.from(Document.querySelectorAll('iframe')).forEach((iframe) => {
            let iframeDocument;
            try {
                iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
            } catch (e) {
                return;
            }

            if(!iframeDocument.head || !iframeDocument.body) return console.warn("An iframe is missing head or body elements");

            if(this.Documents.includes(iframeDocument)) return;
            this.Documents.push(iframeDocument);

            this.attachSelectorEvents(iframeDocument);
            iframeDocument.head.appendChild(userpilotDocumentStyles)
            if (iframeDocument.readyState == 'complete') {
                this.attachIframeListeners(iframeDocument);
            } else {
                iframeDocument.addEventListener("load", function() {
                    this.attachIframeListeners(iframeDocument);
                });
            }
        })
    }

    attachShadowDomListeners(shadowRoot) {
        if(this.Documents.includes(shadowRoot) || shadowRoot.host.id === "userpilot-root") return;

        const userpilotDocumentStyles = shadowRoot.querySelector("#userpilot-chrome-extension-css") || ExperienceBuilderViews.getViewElement("userpilot-chrome-extension-css");
        userpilotDocumentStyles.remove();

        this.attachSelectorEvents(shadowRoot);
        if (shadowRoot.querySelector("iframe")) {
            this.attachIframeListeners(shadowRoot);
        }
        shadowRoot.appendChild(userpilotDocumentStyles)
    }

    attachSelectorEvents(Document = document) {
        this.Documents.push(Document)
        initCursor(Document);
        Document.addEventListener("mousemove", this.handleMouseMove, true);
        Document.addEventListener("mouseover", this.highlightElementRef, true);
        Document.addEventListener("click", this.setElementRef, true);
        Document.addEventListener("mouseup", this.stopAndPreventEvent, true);
        Document.addEventListener("mousedown", this.stopAndPreventEvent, true);
        Document.addEventListener("keyup", this.handleKeyupEventsRef, true);
        Document.addEventListener("keydown", this.handleKeydownEventsRef, true);
    }

    /**
     * This is to handle a case where the shift key is being pressed and then failing to
     * detect when it was released. Such as, opening a native select menu, or clicking
     * a button while the shift key is pressed.
     */
    handleMouseMove = (e) => !e.shiftKey && this.shiftKeyDown && this.handleShiftKeyup();

    stopAndPreventEvent = (e) => {
        e.preventDefault();
        e.stopPropagation();
    }

    highlightElement(event) {
        let target = event.target;

        this.validTarget = true;

        if (this.shiftKeyDown) return false;

        if (this.isLabeledEvent && target && !target.shadowRoot) {
            const cursorContainers = target.getRootNode() instanceof DocumentFragment ? this.Documents.filter(Document => Document.host) : [target.ownerDocument];
            target = changeTargetIfNeedBe(event);
            
            cursorContainers.forEach(Document => removeCursor(Document));
            
            const payload = findFirstMeaningfullElement(target, true);

            cursorContainers.forEach(Document => initCursor(Document));
            
            target = payload.target
            if (!payload.valid) {
                this.highlightTimeout = setTimeout(() => {
                    Element.highlightElement(target, undefined, false, false);
                }); 
                this.currentTarget = target;
                this.validTarget = false;       
                return false
            }
        }

        this.mouseEvent = event;
        if (this.currentTarget === target) return;
        if(!target) return;

        this.currentTarget = target;
        if(target.shadowRoot) this.attachShadowDomListeners(target.shadowRoot);

        clearTimeout(this.highlightTimeout);
        this.highlightTimeout = setTimeout(() => {
            Element.highlightElement(target);
        });
    }

    setElement(event) {
        if (this.validTarget === false) {
            Alert({severity: "error", content: "An element must be either clickable, text input, or a form."});
            this.stopAndPreventEvent(event);
            return false;
        }

        // Handle cannot set property target of #<Event> which has only a getter, as in labled events we are skipping elements.
        let targetEvent = event;

        Common.ELEMENT_PICKER_EVENT_REF = event;

        let target = event.target.shadowRoot ? event.composedPath()[0] : event.target;

        // Make sure the element target is not part of the Userpilot app
        if(Common.shadowRoot.contains(target) || target.id == "userpilot") {
            return;
        }

        if (this.isLabeledEvent) {
            const cursorContainers = target.getRootNode() instanceof DocumentFragment ? this.Documents.filter(Document => Document.host) : [target.ownerDocument];
            cursorContainers.forEach(Document => removeCursor(Document));

            target = findFirstMeaningfullElement(changeTargetIfNeedBe({
                target
            }), true).target;
            targetEvent = {
                target: target
            }

            cursorContainers.forEach(Document => initCursor(Document));
        }

        this.stopAndPreventEvent(event);

        const elementTree = JSON.stringify(ElementTreeConstructor.getElementAttrs(target));
        Element.unhighlightElement();

        this.key && JsonOperations.setObjectValue(this.saveToObject, [this.key], elementTree);

        this.destruct();
        if (this.callbackClassObject) {
            this.callbackOnFinish.apply(this.callbackClassObject, this.callbackOnFinishParams);
        } else {
            this.callbackOnFinish(targetEvent, elementTree);
        }
        
    }

    handleKeyupEvents(event) {
        event.preventDefault();
        event.stopPropagation();

        switch(event.key) {
            case "Escape":
                this.destruct();
                if(this.callbackOnCancel) this.callbackOnCancel();
                break;
            case "Shift":
                this.handleShiftKeyup();
                break;
        }
    }
    
    handleShiftKeyup = () => {
        this.shiftKeyDown = 0;
        this.Documents.forEach(Document => {
            Document.addEventListener("click", this.setElementRef, true);
            Document.addEventListener("mouseup", this.stopAndPreventEvent, true);
            Document.addEventListener("mousedown", this.stopAndPreventEvent, true);
            initCursor(Document);
        });
        this.showHighlightArea();
        this.mouseEvent && this.highlightElement(this.mouseEvent);
    }

    handleKeydownEvents(event) {
        if(event.key === "Shift") {
            event.preventDefault();
            event.stopPropagation();
            this.shiftKeyDown = 1;
            this.Documents.forEach(Document => {
                Document.removeEventListener("click", this.setElementRef, true);
                Document.removeEventListener("mouseup", this.stopAndPreventEvent, true);
                Document.removeEventListener("mousedown", this.stopAndPreventEvent, true);
                
                removeCursor(Document);
            });
            this.hideHighlightArea();
        }
    }

    destruct() {
        Element.unhighlightElement();
        this.Documents.forEach(Document => {
            Document.removeEventListener("mousemove", this.handleMouseMove, true);
            Document.removeEventListener("mouseover", this.highlightElementRef, true);
            Document.removeEventListener("click", this.setElementRef, true);
            Document.removeEventListener("mouseup", this.stopAndPreventEvent, true);
            Document.removeEventListener("mousedown", this.stopAndPreventEvent, true);
            Document.removeEventListener("keyup", this.handleKeyupEventsRef, true);
            Document.removeEventListener("keydown", this.handleKeydownEventsRef, true);    

            
            removeCursor(Document);
        })        
        this.hideHighlightArea();
    }
    showHighlightArea() {
        this.elementHighlighter.style.display = "block";
    }
    hideHighlightArea() {
        this.elementHighlighter.style.display = "none";
    }
}