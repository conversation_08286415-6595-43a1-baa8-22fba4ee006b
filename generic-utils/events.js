import Actions from "../store/constants/actions";
import { Store } from "../store/store";

export const disableScroll = (win = window) => {
    win.addEventListener('wheel', stopBubbling, { passive: false });
    window.currentOffset = window.pageYOffset;
    window.addEventListener('scroll', preventScroll)
}

export const enableScroll = (win = window) => {
    win.removeEventListener('wheel', stopBubbling, { passive: false });
    window.removeEventListener('scroll', preventScroll);
}

const preventScroll = () => {
    window.scrollTo(0, window.currentOffset);
}

const stopBubbling = (event) => {
    event.stopPropagation();
}

export const disableIframesPointerEvents = (rootNode) => {
    if(!rootNode) return;

    const bubbleEvents = ["mousedown", "mouseup", "dragend"];

    let iframes = [];
    const disbalePointerEvents = (points = { x: 0, y: 0 }) => {
        const elements = document.elementsFromPoint(points.x, points.y);
        iframes = Array.from(elements).filter(element => element.tagName === "IFRAME");
        iframes.forEach(iframe => !iframe.classList.contains("__userpilot-website-iframe__") && iframe.classList.add('__userpilot-disable-pointer-events__'));
    }

    const enablePointerEvents = () => {
        iframes.forEach(iframe => iframe.classList.remove('__userpilot-disable-pointer-events__'));
        iframes = [];
    }

    bubbleEvents.forEach(eventName => {
        rootNode.addEventListener(eventName, (event) => {
            switch(event.type) {
                case "mousedown":
                    disbalePointerEvents({ x: event.x, y: event.y });
                    break;
                case "mouseup":
                case "dragend":
                    enablePointerEvents();
                    break;
            }
        });
    });
}

const onShiftKeyPress = (event) => {
    if (event.key == 'Shift') {
        Store.publishAction(Actions.SHIFT_KEY_PRESSED);
    }
}

export const initShiftListener = () => {
    document.addEventListener('keyup', onShiftKeyPress);
}

export const removeShiftListener = () => {
    document.removeEventListener('keyup', onShiftKeyPress);
}
