import ExperienceBuilderIcons from "./experienceBuilderIcons.js";
import Common from "../index.js";
import UserCompnayProperties from "../generic-components/userProperties/userCompnayProperties.js";
import { PROPERTIES_TYPES } from "../generic-components/userProperties/constants.js";
import { EXPERIENCE_STATUSES } from "../store/constants/experiences/constants.js";
export default class ExperienceBuilderViews {

    static getView(view, vars = {}) {
        var template = Common.shadowRoot.getElementById(view + "Temp");
        var element = template.content.firstElementChild.cloneNode(true);
        return element.outerHTML.formatUnicorn(Object.assign({}, this.getViewVars(view), vars));
    }

    static getViewElement(view, vars = {}) {
        let el = document.createElement("template");
        el.innerHTML = this.getView(view, vars);
        return el.content.firstElementChild.cloneNode(true);
    }

    static getViewVars(view) {
        switch (view) {
            case "experiences-list-menu":
                return this.getExperiencesListVars();

            case "template-builder":
                return {
                    closeIcon: ExperienceBuilderIcons.getCloseIcon(),
                    addIcon: ExperienceBuilderIcons.getAddIcon()
                };
            case "experiences-list-select":
                return {
                    lightUserpilotIcon: ExperienceBuilderIcons.getLightUserpilotIcon(),
                    darkUserpilotIcon: ExperienceBuilderIcons.getDarkUserpilotIcon(),
                    arrowDownIcon: ExperienceBuilderIcons.getArrowDownIcon()
                };
            case "create-experience-button":
                return {
                    addIcon: ExperienceBuilderIcons.getAddIcon()
                };
            case 'saved-view':
                return {
                    cloudIcon: ExperienceBuilderIcons.getCloudIcon()
                };
            case "exit-builder":
                return {
                    exitIcon: ExperienceBuilderIcons.getExitBuilderIcon()
                };
            case "navigate-to-page":
                return {
                    navigateIcon: ExperienceBuilderIcons.getNavigateIcon()
                };
            case "initialise-experience":
                return {
                    addIcon: ExperienceBuilderIcons.getAddIcon()
                };
            case "select-experience-type-container":
                return this.getExperienceTypesVars();
            case "add-ui-pattern-menu":
                return this.getUIPatternMenuVars();
            case "add-spotlight-menu":
                return this.getSpotlightMenuVars();
            case "group-manager":
                return {
                    addIcon: ExperienceBuilderIcons.getAddIcon()
                };
            case "add-ui-pattern-menu-v2":
                return {
                    addUiPatternMenu: this.getView("add-ui-pattern-menu"),
                        addToGroupIcon: ExperienceBuilderIcons.getAddToGroupIcon()
                };
            case "step-group":
                return {
                    dragIcon: ExperienceBuilderIcons.getDragIcon(),
                        addToGroupIcon: ExperienceBuilderIcons.addToGroupBarIcon()
                };
            case "localization-settings":
                return this.getLocalizationIcons();
            case "tooltip-targeting-help-message":
                return {
                    icon: ExperienceBuilderIcons.getNavigationIcon()
                }
            case "empty-content":
                return {
                    icon: ExperienceBuilderIcons.getEmptyImage()
                }
            case "templates-thumbnails-container":
                return this.getTemplatesThumbnailsVars();

            case "builder-loading":
                return this.getBuilderLoadingVars();

            case "responsive-controller-toolbar":
                return {
                    desktopIcon: ExperienceBuilderIcons.getDesktopIcon(),
                    mobileIcon: ExperienceBuilderIcons.getMobileIcon(),
                };
            default:
                return;
        }
    }

    static removeUsingOpacity(element) {
        let transitionDuration = getComputedStyle(element).transitionDuration;
        transitionDuration = parseFloat(transitionDuration) * 1000;

        element.style.opacity = 0;

        setTimeout(() => {
            element.remove();
        }, transitionDuration + 50);
    }

    static addUsingOpacity(container, element, index = 0, resolve = null) {
        if (container.children.length > 0) container.insertBefore(element, container.children[index]);
        else container.appendChild(element);

        setTimeout(() => {
            element.style.visibility = "visible";
            element.style.opacity = 1;
            element.style['z-index'] = 999;
            if(resolve) resolve();
        });
    }

    static async replaceUsingOpacity(element, newElement, remove = true, resolve = null) {
        const parentContainer = element.parentElement;
        const delay = async (millis) => {
            return new Promise(resolve => setTimeout(resolve, millis))
        }

        let transitionDuration = getComputedStyle(element).transitionDuration;
        transitionDuration = parseFloat(transitionDuration) * 1000;

        newElement.style.opacity = 0;
        element.style.opacity = 0;

        await delay(transitionDuration);

        if (remove) {
            const elementPosition = Array.from(parentContainer.children).indexOf(element);
            element.remove();
            this.addUsingOpacity(parentContainer, newElement, elementPosition, resolve);
        } else {
            element.style.display = "none";
            newElement.style.display = "";
            setTimeout(() => {
                newElement.style.opacity = 1;
                if(resolve) resolve();
            }, 50);
        }
    }

    static getExperiencesListVars() {
        return {
            searchIcon: ExperienceBuilderIcons.getSearchIcon(),
            filterIcon: ExperienceBuilderIcons.getFilterIcon(),
            editableIcon: ExperienceBuilderIcons.getExperienceSettingsIcon(),
            arrowDownIcon: ExperienceBuilderIcons.getArrowDownIcon(),
            addIcon: ExperienceBuilderIcons.getAddIcon(),
            exitIcon: ExperienceBuilderIcons.getExitBuilderIcon(),
        }
    }

    static getExperienceTypesVars() {
        return {
            closeIcon: ExperienceBuilderIcons.getCloseIcon(),
        };
    }

    static getUIPatternMenuVars() {
        const modalIcon = ExperienceBuilderIcons.getModalPatternIcon();
        const slideoutIcon = ExperienceBuilderIcons.getSlideoutPatternIcon();
        const tooltipsIcon = ExperienceBuilderIcons.getTooltipsPatternIcon();
        const drivenActionIcon = ExperienceBuilderIcons.getDrivenActionPatternIcon();
        const addToGroupIcon =  ExperienceBuilderIcons.getAddToGroupIcon()

        return {
            modalIcon: modalIcon,
            slideoutIcon: slideoutIcon,
            tooltipsIcon: tooltipsIcon,
            drivenActionIcon: drivenActionIcon,
            addToGroupIcon: addToGroupIcon
        };
    }

    static getSpotlightMenuVars() {
        const tooltipsIcon = ExperienceBuilderIcons.getTooltipsPatternIcon();
        const hotspotIcon = ExperienceBuilderIcons.getHotspotIcon();
        const bannerIcon = ExperienceBuilderIcons.getBannerIcon();
        const buttonIcon = ExperienceBuilderIcons.getButtonIcon();

        return {
            tooltipsIcon: tooltipsIcon,
            hotspotIcon: hotspotIcon,
            bannerIcon: bannerIcon,
            buttonIcon: buttonIcon
        };
    }

    static getThemeOptionElement(themeTitle, themeId, generalThemeData) {
        let themeOptionElement = this.getViewElement("theme-select-option");
        themeOptionElement.querySelector(".theme-title").innerText = themeTitle?.trim();
        themeOptionElement.setAttribute("value", themeId);

        const themeColorsContainer = themeOptionElement.querySelector(".theme-colors");
        const primaryColors = [generalThemeData.general.background, generalThemeData.general.text, generalThemeData.button.background];
        primaryColors.forEach(color => {
            let colorElement = document.createElement("div");
            colorElement.style.background = color;
            themeColorsContainer.appendChild(colorElement);
        });

        return themeOptionElement;
    }

    static getThemeSelectOptions(userThemes) {
        const themeSelectElements = [];
        userThemes.themes.forEach(theme => {
            const themeOptionElement = this.getThemeOptionElement(theme.title, theme.theme_id, theme.theme_data.color.general);
            themeSelectElements.push(themeOptionElement);
        });
        return themeSelectElements;
    }

    static getCategoriesSelectOptions(categories) {
        return categories.map(category => {
            const categoryOptionElement = document.createElement("div");
            categoryOptionElement.textContent = category;
            categoryOptionElement.setAttribute("value", category);
            return categoryOptionElement;
        });
    }

    static getFoldersSelectOptions(folders) {
        return folders.map(folder => {
            const folderOptionElement = document.createElement("div");
            folderOptionElement.textContent = folder.title;
            folderOptionElement.setAttribute("value", folder.id);
            return folderOptionElement;
        });
    }

    static getUserAttributeSelectOptions(type = PROPERTIES_TYPES.USER) {
        const attributes = (type === PROPERTIES_TYPES.USER) ? UserCompnayProperties.getUserProperties() : UserCompnayProperties.getCompanyProperties();

        return attributes.map(attribute => {
            const attributeOptionElement = document.createElement("div");
            attributeOptionElement.textContent = attribute.display_name;
            attributeOptionElement.setAttribute("value", "{" + attribute.legacy_key + "}");
            return attributeOptionElement;
        });
    }

    static getFlowsSelectOptions(publishedOnly = false) {
        const flows = (publishedOnly)
            ? Common.userData.flows.filter(experience => [EXPERIENCE_STATUSES.PUBLISHED, EXPERIENCE_STATUSES.PENDING_CHANGES].includes(experience.status))
            : Common.userData.flows;
        return flows.map(flow => {
            const attributeOptionElement = document.createElement("div");
            attributeOptionElement.textContent = flow.title;
            attributeOptionElement.setAttribute("value", flow.id);
            return attributeOptionElement;
        });
    }

    static getSurveysSelectOptions(publishedOnly = false) {
        const surveys = (publishedOnly)
            ? Common.userData.surveys.filter(survey => ["published", "pending_changes"].includes(survey.status))
            : Common.userData.surveys;
        return surveys.map(survey => {
            const attributeOptionElement = document.createElement("div");
            attributeOptionElement.textContent = survey.title;
            attributeOptionElement.setAttribute("value", survey.id);
            return attributeOptionElement;
        });
    }

    static getFontsSelectOptions() {
        return Common.userData.themes.fonts.map(font => {
            const fontOptionElement = document.createElement("div");
            fontOptionElement.textContent = font.family;
            fontOptionElement.setAttribute("value", font.family.toLowerCase());
            return fontOptionElement;
        });
    }

    static getStepView(type, edit_permissions) {
        const stepView = document.createElement("div");
        stepView.classList.add("userpilot-step");
        stepView.innerHTML = ExperienceBuilderIcons.getGreyPatternIcon(type)

        if(edit_permissions) stepView.setAttribute("draggable", true);
        return stepView;
    }

    static getLocalizationIcons() {
        return {
            closeIcon: ExperienceBuilderIcons.getCloseIcon(),
            automaticIcon: ExperienceBuilderIcons.getAutoLocalizationIcon(),
            manualIcon: ExperienceBuilderIcons.getManualLocalizationIcon(),
            addIcon: ExperienceBuilderIcons.getAddIcon()
        }
    }

    static getTemplatesThumbnailsVars() {
        const emptyContent = ExperienceBuilderViews.getView("saved-templates-empty-content", {
            title: "",
            message: "You haven’t saved any templates yet"
        });

        return {
            emptyContent: emptyContent,
            searchIcon: ExperienceBuilderIcons.getSearchIcon()
        }
    }

    static getBuilderLoadingVars() {
        return {
            builderLoadingAnimation: this.getView("builder-loading-animation")
        }
    }
}


// Static variables
String.prototype.formatUnicorn = String.prototype.formatUnicorn ||
    function () {
        "use strict";
        var str = this.toString();
        if (arguments.length) {
            var t = typeof arguments[0];
            var key;
            var args = ("string" === t || "number" === t) ?
                Array.prototype.slice.call(arguments) :
                arguments[0];

            for (key in args) {
                str = str.replace(new RegExp("\\{" + key + "\\}", "gi"), args[key]);
            }
        }
        return str;
    };