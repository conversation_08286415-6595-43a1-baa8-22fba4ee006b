// Before adding a new constant action, make sure it doesn't already exist in the list

const Actions = {
    /* APP */
    SAVE_EXPERIENCE: "SAVE_EXPERIENCE",
    SAVE_EXPERIENCE_SETTINGS: "SAVE_EXPERIENCE_SETTINGS",
    SAVE_EXPERIENCE_CONTENT: "SAVE_EXPERIENCE_CONTENT",
    SAVE_EXPERIENCE_THEME_ID: "SAVE_EXPERIENCE_THEME_ID",
    SAVE_EXPERIENCE_LOCALIZATION: "SAVE_EXPERIENCE_LOCALIZATION",
    WINDOW_LOCATION_CHANGE: "WINDOW_LOCATION_CHANGE",
    SELECT_NEW_EXPERIENCE_TYPE: "SELECT_NEW_EXPERIENCE_TYPE",
    C<PERSON>AR_WINDOW_COMPONENTS: "CLEAR_WINDOW_COMPONENTS",
    REMOVE_INLINE_PLACEHOLDER: "REMOVE_INLINE_PLACEHOLDER",
    HIDE_WINDOW_COMPONENTS: "HIDE_WINDOW_COMPONENTS",
    SHOW_WINDOW_COMPONENTS: "SHOW_WINDOW_COMPONENTS",
    SPAWN_EXPERIENCE_SETTINGS: "SPAWN_EXPERIENCE_SETTINGS",
    SPAWN_TEMPLATE_PICKER: "SPAWN_TEMPLATE_PICKER",
    SPAWN_TEMPLATE_EDITOR: "SPAWN_TEMPLATE_EDITOR",
    SPAWN_PAGE_CHANGE_SETTINGS: "SPAWN_PAGE_CHANGE_SETTINGS",
    PREVIEW_EXPERIENCE: "PREVIEW_EXPERIENCE",
    END_PREVIEW: "END_PREVIEW",
    END_PREVIEW_SDK: "END_PREVIEW_SDK",
    ENDED_PERVIEW_MSG: "ENDED_PERVIEW_MSG",
    REMOVE_ENDED_PREVIEW_MSG: "REMOVE_ENDED_PREVIEW_MSG",
    SHOW_TOOLTIP_HELP_MESSAGE: "SHOW_TOOLTIP_HELP_MESSAGE",
    HIDE_TOOLTIP_HELP_MESSAGE: "HIDE_TOOLTIP_HELP_MESSAGE",
    RESET_BUILD_URL: "RESET_BUILD_URL",
    SET_ACTIVE_CONTENT_INDEX: "SET_ACTIVE_CONTENT_INDEX",
    SHIFT_KEY_PRESSED: "SHIFT_KEY_PRESSED",
    RESET_CURRENT_LOCALIZATION: "RESET_CURRENT_LOCALIZATION",
    SPAWN_FEATURE_SETTINGS: "SPAWN_FEATURE_SETTINGS",
    CHECK_FOR_UNSAVED_CHANGES: "CHECK_FOR_UNSAVED_CHANGES",
    SESSION_ENDED: "SESSION_ENDED",

    /* MAIN BAR */
    SHOW_LOADING_SCREEN: "SHOW_LOADING_SCREEN",
    SHOW_BAR_MESSAGE: "SHOW_BAR_MESSAGE",
    HIDE_BAR_MESSAGE: "HIDE_BAR_MESSAGE",
    SHOW_MIGRATION_MESSAGE: "SHOW_MIGRATION_MESSAGE",
    HIDE_BAR: "HIDE_BAR",
    SHOW_BAR: "SHOW_BAR",
    SET_BAR_TO_INITIAL: "SET_BAR_TO_INITIAL",
    SETUP_NEW_EXPERIENCE: "SETUP_NEW_EXPERIENCE",
    INIT_NEW_EXPERIENCE: "INIT_NEW_EXPERIENCE",
    EDIT_EXPERIENCE: "EDIT_EXPERIENCE",
    SHOW_CURRENT_ACTIVE_EXPERIENCE: "SHOW_CURRENT_ACTIVE_EXPERIENCE",
    LOCK_BAR: "LOCK_BAR",
    UNLOCK_BAR: "UNLOCK_BAR",

    /* CONTENT LIST */
    SEARCH_CONTENT: "SEARCH_CONTENT",
    SORT_CONTENT: "SORT_CONTENT",
    FILTER_CONTENT: "FILTER_CONTENT",
    SET_ACTIVE_TITLE: "SET_ACTIVE_TITLE",
    UPDATE_CURRENT_OPENED_CONTENT: "UPDATE_CURRENT_OPENED_CONTENT",
    FILTER_FLOWS_FOLDERS: "FILTER_FLOWS_FOLDERS",
    FILTER_SPOTLIGHTS_FOLDERS: "FILTER_SPOTLIGHTS_FOLDERS",
    FILTER_BANNERS_FOLDERS: "FILTER_BANNERS_FOLDERS",

    /* NEW EXPERIENCE */
    NAVIGATE_TO_PAGE: "NAVIGATE_TO_PAGE",

    /* EXPERIENCE EDITOR */
    HIGHLIGHT_BAR_BTN: "HIGHLIGHT_BAR_BTN",
    UNHIGHLIGHT_BAR_BTN: "UNHIGHLIGHT_BAR_BTN",
    SHOW_FLOW_OVERFLOW_ARROWS: "SHOW_FLOW_OVERFLOW_ARROWS",
    SCROLL_TO_CURRENT_PAGE_CONTENT: "SCROLL_TO_CURRENT_PAGE_CONTENT",
    ACTIVATE_ELEMENT_SELECTION: "ACTIVATE_ELEMENT_SELECTION",

    /* GROUPS AND STEPS (FLOW)*/
    SAVE_FLOW_CONTENT: "SAVE_FLOW_CONTENT",
    SAVE_PAGE_CONTENT: "SAVE_PAGE_CONTENT",
    SAVE_STEP: "SAVE_STEP",
    AUTO_SAVE_STEP: "AUTO_SAVE_STEP",
    ADD_STEP: "ADD_STEP",
    ADD_STEP_AFTER_GROUP: "ADD_STEP_AFTER_GROUP",
    ADD_STEP_AFTER_STEP: "ADD_STEP_AFTER_STEP",
    PUSH_STEP_INTO: "PUSH_STEP_INTO",
    REMOVE_STEP: "REMOVE_STEP",
    REPLACE_STEP: "REPLACE_STEP",
    ADD_GROUP: "ADD_GROUP",
    REMOVE_GROUP: "REMOVE_GROUP",
    SPLIT_GROUP_AT_STEP: "SPLIT_GROUP_AT_STEP",
    SET_UNDER_EDIT_STEP: "SET_UNDER_EDIT_STEP",
    DELETE_PAGE_CHANGE: "DELETE_PAGE_CHANGE",
    SAVE_PAGE_CHANGE: "SAVE_PAGE_CHANGE",
    PREVIEW_STEP: "PREVIEW_STEP",
    UNPREVIEW_STEP: "UNPREVIEW_STEP",
    SET_FLOW_UNDER_DRAG: "SET_FLOW_UNDER_DRAG",
    HIDE_PAGE_CHANGE_SETTINGS: "HIDE_PAGE_CHANGE_SETTINGS",
    CLOSE_PAGE_CHANGE_SETTINGS: "CLOSE_PAGE_CHANGE_SETTINGS",
    SPAWN_LOCALIZATION_SETTINGS: "SPAWN_LOCALIZATION_SETTINGS",
    HIDE_LOCALIZATION_SETTINGS: "HIDE_LOCALIZATION_SETTINGS",
    SHOW_LOCALIZATION: "SHOW_LOCALIZATION",
    HIDE_LOCALIZATION: "HIDE_LOCALIZATION",
    SET_EDIT_BAR_STATUS: "SET_EDIT_BAR_STATUS",
    PREVENT_FLOW_POINTER_EVENTS: "PREVENT_FLOW_POINTER_EVENTS",
    RERENDER_FLOW: "RERENDER_FLOW",
    RESPONSIVE_MODE_ON: "RESPONSIVE_MODE_ON",
    RESPONSIVE_MODE_OFF: "RESPONSIVE_MODE_OFF",
    MOBILE_MODE_ON: "MOBILE_MODE_ON",
    MOBILE_MODE_OFF: "MOBILE_MODE_OFF",
    SHIFT_RESPONSIVE_TOOLBAR: "SHIFT_RESPONSIVE_TOOLBAR",
    UNSHIFT_RESPONSIVE_TOOLBAR: "UNSHIFT_RESPONSIVE_TOOLBAR",

    /* Empty State */
    SHOW_ADD_STEP_EMPTY_BUTTON: "SHOW_ADD_STEP_EMPTY_BUTTON",
    REMOVE_ADD_STEP_EMPTY_BUTTON: "REMOVE_ADD_STEP_EMPTY_BUTTON",

    /* LOCALIZATION ACTIONS */
    LOCALE_DELETE: "LOCALE_DELETE",
    LOCALE_CHANGE_DEFAULT: "LOCALE_CHANGE_DEFAULT",
    LOCALES_VIEW: "LOCALES_VIEW",
    SET_ACTIVE_LOCALE: "SET_ACTIVE_LOCALE",
    LOCALE_ENABLE: "LOCALE_ENABLE",
    LOCALE_DISABLE: "LOCALE_DISABLE",
    LOCALE_TRANSLATE: "LOCALE_TRANSLATE",
    LOCALE_ADD: "LOCALE_ADD",
    LOCALE_UPDATE: "LOCALE_UPDATE",
    SET_LOCALES_LIST: "SET_LOCALES_LIST",
    REFRESH_LOCALES_LIST: "REFRESH_LOCALES_LIST",
    TOGGLE_LOCALIZATION: "TOGGLE_LOCALIZATION",

    /* PAGECHANGE SUGGESTED */
    SUGGEST_PAGECHANGE_SETTINGS: "SUGGEST_PAGECHANGE_SETTINGS",

    /* PAGE SETTINGS SUGGESTED */
    SUGGEST_PAGE_SETTINGS: "SUGGEST_PAGE_SETTINGS",

    /* RESET SUGGESTED PAGE */
    RESET_SUGGESTED_PAGE: "RESET_SUGGESTED_PAGE",

    /* SPOTLIGHT */
    STEP_TYPE_CHANGED: "STEP_TYPE_CHANGED",

    /* FEATURE TAG */
    SPAWN_INTERACTION_TYPE_SIDEBAR: "SPAWN_INTERACTION_TYPE_SIDEBAR",
    HIDE_INTERACTION_TYPE_SIDEBAR: "HIDE_INTERACTION_TYPE_SIDEBAR",
    SPAWN_FEATURE_TAG_SETTINGS_SIDEBAR: "SPAWN_FEATURE_TAG_SETTINGS_SIDEBAR",
    HIDE_FEATURE_TAG_SETTINGS_SIDEBAR: "HIDE_FEATURE_TAG_SETTINGS_SIDEBAR",
    INIT_NEW_FEATURE_TAG: "INIT_NEW_FEATURE_TAG",
    SHOW_FEATURE_TAG_NOT_FOUND_MESSAGE: "SHOW_FEATURE_TAG_NOT_FOUND_MESSAGE",
    SHOW_FEATURE_TAG_TRIGGERED_MESSAGE: "SHOW_FEATURE_TAG_TRIGGERED_MESSAGE",
    INIT_FEATURE_TAG_DOMAIN_LIST: "INIT_FEATURE_TAG_DOMAIN_LIST",
    INIT_FEATURE_TAG_STAGING_DOMAIN_LIST: "INIT_FEATURE_TAG_STAGING_DOMAIN_LIST",
    INIT_FEATURE_TAG_PAGES_LIST: "INIT_FEATURE_TAG_PAGES_LIST",
    UPDATE_FEATURE_TAG_ELEMENT: "UPDATE_FEATURE_TAG_ELEMENT",
    UPDATE_FEATURE_TAG_TYPE: "UPDATE_FEATURE_TAG_TYPE",
    UPDATE_FEATURE_TAG_PATHS_CRITERIA: "UPDATE_FEATURE_TAG_PATHS_CRITERIA",
    UPDATE_FEATURE_TAG_DOMAIN_TYPE: "UPDATE_FEATURE_TAG_DOMAIN_TYPE",
    UPDATE_FEATURE_TAG_PAGE_TYPE: "UPDATE_FEATURE_TAG_PAGE_TYPE",
    SAVE_FEATURE_TAG_SETTINGS: "SAVE_FEATURE_TAG_SETTINGS",
    SYNC_FEATURE_TAG_CONFIGURATION: "SYNC_FEATURE_TAG_CONFIGURATION",
    SAVE_FEATURE_TAG_DOMAINS: "SAVE_FEATURE_TAG_DOMAINS",
    SAVE_FEATURE_TAG_PAGES: "SAVE_FEATURE_TAG_PAGES",
    SAVE_FEATURE_TAG: "SAVE_FEATURE_TAG",
    CREATE_NEW_FEATURE_TAG: "CREATE_NEW_FEATURE_TAG",
    FEATURE_TAG_DISCARD_CHANGES: "FEATURE_TAG_DISCARD_CHANGES",
    CLOSE_FEATURE_TAG_SETTINGS_SIDEBAR: "CLOSE_FEATURE_TAG_SETTINGS_SIDEBAR",
    CLOSE_FEATURE_TAG_HEATMAP: "CLOSE_FEATURE_TAG_HEATMAP",
    UPDATE_FEATURE_TAG_HEATMAP_TARGETS: "UPDATE_FEATURE_TAG_HEATMAP_TARGETS",
    UPDATE_FEATURE_TAG_ENVIRONMENT: "UPDATE_FEATURE_TAG_ENVIRONMENT",

    /* ENGAGEMENT LAYER */
    EMGAGEMENT_MOVE_TO_NEXT_STEP: "EMGAGEMENT_MOVE_TO_NEXT_STEP",
    EMGAGEMENT_MARK_FLOW_AS_COMPLETE: "EMGAGEMENT_MARK_FLOW_AS_COMPLETE",
    SHOW_ENGAGEMENTS: "SHOW_ENGAGEMENTS",
    ENGAGEMENT_MARK_SPOTLIGHT_AS_COMPLETE: "ENGAGEMENT_MARK_SPOTLIGHT_AS_COMPLETE",
    ENGAGEMENT_TRIGGER_SPOTLIGHT: "ENGAGEMENT_TRIGGER_SPOTLIGHT",
    SHOW_DISCARD_CHANGES_BUTTON: "SHOW_DISCARD_CHANGES_BUTTON",

    /* LABELED EVENT */
    HIDE_LABELED_EVENT_SETTINGS_SIDEBAR: "HIDE_LABELED_EVENT_SETTINGS_SIDEBAR",
    CREATE_NEW_LABELED_EVENT: "CREATE_NEW_LABELED_EVENT",
    CREATE_NEW_LABELED_EVENT_FROM_RAW_EVENT: "CREATE_NEW_LABELED_EVENT_FROM_RAW_EVENT",
    INIT_NEW_LABELED_EVENT: "INIT_NEW_LABELED_EVENT",
    SPAWN_LABELED_EVENT_SETTINGS: "SPAWN_LABELED_EVENT_SETTINGS",
    SPAWN_LABELED_EVENTS_INTERACTION_TYPE_SIDEBAR: "SPAWN_LABELED_EVENTS_INTERACTION_TYPE_SIDEBAR",
    UPDATE_LABELED_EVENT_INTERACTION_TYPE: "UPDATE_LABELED_EVENT_INTERACTION_TYPE",
    UPDATE_LABELED_EVENT_DOMAIN_TYPE: "UPDATE_LABELED_EVENT_DOMAIN_TYPE",
    UPDATE_LABELED_EVENT_PAGE_TYPE: "UPDATE_LABELED_EVENT_PAGE_TYPE",
    UPDATE_LABELED_EVENT_PAGE_CRITERIA: "UPDATE_LABELED_EVENT_PAGE_CRITERIA",
    UPDATE_LABELED_EVENT_TAG_ELEMENT: "UPDATE_LABELED_EVENT_TAG_ELEMENT",
    SAVE_LABELED_EVENT_DOMAINS: "SAVE_LABELED_EVENT_DOMAINS",
    SAVE_LABELED_EVENT_PAGES: "SAVE_LABELED_EVENT_PAGES",
    SHOW_LABELED_EVENT_TRIGGERED_MESSAGE: "SHOW_LABELED_EVENT_TRIGGERED_MESSAGE",
    SHOW_LABELED_EVENT_NOT_FOUND_MESSAGE: "SHOW_LABELED_EVENT_NOT_FOUND_MESSAGE",
    SYNC_LABELED_EVENT_DATA: "SYNC_LABELED_EVENT_DATA",
    LABELED_EVENT_DISCARD_CHANGES: "LABELED_EVENT_DISCARD_CHANGES",

}

export default Actions;