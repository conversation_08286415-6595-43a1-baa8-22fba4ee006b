import Common from "../index.js";

import Actions from "./constants/actions.js";

// Utils
import JsonOperations from "../generic-utils/jsonOperations.js";

const reducers = new Map();
const actionsReducers = {};
const publicStates = {};

let cleanupTimeout;

const registerComponent = (component, element, state, actions, reducer, reducerEffect, exposeState = false) => {
    actions.some(action => {
        return (Actions[action]) ? false : console.error("action: " + action + " is not defined in src/action.js");
    });

    reducers.set(reducer, {
        component: component,
        element: element,
        state: state,
        reducerEffect: reducerEffect
    });

    actions.forEach(action => {
        actionsReducers[action] = new Set((actionsReducers[action])).add(reducer);
    });

    const stateProxy = new Proxy(reducers.get(reducer), {
        get: (target, prop) => {
            return target.state[prop];
        }
    });

    if (exposeState) publicStates[component] = stateProxy;

    return stateProxy;
}

const cleanupComponents = () => {
    clearTimeout(cleanupTimeout);
    cleanupTimeout = setTimeout(() => {
        reducers.forEach((reducer, key) => {
            if (!Common.shadowRoot.contains(reducer.element)) {
                reducers.delete(key);
            }
        });
    }, 1000);
}

const publishAction = async (action, props = {}) => {
    if (!(actionsReducers[action] instanceof Set)) {
        console.warn("No reducers available for: " + action);
        return;
    }

    for await (const reducer of actionsReducers[action]) {
        const component = reducers.get(reducer);

        if (!component) {
            actionsReducers[action].delete(reducer);
            continue;
        }

        component.state = Object.freeze({
            ...component.state,
            ...reducer(action, props)
        });

        await component.reducerEffect(action, props);
    }

    cleanupComponents();
}

const getComponentState = (component) => {
    if (!publicStates[component])
        console.error("DevError: Component state not exposed. Pass bool exposeState as true when registering a component to expose its state");
    return publicStates[component];
}

export {
    registerComponent,
    publishAction,
    getComponentState
}

export * as Store from "./store.js";