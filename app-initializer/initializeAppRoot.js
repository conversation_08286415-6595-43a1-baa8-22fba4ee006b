import tippy from "tippy.js";

import Common from "../index.js";
import InitializeCSS from "./initializeCssFiles.js";
import InitializeHtmlTemplates from "./initializeHtmlTemplates.js";
import ExperienceBuilderViews from "../views/experienceBuilderViews.js";
import { disableIframesPointerEvents, initShiftListener } from "../generic-utils/events.js";

const InitializeApp = async () => {
    const root = document.getElementById('userpilot-root');
    // prevent reinitializing from background.js, bug happens because chrome considers tab navigation in SPA as a page reload.
    if (root.shadowRoot) {
        return false;
    }
    root.attachShadow({
        mode: "open"
    });
    const shadowRoot = root.shadowRoot;

    // Append all required templates
    await Promise.all([
        InitializeCSS(shadowRoot),
        InitializeHtmlTemplates(shadowRoot)
    ]);

    // Prevent events dispatched from the Chrome Extension elements from bubbling up to the user app
    initShiftListener();
    preventEventBubbling(shadowRoot);
    disableIframesPointerEvents(shadowRoot);

    // Append needed elements to the user app document
    setTimeout(() => {
        appendDocumentElements();
    }, 100);

    initLocalStorage();
    initSessionStorage();
    initTippyjs();
    initPort();

    return shadowRoot;
}

const preventEventBubbling = (shadowRoot) => {
    const bubbleEvents = ["click", "dblclick", "mousedown", "mouseup", "dragstart", "dragenter", "dragleave", "dragover",
        "drop", "dragend", "keydown", "keypress", "keyup", "select", "change", "submit", "focusin",
    ];

    bubbleEvents.forEach(eventName => {
        shadowRoot.addEventListener(eventName, (event) => {
            event.stopPropagation();
        });
    });
}

const appendDocumentElements = () => {
    const userpilotDocumentStyles = ExperienceBuilderViews.getViewElement("userpilot-chrome-extension-css");
    document.body.parentElement.appendChild(userpilotDocumentStyles);

    const elementHighlighter = ExperienceBuilderViews.getViewElement("userpilot-hovered-mask");
    document.body.parentElement.appendChild(elementHighlighter);

    const shadowRootContainer = Common.shadowRoot.querySelector(".userpilot");
    shadowRootContainer.appendChild(
        ExperienceBuilderViews.getViewElement("userpilot-chrome-extension-css")
    );

    shadowRootContainer.appendChild(
        ExperienceBuilderViews.getViewElement("userpilot-hovered-mask")
    );
}

const initLocalStorage = () => {
    const isDarkThemeEnabled = localStorage.getItem("userpilot-isDarkThemeEnabled");
    if(isDarkThemeEnabled) return;

    localStorage.setItem("userpilot-isDarkThemeEnabled", true);
}

const initSessionStorage = () => {
    sessionStorage.setItem("userpilotChromeBuilding", "true");
}

const initTippyjs = () => {
    tippy.setDefaultProps({
        zIndex: "inherit",
        delay: [200, 0],
        theme: "userpilot",
        content: (el) => {
            const label = el.getAttribute('label-on-hover');
            el.removeAttribute('label-on-hover');
            return label;
        },
        onTrigger: (instance) => {
            if(Common.isDarkThemeApplied()) {
                instance.popper.classList.remove("theme-light");
                instance.popper.classList.add("theme-dark");
            } else {
                instance.popper.classList.remove("theme-dark");
                instance.popper.classList.add("theme-light");
            }
        },
    });
}

const initPort = () => {
    const port = chrome.runtime.connect({ name: "Userpilot" });
    port.onDisconnect.addListener(() => Common.exitChromeExtension());
}

export default InitializeApp;